# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TIS DLPF Application is a multi-component enterprise system consisting of:

- **Java Spring Boot Web Application** (`webapp/`): RESTful API service with Spring Boot 3.2.1, MyBatis, PostgreSQL/H2
- **AWS Glue Jobs** (`job/`): Python 3.9 data processing pipeline with ETL operations
- **AWS Infrastructure** (`cloudformation/`): CloudFormation templates for AWS resource deployment
- **CI/CD Pipeline** (`cicd/`): Automated deployment scripts and build configurations

## Development Commands

### Java Web Application (webapp/)

```bash
# Build and run Spring Boot application
cd webapp
./mvnw spring-boot:run

# Run tests with coverage (JaCoCo)
./mvnw test

# Build application
./mvnw clean package

# Test coverage check (50% minimum required)
./mvnw jacoco:check
```

### Python Glue Jobs (job/)

```bash
# Setup Python 3.9 environment
cd job
uv venv .venv
source .venv/bin/activate

# Install dependencies (including dev tools: black, pylint, pytest)
uv sync --all-groups --reinstall

# Run unit tests with coverage
pytest --cov --cov-config=.coveragerc

# Format code with black
black source/ test/

# Run linting with pylint
pylint source/

# Build and deploy AWS Glue packages
./build_and_deploy_dev.sh  # For development environment
./build_and_deploy_stg.sh  # For staging environment
```

### Infrastructure Deployment

```bash
# Deploy CloudFormation templates
./cloudformation/scripts/cfn_deploy.sh -e dev -n -y <resource_type> <resource_name>

# Deploy to staging
./cloudformation/scripts/cfn_deploy.sh -e stg -n -y <resource_type> <resource_name>
```

## Architecture

### Java Application (webapp/)

- **Spring Boot 3.2.1** with Java 17
- **MyBatis** for database ORM with XML mappers
- **PostgreSQL** (production) / **H2** (development) databases
- **Spring Security** for authentication
- **Log4j2** for logging
- **Controllers**: REST endpoints for customers, orders, subscriptions, health checks
- **Services**: Business logic layer
- **Mappers**: MyBatis database access layer
- **DTOs**: Data transfer objects for API requests/responses

### Python ETL Jobs (job/)

- **AWS Glue Jobs**: Data transformation and ETL processing
- **Data Converters**: Format conversion (CSV/TSV, XML, fixed-width, database)
- **Database Connectors**: PostgreSQL integration with SQLAlchemy
- **File Processors**: S3 file operations, SFTP transfers, compression
- **Configuration-driven**: YAML-based ETL configurations in `source/config/converter/`

### AWS Infrastructure (cloudformation/)

- **Step Functions**: Orchestrate data processing workflows
- **Glue Jobs**: ETL job definitions
- **Lambda Functions**: Utility functions for file checks and array operations
- **Secrets Manager**: Secure credential storage
- **Event Bridge**: Event-driven architecture
- **Security Groups**: Network access control

## Key Configuration Files

- `webapp/pom.xml`: Maven dependencies and build configuration
- `job/pyproject.toml`: Python dependencies and project metadata
- `job/uv.lock`: Locked dependency versions
- `cloudformation/environments/`: Environment-specific parameters (dev/stg/prd)

## Testing Guidelines

### Java Tests

- Unit tests in `webapp/src/test/java/`
- Integration tests for controllers and services
- Minimum 50% code coverage enforced by JaCoCo

### Python Tests

- Unit tests in `job/test/`
- Comprehensive test coverage for ETL components
- Mock AWS services using moto library

## Branch Strategy

- **develop**: Development environment deployments
- **release**: Staging environment deployments
- **master**: Production deployments

## Environment-Specific Notes

- **Development**: Uses account `************`
- **Staging**: Uses account `************`
- **Production**: Uses account `************`
- Always verify branch alignment with target environment before deployment

## Business Workflows and Rules

### 🚀 Automated Deployment Workflow

- **Time-based Management**: Deploys PRs based on merge timestamps (solves PR overtaking issues)
- **Change Type Detection**: Automatically detects Glue/CloudFormation/Lambda changes
- **Scheduled Execution**: Hourly auto-deployment (9-19h) for development environment
- **5-minute Buffer**: Handles GitHub API delays with deploy deadline time
- **Environment-specific Branches**: dev→develop, stg→release, prd→master

### 📋 Manual Deployment Procedures

- **Unified Script**: Use `./cloudformation/scripts/cfn_deploy.sh` for all manual deployments
- **Group Deploy Mode**: Deploy all templates of specific resource type at once
- **Parameter Precedence**: Resource-specific → Type-specific → Environment-common
- **Validation Required**: Always run `-v` (validate) and `-d` (dry-run) before actual deployment

### 🔧 Development Workflow Rules

- **❌ Direct develop commits prohibited** - Always create PR from feature branch
- **✅ Squash merge required** - Clean commit history with branch cleanup
- **✅ Self-review mandatory** - Review all changes before merge
- **Cherry-pick Management**: Use Universal Issue Commits Tool for release management

### 👥 Approval & Responsibility Matrix

| Environment     | Approver                                | Deployment Method                | Notification |
| --------------- | --------------------------------------- | -------------------------------- | ------------ |
| **Development** | Development Team                        | Automated (hourly)               | SNS enabled  |
| **Staging**     | Team Leaders (Ninomiya-san, Harada-san) | Manual with approval             | SNS disabled |
| **Production**  | Technical + Operations Managers         | Manual with multi-stage approval | SNS disabled |

### 🛡️ Security Requirements

- **GitHub App Authentication**: Managed in AWS Secrets Manager (`github-app-credentials`)
- **VPC Execution**: CodeBuild runs in private subnets with fixed IP
- **IAM Principle**: Minimum required permissions only
- **MFA Automation**: `aws-mfa-auto.sh` for environment switching

### 📊 Quality Assurance Process

- **8-Stage Quality Gates**: Syntax → Type → Lint → Security → Test → Performance → Documentation → Integration
- **DRY-RUN Mandatory**: All production deployments must be tested in dry-run mode first
- **Parameter Store Protection**: Automatic updates only, manual editing prohibited
- **Test Suite**: 8 mandatory test phases for different components

### 🚨 Error Handling & Emergency Response

- **Immediate Halt**: `set -e` usage for immediate error stopping
- **Parameter Protection**: Never reset Parameter Store values to prevent data loss
- **Rollback Priority**: `continue-update-rollback` preferred over deletion
- **24/7 Emergency Contact**: TIS Huang (<EMAIL>)

### 🔄 Data Processing Rules

- **Delete Flag Implementation**: Unified `delete_flg` column (0=active, 1=deleted)
- **View Tables**: Use `*_view` tables for parallel operation during migration
- **XML Generation**: DB2XML conversion with hierarchical query structure
- **Campaign Data**: Time-based extraction with promotion condition filtering

### 📂 File Management Constraints

- **❌ Prohibited Files**: Augment conversation records (`タスク進捗サマリー_*.md`, etc.)
- **Language Rules**: Comments (English), Commit messages/PR/Documentation (Japanese)
- **Document Updates**: ToDoList.md, issues-list.md, deployment-system.md, operation-guide.md

### 🔧 Infrastructure Management

- **IAM Role Migration**: Infrastructure team (Takizawa-san) creates roles → App team updates templates
- **CodeConnections**: Unified authentication for repository access
- **Resource Naming**: `cdp-{env}-dlpf-*` pattern for all AWS resources
- **CloudFormation Only**: No manual AWS resource changes, template-driven only

### 📅 Operational Workflows

- **Daily**: CodeBuild log monitoring, error notification checks
- **Weekly**: ToDoList progress updates, test execution status review
- **Monthly**: AWS resource usage review, security configuration audit
- **Release**: Cherry-pick workflow with conflict resolution strategies

## Important Information Sources

### Core Documentation Locations

- **Deployment Rules**: `cicd/docs/deployment-rules.md` (343 lines of detailed rules)
- **Operation Guide**: `cicd/docs/operation-guide.md` (623 lines of comprehensive procedures)
- **System Design**: `cicd/docs/deployment-system.md` (631 lines of architecture)
- **Manual Procedures**: `cloudformation/docs/CloudFormation手動デプロイ手順書.md` (266 lines)

### Environment Setup Procedures

- **Production**: `cicd/docs/production-cicd-deployment-procedure.md` (459 lines)
- **Staging**: `cicd/docs/staging-cicd-system-deployment-procedure.md` (263 lines)
- **Development**: Automated deployment via EventBridge Scheduler

### Issue & Task Management

- **Active Issues**: `cicd/docs/issues-list.md`
- **Task Tracking**: `cicd/docs/ToDoList.md`
- **Completed Projects**: `CHANGE-388/削除フラグ対応_作業計画書.md` (29 files modified)

### Technical References

- **DB2XML Guide**: `job/docs/DB2XML_YAML作成ガイド.md`
- **Interface Rules**: `redmine-8254/IF設計書調整ルール.md`
- **Campaign Definitions**: `job/docs/XML_キャンペーン・プロモーション_IF定義.md`

## Dev Tools

- **gh cli ツール利用できること**

## Commit and PR Guidelines

### Commit Message Rules

**Format Patterns** (based on project history):
1. **Issue/Redmine + Description**: `8423_pr001_商品ヘッダと明細の基準日時をそろえる`
2. **English Functional**: `add authority for mdm to api user`
3. **Redmine Format**: `Redmine 7295 : 本番環境ブランチ参照修正 + CI/CD削除ファイルデプロイ失敗問題修正`

### PR Creation Standards

**Title Formats**:
- **Redmine**: `Redmine [番号] : [変更内容（日本語で明確に）]`
- **Issue**: `[Issue番号]_[変更内容（日本語）]`
- **Simple**: `[機能説明（英語/日本語）]`

**Standard PR Template**:
```markdown
## 📋 概要
[変更の目的・背景を簡潔に説明]

## 🎯 変更内容
### **[カテゴリ1]**
- **[項目]**: [詳細説明]

## 🔄 対応背景
### **[背景説明]**
- **[段階]**: [説明]

## 📁 変更ファイル
- `[ファイルパス]`

## ✅ 確認事項
- [x] [確認項目]

## 🚀 次のステップ
1. **[ステップ]**: [説明]

関連: Redmine #[番号] / Issue #[番号]
```

### Language and Quality Rules

- **Code Comments**: English (UTF-8 error avoidance)
- **Commit Messages**: Japanese
- **PR Content**: Japanese with structured markdown
- **Documentation**: Japanese (align with existing standards)

### Required Workflow

1. **Branch Creation**: Always create PR from feature branch
2. **Self Review**: Mandatory review before merge
3. **Squash Merge**: Required for clean history
4. **Auto Labels**: `cicd-processed` automatically added
5. **Reviewers**: Assign appropriate team members

### GitHub CLI Usage

```bash
# Create PR with template
gh pr create --title "[タイトル]" --body-file pr_description.md --assignee "@me"

# View PR details  
gh pr view [番号] --repo TIS-DSDev/tis-dlpf-app

# List recent PRs
gh pr list --repo TIS-DSDev/tis-dlpf-app --state merged --limit 10
```
