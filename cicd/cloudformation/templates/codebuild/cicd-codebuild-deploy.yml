AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for creating a CodeBuild project for simplified deployment

Parameters:
  VpcId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID for the CodeBuild project
  SubnetIds:
    Type: List<AWS::EC2::Subnet::Id>
    Description: Subnet IDs for the CodeBuild project (comma-separated list)
  SecurityGroupIds:
    Type: List<AWS::EC2::SecurityGroup::Id>
    Description: Security Group IDs for the CodeBuild project (comma-separated list)
  GitHubAppSecretName:
    Type: String
    Description: Secrets Manager secret name or ARN containing GitHub App credentials (app_id, installation_id, private_key)
  GitHubOwner:
    Type: String
    Description: GitHub repository owner (organization or user)
    Default: "TIS-DSDev"
  GitHubRepo:
    Type: String
    Description: GitHub repository name
    Default: "tis-dlpf-app"
  Environment:
    Type: String
    Description: Environment name (dev, stg, prd)
    Default: "dev"
  CodeConnectionsArn:
    Type: String
    Description: ARN of the CodeConnections resource for GitHub integration

  # Note: NotificationEmail parameter is now managed in the system-manager/parameter-store.yaml template

Conditions:
  IsDevelopment: !Equals [!Ref Environment, "dev"]
  IsStaging: !Equals [!Ref Environment, "stg"]

Resources:
  # Note: SSM Parameter for tracking last deployed PR is now managed in a separate template
  # cicd/cloudformation/templates/system-manager/parameter-store.yaml

  # Note: SNS Topic for error notifications is now managed in a separate template
  # cicd/cloudformation/templates/sns/notification-topic.yaml

  # Note: IAM Role for CodeBuild is now managed by infrastructure team
  # Role name: role-{Environment}-dlpf-cdp-appdeploy

  DeployCodeBuildProject:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: !Sub "cdp-${Environment}-dlpf-deploy"
      Description: !Sub "CodeBuild project for simplified deployment of PR changes to ${Environment} environment"
      ServiceRole: !Sub "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/role-${Environment}-dlpf-cdp-appdeploy"
      # 環境に応じたブランチを使用
      SourceVersion:
        Fn::If:
          - IsDevelopment
          - "develop"
          - Fn::If:
              - IsStaging
              - "release"
              - "master"
      Artifacts:
        Type: NO_ARTIFACTS
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_MEDIUM
        Image: aws/codebuild/standard:6.0
        PrivilegedMode: true
        EnvironmentVariables:
          - Name: GITHUB_APP_SECRET_NAME
            Type: PLAINTEXT
            Value: !Ref GitHubAppSecretName
          - Name: GITHUB_OWNER
            Type: PLAINTEXT
            Value: !Ref GitHubOwner
          - Name: GITHUB_REPO
            Type: PLAINTEXT
            Value: !Ref GitHubRepo
          - Name: ENVIRONMENT
            Type: PLAINTEXT
            Value: !Ref Environment
          - Name: deploy_baseline_time
            Type: PLAINTEXT
            Value: !Sub "/dlpf/${Environment}/deploy-baseline-time"
          - Name: BRANCH_NAME
            Type: PLAINTEXT
            Value:
              Fn::If:
                - IsDevelopment
                - "develop"
                - Fn::If:
                    - IsStaging
                    - "release"
                    - "master"
          - Name: DRY_RUN
            Type: PLAINTEXT
            Value: "false" # Default: perform actual deployment
          - Name: DEBUG_MODE
            Type: PLAINTEXT
            Value: "false" # Default: disable detailed logging (reduce log volume in production)
          - Name: SNS_NOTIFICATION_ENABLED
            Type: PLAINTEXT
            Value:
              Fn::If:
                - IsDevelopment
                - "true" # 開発環境のみ有効
                - "false" # その他の環境では無効
          - Name: SNS_TOPIC_ARN
            Type: PLAINTEXT
            Value: "" # SNS topic ARN is managed by deployment scripts, not CloudFormation

      Source:
        Type: GITHUB
        Location: !Sub "https://github.com/${GitHubOwner}/${GitHubRepo}.git"
        BuildSpec: cicd/buildspec/deploy-buildspec.yml
        GitCloneDepth: 0 # FullCloneを有効化
        Auth:
          Type: CODECONNECTIONS
          Resource: !Ref CodeConnectionsArn
      VpcConfig:
        VpcId: !Ref VpcId
        Subnets: !Ref SubnetIds
        SecurityGroupIds: !Ref SecurityGroupIds
      TimeoutInMinutes: 60
      QueuedTimeoutInMinutes: 60
      LogsConfig:
        CloudWatchLogs:
          Status: ENABLED
          GroupName: !Sub "/aws/codebuild/${AWS::StackName}"
          StreamName: "build-log"
      Cache:
        Type: LOCAL
        Modes:
          - LOCAL_DOCKER_LAYER_CACHE
          - LOCAL_SOURCE_CACHE
          - LOCAL_CUSTOM_CACHE
      ConcurrentBuildLimit: 1 # Limit concurrent builds to 1 (prevent multiple simultaneous executions)

Outputs:
  CodeBuildDeployProjectName:
    Description: The name of the CodeBuild deploy project
    Value: !Ref DeployCodeBuildProject
  CodeBuildDeployProjectArn:
    Description: The ARN of the CodeBuild deploy project
    Value: !GetAtt DeployCodeBuildProject.Arn
  DeployBaselineTimeParameterName:
    Description: The name of the SSM parameter tracking the deploy baseline time
    Value: !Sub "/dlpf/${Environment}/deploy-baseline-time"
  # Note: ErrorNotificationTopicArn and NotificationEmailParameterName are now exported from their respective templates
  # cicd/cloudformation/templates/sns/notification-topic.yaml
  # cicd/cloudformation/templates/system-manager/parameter-store.yaml
