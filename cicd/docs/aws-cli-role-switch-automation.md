# AWS CLI ロールスイッチ自動化設定

## 📋 概要

本番環境での AWS CLI ロールスイッチ処理を自動化し、書き込み操作時に手動切り替えを不要にする設定方法です。

## 🔍 現状の問題

### **現在の認証フロー**

1. **基本ユーザー**: `group-prd-dlpf-appdev/<EMAIL>` (READ ONLY)
2. **書き込み用ロール**: `arn:aws:iam::879381279381:user/group-prd-dlpf-infradev` (要手動切り替え)
3. **MFA 認証**: `aws-mfa-auto.sh`で自動化済み

### **問題点**

- 書き込み操作時に手動でロールスイッチが必要
- 暗黙的な書き込み権限への自動切り替えができていない

## 🎯 解決案: AWS_PROFILE 環境変数による自動ロールスイッチ

### **1. config 設定**

```ini
# ~/.aws.prd/config
[default]
region = ap-northeast-1
output = json

[profile appdev]
role_arn = arn:aws:iam::879381279381:role/role-prd-dlpf-appdev
source_profile = default
mfa_serial = arn:aws:iam::879381279381:mfa/o.iketsuo.iketsu
```

### **2. 使用方法**

#### **デフォルトで書き込み権限を使用する場合**

```bash
# appdevロールをデフォルトに設定
export AWS_PROFILE=appdev

# 書き込み操作（自動ロールスイッチ）
aws cloudformation deploy --template-file template.yml --stack-name test-stack
aws s3 cp file.txt s3://bucket/
```

#### **読み取り専用操作が必要な場合**

```bash
# デフォルトプロファイル（読み取り専用）を使用
unset AWS_PROFILE
aws s3 ls
aws cloudformation describe-stacks
```

#### **プロファイル切り替え**

```bash
# 書き込み権限に切り替え
export AWS_PROFILE=appdev

# デフォルト（読み取り専用）に戻す
unset AWS_PROFILE
```

## 🔧 **実装手順**

### **Step 1: config 設定の更新**

```bash
# 本番環境に切り替え
rm ~/.aws && ln -s ~/.aws.prd ~/.aws

# config設定を手動編集
vim ~/.aws/config
# または
nano ~/.aws/config
```

**設定内容**:

```ini
[default]
region = ap-northeast-1
output = json

[profile appdev]
role_arn = arn:aws:iam::879381279381:role/role-prd-dlpf-appdev
source_profile = default
mfa_serial = arn:aws:iam::879381279381:mfa/o.iketsuo.iketsu
```

### **Step 2: MFA 認証**

```bash
# MFA認証実行
~/.aws/aws-mfa-auto.sh
```

### **Step 3: プロファイル設定と動作確認**

```bash
# デフォルトで書き込み権限を使用
export AWS_PROFILE=infradev

# 認証状況確認
aws sts get-caller-identity

# CloudFormationデプロイテスト
aws cloudformation describe-stacks
```

## ⚠️ **注意事項**

### **セキュリティ考慮事項**

- MFA トークンの有効期限（通常 12 時間）
- ロールセッションの有効期限（通常 1 時間）
- 一時的なクレデンシャルの適切な管理

### **運用上の注意**

- 定期的な MFA 再認証が必要
- ロールの権限範囲を事前確認
- エラー時の手動切り替え手順の準備

## 🚀 **推奨運用フロー**

### **日次作業開始時**

```bash
# 1. 本番環境に切り替え
rm ~/.aws && ln -s ~/.aws.prd ~/.aws

# 2. MFA認証実行
~/.aws/aws-mfa-auto.sh

# 3. 書き込み権限をデフォルトに設定
export AWS_PROFILE=appdev

# 4. 動作確認
aws sts get-caller-identity
```

### **CloudFormation デプロイ時**

```bash
# AWS_PROFILE=appdevが設定済みなら、そのまま実行
aws cloudformation deploy --template-file cicd-codebuild-deploy.yml --stack-name cicd-codebuild-deploy --parameter-overrides file://parameters.json
```

### **読み取り専用操作時**

```bash
# デフォルトプロファイル（読み取り専用）を使用
unset AWS_PROFILE
aws s3 ls
aws cloudformation describe-stacks
```

## 📞 **トラブルシューティング**

### **よくあるエラー**

1. **MFA 認証エラー**: `~/.aws/aws-mfa-auto.sh`を再実行
2. **ロール切り替えエラー**: 権限確認、MFA 有効期限確認
3. **セッション期限切れ**: 再認証が必要

### **緊急時の手動切り替え**

```bash
# 手動でロール切り替え
aws sts assume-role --role-arn arn:aws:iam::879381279381:role/group-prd-dlpf-infradev-role --role-session-name emergency-session
```
