# デプロイシステム設計書

## 1. 概要

本ドキュメントは、当システムで採用しているデプロイシステムの設計について説明します。本システムは、PRマージイベントの履歴をGitHub APIから取得し、前回デプロイ実施時刻以降にマージされた未デプロイPRの変更を一括でデプロイする機能を提供します。

## 2. 設計の背景と目的

### 2.1 GitHub Actions方式利用時の課題

以前採用していたGitHub Actionsベースの自動デプロイシステムには以下の課題がありました：

1. **複雑な構成**:
   - GitHub Actionsワークフロー
   - self-hostedランナー（CodeBuild）
   - GitHub Actions変数によるキュー管理
   - デプロイ用CodeBuildプロジェクト

2. **ワークフローキューの制限**:
   - GitHub Actionsのワークフローキューには制限があり、長期間デプロイしない場合に問題が発生する可能性がありました

3. **リソース効率**:
   - ランナーが不要な時間も起動している状態
   - 複数のCodeBuildプロジェクトによるコスト増加

### 2.2 現行設計の目的

1. **シンプル化**:
   - GitHub Actionsワークフローとself-hostedランナーを使用せず
   - 単一のCodeBuildプロジェクトでデプロイを実行

2. **信頼性向上**:
   - GitHub CLI (gh コマンド) を使用してPRマージ履歴を取得
   - AWS Systems Manager Parameter Storeでデプロイ基準時刻を管理

3. **柔軟性向上**:
   - デプロイ基準時刻（deploy_baseline_time）を指定可能
   - 特定の時刻以降の変更のみをデプロイするなどの柔軟な運用が可能

## 3. システムアーキテクチャ

### 3.1 コンポーネント構成

本システムは以下のコンポーネントで構成されています：

1. **CodeBuildプロジェクト**:
   - 環境ごとに1つのプロジェクトを配置（開発環境: `cdp-dev-dlpf-deploy`, 検証環境: `cdp-stg-dlpf-deploy`）
   - デプロイ処理を実行します

2. **Parameter Store**:
   - 前回デプロイ実施時刻を保存します
   - 環境ごとに別々のパラメータを使用（`/dlpf/dev/deploy-baseline-time`, `/dlpf/stg/deploy-baseline-time`）
   - 開発環境では通知用メールアドレスも保存（`/dlpf/dev/notification-email`）

3. **SNSトピック**:
   - 開発環境のみ、デプロイエラー通知用のSNSトピックを使用（`cdp-dev-dlpf-deploy-error-notification`）
   - エラー発生時に登録されたメールアドレスに通知を送信します

4. **デプロイスクリプト**:
   - CodeBuildプロジェクトを手動で起動するためのスクリプトです
   - デプロイ基準時刻（deploy_baseline_time）を指定可能です

### 3.2 データフロー

```mermaid
sequenceDiagram
    actor User
    participant Console as AWSコンソール
    participant Script as デプロイスクリプト
    participant CodeBuild as CodeBuildプロジェクト
    participant GitHub as GitHub CLI
    participant SSM as Parameter Store
    participant Manual as 既存手動デプロイ
    participant SNS as SNSトピック

    Note over User, SNS: 主要起動方式：AWSコンソール
    User->>Console: CodeBuildプロジェクト手動起動
    Console->>CodeBuild: ビルド開始

    Note over User, SNS: 代替起動方式：デプロイスクリプト
    User->>Script: デプロイ実行（deploy_baseline_timeを指定可能）
    Script->>CodeBuild: プロジェクト起動

    Note over CodeBuild, Manual: 既存手動デプロイシステムを活用
    CodeBuild->>SSM: 前回デプロイ基準時刻取得
    CodeBuild->>GitHub: PRマージ履歴取得（gh CLI）
    CodeBuild->>CodeBuild: デプロイ対象PR特定（時刻ベース）
    CodeBuild->>Manual: 既存デプロイ処理実行
    CodeBuild->>GitHub: PRにコメント追加（gh CLI）
    CodeBuild->>SSM: デプロイ基準時刻更新
    CodeBuild-->>User: 完了通知

    alt エラー発生時（開発環境のみ）
        CodeBuild->>SNS: エラー通知送信
        SNS-->>User: メール通知
    end
```

## 4. 主要コンポーネントの詳細

### 4.1 CodeBuildプロジェクト

#### 4.1.1 環境変数

| 変数名 | 説明 | デフォルト値 |
|-------|------|------------|
| GITHUB_APP_SECRET_NAME | GitHub App認証情報のSecrets Manager名 | 環境ごとに設定 |
| GITHUB_OWNER | GitHubリポジトリのオーナー | "TIS-DSDev" |
| GITHUB_REPO | GitHubリポジトリ名 | "tis-dlpf-app" |
| ENVIRONMENT | デプロイ環境（dev/stg/prd） | 環境ごとに設定 |
| deploy_baseline_time | この時刻以降にマージされたPRを処理 | Parameter Storeから取得 |
| DRY_RUN | 実際のデプロイを行わず、実行される操作をログとして出力 | "false" |
| DEBUG_MODE | 詳細なログ出力を有効化 | "false" |
| SNS_NOTIFICATION_ENABLED | SNS通知機能の有効/無効 | 開発環境: "true"<br>検証環境: "false"<br>本番環境: "false" |
| SNS_TOPIC_ARN | エラー通知用SNSトピックのARN | 開発環境: SNSトピックARN<br>検証環境: ""<br>本番環境: "" |

#### 4.1.2 処理フロー

1. GitHub App認証情報を取得
2. GitHub APIトークンを生成
3. 前回デプロイ基準時刻を取得
4. 対象PRの変更内容を分析（時刻ベース）
5. デプロイ実行
6. デプロイ基準時刻を更新
7. PRにコメント追加
8. エラー発生時（開発環境のみ）：SNSトピックを通じてメール通知

### 4.2 Parameter Store

#### 4.2.1 パラメータ構造

**Parameter Store名**: `/dlpf/{環境}/deploy-baseline-time`

```json
{
  "deploy_baseline_time": "2025-05-27T10:30:00Z"
}
```

> **重要**: `deploy_baseline_time`フィールドは、次回デプロイ時にどの時刻以降のPRをデプロイ対象とするかを決定する重要な値です。この値が誤って古い時刻にリセットされると、過去のPRが再度デプロイ対象になり、不要なリソース消費や予期しない副作用が発生する可能性があります。デプロイ完了時に現在時刻で自動更新されます。

#### 4.2.2 パラメータ名

- 開発環境:
  - デプロイ基準時刻情報: `/dlpf/dev/deploy-baseline-time`
  - 通知メールアドレス: `/dlpf/dev/notification-email`
- 検証環境:
  - デプロイ基準時刻情報: `/dlpf/stg/deploy-baseline-time`
- 本番環境:
  - デプロイ基準時刻情報: `/dlpf/prd/deploy-baseline-time`

#### 4.2.3 Parameter Storeの管理

Parameter Storeリソースは専用のCloudFormationテンプレートで管理されています：

```yaml
# cicd/cloudformation/templates/system-manager/cicd-parameter-store.yaml
Resources:
  DeployBaselineTimeParameter:
    Type: AWS::SSM::Parameter
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      Name: !Sub '/dlpf/${Environment}/deploy-baseline-time'
      Type: String
      Value: '{"deploy_baseline_time": "1970-01-01T00:00:00Z"}'
      Description: 'Deploy baseline time information - DO NOT MODIFY DIRECTLY'
```

このテンプレートの特徴：

1. **DeletionPolicy: Retain**:
   - スタックが削除されても、Parameter Storeリソースは保持されます

2. **UpdateReplacePolicy: Retain**:
   - リソースが置き換えられる場合でも、元のリソースは保持されます

3. **初期値のみを設定**:
   - テンプレートは初期値のみを設定し、その後の値の更新はデプロイスクリプトが行います

> **重要**: このテンプレートは初期設定時に一度だけデプロイしてください。再デプロイすると、Parameter Storeの値が初期値にリセットされる可能性があります。

#### 4.2.4 エラーハンドリング

Parameter Storeの値が不正な場合、デプロイスクリプトは以下のようなエラーを表示して終了します：

- `Parameter Storeから値を取得できませんでした`
- `Parameter Storeの値が有効なJSON形式ではありません`
- `Parameter Storeの値に有効な'deploy_baseline_time'フィールドがありません`
- `deploy_baseline_time (***) が有効な日時形式ではありません`

これらのエラーが発生した場合、Parameter Storeの値を正しい形式に修正する必要があります。詳細は運用ガイドを参照してください。

### 4.3 SNSトピック（開発環境のみ）

#### 4.3.1 トピック構成

- トピック名: `cdp-dev-dlpf-deploy-error-notification`
- 表示名: `DLPF dev Deployment Error Notifications`
- プロトコル: email
- エンドポイント: Parameter Store `/dlpf/dev/notification-email` に設定されたメールアドレス

#### 4.3.2 通知内容

エラー通知メールには以下の情報が含まれます：

- 環境名（開発環境）
- プロジェクト名
- ビルドID
- ビルド番号
- タイムスタンプ
- エラーメッセージ
- 追加情報（コマンド出力など）
- CodeBuildコンソールへのリンク

### 4.4 デプロイスクリプト

#### 4.4.1 パラメータ

| パラメータ | 説明 | デフォルト値 |
|-----------|------|------------|
| ENVIRONMENT | デプロイ環境（dev/stg/prd） | 必須 |
| deploy_baseline_time | この時刻以降にマージされたPRを処理 | 省略可（Parameter Storeから取得） |
| -d, --dry-run | 実際のデプロイを行わず、実行される操作をログとして出力 | 省略可（デフォルトは実際にデプロイを実行） |

#### 4.4.2 使用例

以下のコマンド例を参考にしてください：

```bash
# 開発環境に最後のデプロイ以降のすべてのPRをデプロイする場合
./deploy.sh dev

# 開発環境に特定の時刻以降のPRをデプロイする場合
./deploy.sh dev "2025-05-27T10:00:00Z"

# 開発環境に最後のデプロイ以降のすべてのPRをDRY-RUNモードでテストする場合（実際のデプロイは行われません）
./deploy.sh dev --dry-run

# 開発環境に特定の時刻以降のPRをDRY-RUNモードでテストする場合
./deploy.sh dev "2025-05-27T10:00:00Z" -d
```

## 5. デプロイ範囲の定義

デプロイ範囲は以下のように定義されます：

- **deploy_baseline_time**: この時刻**以降**にマージされたPRを処理対象とします

時刻ベースの範囲指定により、PR番号の大小関係に依存しない確実なデプロイ管理を実現します。

### 5.1 特殊値

- **deploy_baseline_time = "1970-01-01T00:00:00Z"**: 「最初から」を意味する特殊値
- **deploy_baseline_time = "/dlpf/{env}/deploy-baseline-time"**: Parameter Storeから前回デプロイ基準時刻を取得

### 5.2 初回デプロイの処理

Parameter Storeに値が存在しない初回デプロイの場合、システムは以下のように処理します：

1. Parameter Storeから値を取得しようとする
2. 値が存在しない場合、deploy_baseline_time = "1970-01-01T00:00:00Z"（最初から）として処理
3. すべてのマージ済みPRをデプロイ対象とする
4. デプロイ完了後、Parameter Storeに現在時刻をデプロイ基準時刻として設定

これにより、初回デプロイ時には自動的にすべてのPRが対象となり、特別な対応は不要です。

### 5.3 デプロイ範囲の詳細な説明

#### 5.3.1 Parameter Storeの値の使用

デプロイスクリプトでdeploy_baseline_timeを省略した場合、システムは自動的にParameter Storeから前回デプロイ基準時刻を取得します。これにより、「前回のデプロイ以降のすべての変更」を簡単にデプロイできます。

```
./deploy.sh dev
```

この場合、内部的には以下のように処理されます：
1. Parameter Store `/dlpf/dev/deploy-baseline-time` から前回デプロイ基準時刻（例: "2025-05-27T10:00:00Z"）を取得
2. deploy_baseline_time = "2025-05-27T10:00:00Z" として処理
3. この時刻以降にマージされたPRをすべてデプロイ
4. デプロイ完了後、Parameter Storeの値を現在時刻に更新

#### 5.3.2 複数PRの一括デプロイ処理

複数のPRをまとめてデプロイする場合、システムは以下のように処理します：

1. 指定された時刻以降のすべてのマージ済みPRをGitHub APIから取得
2. 各PRの変更内容（ファイル変更、追加、削除）を分析
3. すべてのPRの変更を統合して、一度のデプロイ操作として実行
4. デプロイ完了後、各PRにデプロイ完了コメントを追加

これにより、複数のPRがある場合でも効率的にデプロイを実行できます。

#### 5.3.3 デプロイ順序の保証

PRのマージ順序とデプロイ順序を一致させるため、システムはPRのマージ日時順にソートして処理します。これにより、依存関係のある変更が正しい順序でデプロイされることを保証します。

### 5.4 使用例

以下に代表的な使用例を示します：

1. **初回デプロイ（すべてのPR）**:
   ```bash
   ./deploy.sh dev
   ```
   - Parameter Storeに値が存在しない場合の動作
   - deploy_baseline_time: "1970-01-01T00:00:00Z"（最初から）として自動的に処理されます
   - 結果: すべてのマージ済みPRがデプロイされます

2. **通常のデプロイ（最後のデプロイ以降すべて）**:
   ```bash
   ./deploy.sh dev
   ```
   - deploy_baseline_time: Parameter Storeから取得されます（例: "2025-05-27T10:00:00Z"）
   - 結果: この時刻以降にマージされたPRがすべてデプロイされます

3. **特定時刻以降のデプロイ**:
   ```bash
   ./deploy.sh dev "2025-05-27T15:00:00Z"
   ```
   - deploy_baseline_time: "2025-05-27T15:00:00Z"（指定時刻以降を処理）
   - 結果: 2025年5月27日15時以降にマージされたPRがデプロイされます

4. **特定日以降のすべてをデプロイ**:
   ```bash
   ./deploy.sh stg "2025-05-20T00:00:00Z"
   ```
   - deploy_baseline_time: "2025-05-20T00:00:00Z"（5月20日以降）
   - 結果: 5月20日以降にマージされたPRがすべてデプロイされます

### 5.5 エラー処理と再開

デプロイ中にエラーが発生した場合、以下のように特定の時刻以降から再開することができます：

```bash
# 特定の時刻以降のPRから再開する例（問題のあるPRより後の時刻を指定）
./deploy.sh dev "2025-05-27T16:00:00Z"
```

この方法により、問題のあるPRをスキップしながらデプロイを継続することが可能です。また、Parameter Storeの値を手動で調整することで、デプロイ基準時刻を柔軟に管理できます。

## 6. 実装ファイル一覧

| ファイルパス | 説明 |
|------------|------|
| `cicd/cloudformation/templates/codebuild/cicd-codebuild-deploy.yml` | デプロイ用CodeBuildプロジェクトのCloudFormationテンプレート |
| `cicd/cloudformation/templates/system-manager/cicd-parameter-store.yaml` | Parameter Store用のCloudFormationテンプレート |
| `cicd/cloudformation/templates/sns/cicd-notification-topic.yaml` | SNSトピック用のCloudFormationテンプレート |
| `cicd/cloudformation/environments/dev/parameters/codebuild/default.json` | 開発環境用パラメータファイル |
| `cicd/cloudformation/environments/stg/parameters/codebuild/default.json` | 検証環境用パラメータファイル |
| `cicd/buildspec/deploy-buildspec.yml` | デプロイ用ビルド仕様 |
| `cicd/scripts/deploy.sh` | デプロイ実行スクリプト |
| `cicd/scripts/codebuild/build.sh` | CodeBuildプロジェクトのビルドフェーズスクリプト |
| `cicd/scripts/codebuild/post_build.sh` | CodeBuildプロジェクトのポストビルドフェーズスクリプト |
| `cicd/scripts/utils/error-handling.sh` | エラーハンドリング共通処理 |
| `cicd/scripts/utils/logging.sh` | ログ出力共通処理 |
| `cicd/scripts/utils/sns-notification.sh` | SNS通知共通処理 |
| `cicd/ut_test/test_build_json_parameter.sh` | Parameter Store値のJSON解析テスト |
| `cicd/ut_test/test_build_parameter_store_error.sh` | Parameter Storeエラーハンドリングテスト |
| `cicd/docs/deployment-system.md` | デプロイシステムの設計書（本ドキュメント） |
| `cicd/docs/operation-guide.md` | 運用ガイド |

## 7. セキュリティ考慮事項

1. **GitHub App認証**:
   - GitHub Appの認証情報はAWS Secrets Managerで管理
   - 必要な権限のみを付与（Contents: read, Pull requests: read/write）
   - **重要**: デプロイシステムの前提条件として、以下のシークレットを事前に手動で作成する必要があります
     - 開発環境: `github-app-credentials`
     - 検証環境: `github-app-credentials-stg`
   - シークレットの形式:
     ```json
     {
       "app_id": "GitHub Appのアプリケーション ID",
       "installation_id": "GitHub Appのインストール ID",
       "private_key": "GitHub Appの秘密鍵（PEM形式）"
     }
     ```

2. **IAM権限**:
   - CodeBuildプロジェクトには最小権限の原則に基づいたIAMロールを付与
   - Parameter Storeへのアクセス権限を制限

3. **VPC設定**:
   - CodeBuildプロジェクトはVPC内で実行し、固定IPアドレスを使用
   - GitHub APIへのアクセスはNAT Gatewayを経由

## 8. 運用考慮事項

1. **定期的なデプロイ**:
   - 長期間デプロイしない場合、PRが多数蓄積される可能性
   - 定期的なデプロイを推奨（週1回程度）

2. **エラーハンドリング**:
   - デプロイ中にエラーが発生した場合、特定のPRをスキップして再開可能
   - エラーログは CloudWatch Logs に保存

3. **モニタリング**:
   - デプロイの成功/失敗を CloudWatch Events で監視
   - 長時間実行されるデプロイに対するアラート設定

## 9. 今後の拡張可能性

1. **自動デプロイスケジュール**:
   - EventBridge Scheduler を使用した定期的な自動デプロイ

2. **デプロイ承認フロー**:
   - CodePipeline と統合し、デプロイ前に承認ステップを追加

3. **デプロイ履歴管理**:
   - デプロイ履歴をDynamoDBに保存し、ダッシュボードで可視化

## 10. 時刻ベース管理による追越し問題解決

### 10.1 課題の背景

従来のSSM Parameter Store（PR番号ベース）方式では、以下の「PR追越し問題」が発生していました：

```
問題例:
1. PR #1100, #1101, #1102 が作成
2. PR #1102 が先にマージ → Deploy実行 → SSMに1102を格納
3. PR #1100, #1101 が後でマージ
4. 次回Deploy時: FROM_PR=1102 のため、#1100, #1101 はDeploy対象外
```

さらに、時刻ベース管理導入後も以下の「タイムラグ問題」が発見されました：

```
タイムラグ問題例:
1. 11:00 定時起動、基準時刻(10:05)を取得
2. PR検索: merged_at >= "10:05" → この時点でPR1444はまだマージされていない
3. 11:02 デプロイ実行中にPR1444がマージ
4. 11:08 デプロイ完了、基準時刻を現在時刻(11:08)で更新
5. 12:00 次回起動時: merged_at >= "11:08" → PR1444(11:02)は検出されない
```

さらに、締切時刻仕組み導入後も以下の「GitHub API遅延問題」が発見されました：

```
GitHub API遅延問題例:
1. 11:00:00 CodeBuild定時起動、締切時刻=11:00:00
2. 10:59:30 PRマージ完了
3. 11:00:05 GitHub CLI実行 → まだPR情報が反映されていない
4. 結果: 10:59:30のPRが検出されない
```

### 10.2 時刻ベース管理の設計（締切時刻仕組み対応版）

#### 10.2.1 基本方針

- **時刻ベース管理**: PR番号ではなく、デプロイ基準時刻で管理
- **確実性**: PR番号の大小関係に依存しない
- **直感性**: 「X時刻以降の変更をデプロイ」という明確な概念
- **締切時刻仕組み**: デプロイ実行中のPRマージによる検出漏れを防止

#### 10.2.2 Deploy対象PR取得方式（改善版）

**従来方式（問題あり）**:
```bash
# 問題: デプロイ実行中にマージされたPRが次回検出されない
deploy_baseline_time=$(aws ssm get-parameter --name "/dlpf/dev/deploy-baseline-time" --query "Parameter.Value" --output text | jq -r '.deploy_baseline_time')
gh pr list --state merged --base develop --json number,title,mergedAt \
  --jq ".[] | select(.mergedAt >= \"${deploy_baseline_time}\")"
```

**改善版（締切時刻仕組み + ブランチフィルター）**:
```bash
# 解決: 締切時刻により確定的な処理範囲を設定 + 環境別ブランチフィルター
deploy_baseline_time=$(aws ssm get-parameter --name "/dlpf/dev/deploy-baseline-time" --query "Parameter.Value" --output text | jq -r '.deploy_baseline_time')
deploy_deadline_time=$(date -u -d '5 minutes ago' +"%Y-%m-%dT%H:%M:%SZ")  # デプロイ起動時刻 - 5分
gh pr list --state merged --base "${BASE_BRANCH}" --json number,title,mergedAt \
  --jq ".[] | select(.mergedAt > \"${deploy_baseline_time}\" and .mergedAt <= \"${deploy_deadline_time}\")"
```

#### 10.2.3 実装方式（締切時刻仕組み対応版）

1. **Deploy開始時の締切時刻設定**
   - デプロイ起動時刻を締切時刻として記録
   - 処理対象PRの確定的な範囲設定

2. **Deploy対象判定ロジック**
   - 検索条件: `前回基準時刻 < merged_at <= 締切時刻`
   - 確定的な処理範囲によりタイムラグ問題を解決

3. **Deploy完了時の基準時刻更新**
   - 従来: 現在時刻で更新（問題あり）
   - 改善: 締切時刻で更新（確実性向上）

4. **運用柔軟性**
   - 人間による手動時刻調整も可能（緊急時対応）
   - 環境ごとに独立した時刻管理
   - デプロイ実行中のマージも次回確実に処理

### 10.3 締切時刻仕組みの詳細設計

#### 10.3.1 処理フロー

```mermaid
sequenceDiagram
    participant CodeBuild as CodeBuild
    participant GitHub as GitHub API
    participant SSM as Parameter Store

    Note over CodeBuild,SSM: 定時起動（例：11:00）
    CodeBuild->>CodeBuild: 締切時刻設定 (11:00)
    CodeBuild->>SSM: 基準時刻取得 (10:05)
    CodeBuild->>GitHub: PR検索: 10:05 < merged_at <= 11:00
    GitHub-->>CodeBuild: 確定PR一覧
    Note over CodeBuild: デプロイ実行中...
    Note over GitHub: 11:02 新しいPRマージ<br/>（次回対象）
    Note over CodeBuild: デプロイ完了 (11:08)
    CodeBuild->>SSM: 基準時刻更新 (11:00)

    Note over CodeBuild,SSM: 次回起動（例：12:00）
    CodeBuild->>CodeBuild: 締切時刻設定 (12:00)
    CodeBuild->>SSM: 基準時刻取得 (11:00)
    CodeBuild->>GitHub: PR検索: 11:00 < merged_at <= 12:00
    Note over GitHub: 11:02のPRが確実に検出される
```

#### 10.3.2 技術仕様

**パラメータ**:
- `deploy_baseline_time`: 前回デプロイの基準時刻
- `deploy_deadline_time`: 今回デプロイの締切時刻（起動時刻 - 5分）

**検索条件**:
```bash
# 従来（問題あり）
merged_at >= deploy_baseline_time

# 改善（締切時刻仕組み）
merged_at > deploy_baseline_time AND merged_at <= deploy_deadline_time
```

**基準時刻更新**:
```bash
# 従来（問題あり）
new_baseline_time = current_time  # デプロイ完了時刻

# 改善（締切時刻仕組み + GitHub API遅延対応）
new_baseline_time = deploy_deadline_time  # デプロイ起動時刻 - 5分
```

### 10.4 利点

1. **追越し問題の完全解決**: PR番号に依存しない管理
2. **タイムラグ問題の解決**: 締切時刻による確定的な処理範囲
3. **GitHub API遅延問題の解決**: 5分バッファによる確実な情報取得
4. **環境混在問題の解決**: ブランチフィルターによる環境分離
5. **シンプルな実装**: 複雑な番号管理不要
6. **直感的運用**: 時刻ベースの明確な概念
7. **運用効率**: GitHub CLI連携と柔軟な時刻指定
8. **確実性向上**: デプロイ実行中のマージも次回確実に処理
9. **API安定性**: GitHub内部遅延に対する耐性
10. **環境安全性**: 他環境のPRが誤って処理されることを防止
11. **データ整合性**: マージされたPRのみを確実に処理

## 11. CodeBuild権限の包括的拡張

### 11.1 権限修正の背景

2025-05-27に発生したAccessDeniedエラーを受けて、CodeBuildの権限を包括的に拡張しました。

#### 11.1.1 主要エラー原因

```
An error occurred (AccessDenied) when calling the GetTemplateSummary operation:
User: arn:aws:sts::886436956581:assumed-role/cdp-dev-dlpf-deploy-role/AWSCodeBuild-xxx
is not authorized to perform: cloudformation:GetTemplateSummary
```

### 11.2 追加された権限

#### 11.2.1 CloudFormation権限拡張

```yaml
- cloudformation:GetTemplateSummary      # 主要エラー原因
- cloudformation:DescribeStackResources
- cloudformation:DescribeStackEvents
- cloudformation:GetTemplate
```

#### 11.2.2 業務用AWSサービス権限

| サービス | 追加権限 | 対象リソース |
|---------|----------|-------------|
| **IAM** | ロール管理の包括的権限 | `dlpf-*` パターン |
| **EventBridge** | ルール・ターゲット管理 | `rule/*` |
| **Lambda** | 関数管理・権限設定 | `function:*` |
| **Step Functions** | ステートマシン管理 | `stateMachine:*` |
| **Cognito** | ユーザープール管理 | `userpool/*` |
| **Secrets Manager** | シークレット管理 | `dlpf*` パターン |
| **EC2** | セキュリティグループ管理 | 全リソース |
| **Systems Manager** | パラメータストア拡張 | `dlpf/*` パターン |
| **Glue** | ジョブ・コネクター管理 | `job/*`, `connection/*` |

### 11.3 対象業務用リソース

```
cloudformation/templates/
├── cognito/           # Amazon Cognito
├── event-bridge/      # Amazon EventBridge
├── glue-connector/    # AWS Glue Connector
├── glue-job/          # AWS Glue Job
├── lambda/            # AWS Lambda
├── secrets-manager/   # AWS Secrets Manager
├── security-group/    # EC2 Security Group
├── step-functions/    # AWS Step Functions
└── system-manager/    # AWS Systems Manager
```

## 12. 変更履歴

| 日付 | バージョン | 変更者 | 変更内容 |
|------|-----------|-------|---------|
| 2025-05-13 | 0.1 | TIS黄 | 初版作成 |
| 2025-05-14 | 0.2 | TIS黄 | DRY-RUNモードの追加 |
| 2025-05-15 | 0.3 | TIS黄 | GitHub App認証シークレットの事前作成に関する前提条件を追加 |
| 2025-05-23 | 0.4 | TIS黄 | Parameter Storeの管理方法を改善（専用CFNテンプレート、エラーハンドリング強化） |
| 2025-05-27 | 0.5 | TIS黄 | GitHubラベル方式による追越し問題解決設計を追加 |
| 2025-05-27 | 0.6 | TIS黄 | CodeBuild権限の包括的拡張（全業務用AWSサービス対応） |
| 2025-05-27 | 0.7 | TIS黄 | 時刻ベース管理方式への設計変更（PR追越し問題の根本解決） |
| 2025-06-05 | 0.8 | TIS黄 | 締切時刻仕組み追加（デプロイ実行中PRマージのタイムラグ問題解決） |
| 2025-06-05 | 0.9 | TIS黄 | 締切時刻バッファ追加（GitHub API反映遅延問題解決） |
| 2025-06-05 | 1.0 | TIS黄 | ブランチフィルター追加（環境混在問題解決） |
| 2025-06-05 | 1.1 | TIS黄 | PR状態指定修正（closed→merged、データ整合性向上） |
| 2025-06-05 | 1.2 | TIS黄 | 本番環境対応：三分岐条件分岐ロジック実装（dev/stg/prd対応） |
