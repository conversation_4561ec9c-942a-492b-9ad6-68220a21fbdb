#!/bin/bash

# =============================================================================
# issue 641関連コミット取得スクリプト
# =============================================================================
# 目的: issue 641に関連するPRのマージコミットハッシュを取得
# 使用場面: release_20250606_1ブランチ作成時のcherry-pick対象特定
# 作成者: TIS黄
# 作成日: 2025-06-05
# =============================================================================

set -e  # エラー時に停止

echo "🔍 issue 641関連コミット取得開始..."
echo "=================================================="

# GitHub CLIの確認
if ! command -v gh &> /dev/null; then
    echo "❌ エラー: GitHub CLI (gh) がインストールされていません"
    echo "   インストール方法: https://cli.github.com/"
    exit 1
fi

# GitHub認証確認
if ! gh auth status &> /dev/null; then
    echo "❌ エラー: GitHub CLIの認証が必要です"
    echo "   実行してください: gh auth login"
    exit 1
fi

echo "✅ GitHub CLI認証確認完了"

# developブランチに切り替え
echo ""
echo "📂 developブランチに切り替え中..."
git checkout develop
git pull origin develop

echo ""
echo "🔍 issue 641および子issueにリンクされたPR一覧を取得中..."

# Redmine 7295関連のすべてのissue番号を取得
echo "   Redmine 7295関連のissue一覧を取得中..."
related_issues=$(gh issue list --search "7295" --state all --json number --jq '.[].number' | sort -n)

if [ -z "$related_issues" ]; then
    echo "❌ エラー: Redmine 7295関連のissueが見つかりません"
    exit 1
fi

echo "✅ 関連issue数: $(echo "$related_issues" | wc -l)件"
echo "   対象issue: $(echo "$related_issues" | tr '\n' ' ')"

# 各issueのConnectedEventを取得
echo ""
echo "   各issueのConnectedEventを取得中..."
all_pr_data=""

for issue_num in $related_issues; do
    echo "     issue #$issue_num を処理中..."

    issue_pr_data=$(gh api graphql -f query="
    query {
      repository(owner: \"TIS-DSDev\", name: \"tis-dlpf-app\") {
        issue(number: $issue_num) {
          timelineItems(first: 100, itemTypes: [CONNECTED_EVENT]) {
            nodes {
              __typename
              ... on ConnectedEvent {
                subject {
                  ... on PullRequest {
                    number
                    title
                    mergeCommit {
                      oid
                    }
                    state
                    mergedAt
                  }
                }
              }
            }
          }
        }
      }
    }" --jq '.data.repository.issue.timelineItems.nodes[] | select(.subject.mergeCommit != null) | .subject' 2>/dev/null)

    if [ -n "$issue_pr_data" ]; then
        if [ -z "$all_pr_data" ]; then
            all_pr_data="$issue_pr_data"
        else
            all_pr_data="$all_pr_data"$'\n'"$issue_pr_data"
        fi
        issue_pr_count=$(echo "$issue_pr_data" | jq -s 'length')
        echo "       → ${issue_pr_count}件のマージ済みPRを発見"
    else
        echo "       → マージ済みPRなし"
    fi
done

if [ -z "$all_pr_data" ]; then
    echo "❌ エラー: 関連issueにリンクされたマージ済みPRが見つかりません"
    exit 1
fi

# 重複を除去（PR番号でユニーク化）
pr_data=$(echo "$all_pr_data" | jq -s 'unique_by(.number)')
pr_count=$(echo "$pr_data" | jq 'length')
echo "✅ 重複除去後のマージ済みPR数: ${pr_count}件"

echo ""
echo "📋 各PRのマージコミット情報:"
echo "=================================================="

# 結果を格納する配列
declare -a commit_hashes
declare -a commit_info

# 各PRのマージコミット情報を処理（マージ日時順でソート）
while IFS='|' read -r pr_num merge_commit pr_title merged_at; do
    echo "🔍 PR #$pr_num を処理中..."

    if [ -z "$merge_commit" ] || [ "$merge_commit" = "null" ]; then
        echo "   ⚠️  PR #$pr_num のマージコミットが見つかりません"
        continue
    fi

    # 結果を配列に追加
    commit_hashes+=("$merge_commit")
    commit_info+=("PR #$pr_num: $merge_commit - $pr_title (merged: $merged_at)")

    echo "   ✅ $merge_commit - $pr_title"
done < <(echo "$pr_data" | jq -r '.[] | "\(.number)|\(.mergeCommit.oid)|\(.title)|\(.mergedAt)"' | sort -t'|' -k4)

echo ""
echo "=================================================="
echo "📋 cherry-pick対象コミット一覧 (時系列順):"
echo "=================================================="

if [ ${#commit_hashes[@]} -eq 0 ]; then
    echo "❌ エラー: 有効なマージコミットが見つかりませんでした"
    exit 1
fi

# 結果を表示
for info in "${commit_info[@]}"; do
    echo "$info"
done

echo ""
echo "📝 cherry-pickコマンド例:"
echo "=================================================="
echo "# releaseブランチベースで新ブランチ作成"
echo "git checkout release"
echo "git pull origin release"
echo "git checkout -b release_20250606_1"
echo ""
echo "# コミットを古い順にcherry-pick"
for hash in "${commit_hashes[@]}"; do
    echo "git cherry-pick $hash"
done
echo ""
echo "# リモートにプッシュ"
echo "git push origin release_20250606_1"

echo ""
echo "✅ issue 641関連コミット取得完了"
echo "   対象コミット数: ${#commit_hashes[@]}件"
echo ""
echo "📝 次のステップ:"
echo "   1. 上記のcherry-pickコマンドを実行"
echo "   2. release_20250606_1 → release のPR作成"
echo "   3. 検証環境CI/CDシステム構築作業に進む"
