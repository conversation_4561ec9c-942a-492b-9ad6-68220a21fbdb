# IAM ロール移行手順書

## 📋 概要

**対象**: Redmine 7295 - 本番環境 CI/CD システム用 IAM ロール管理をインフラチームに移行 (#1757)
**目的**: CloudFormation スタック削除を避けて、IAM ロール依存関係の問題を解決
**環境**: 開発環境（dev）
**作成日**: 2025-07-25

## ⚠️ 現在の問題

### 発生している問題

1. **CloudFormation デプロイ**: 成功したが内部で IAM ロール削除エラー
2. **IAM ロール削除失敗**: `cdp-dev-dlpf-deploy-role` の削除権限不足
3. **CodeBuild プロジェクト実行エラー**: VPC ネットワークエラー
4. **根本原因**: CloudFormation 内部状態の不整合

## 🎯 解決方針

### 基本方針

- **CloudFormation スタック削除を避ける**（CloudWatch ログ保護のため）
- **一時的な別名 IAM ロールを使用**して依存関係を解決
- **段階的にロール移行**を実施

## 🔧 実施手順

### **Step 1: インフラチーム - 権限申請と一時的ロール作成**

#### **担当**: 滝澤さん（インフラチーム）

#### **作業内容**

1. IAM ロール削除権限申請

   - 対象ロール: `cdp-dev-dlpf-deploy-role`
   - 申請理由: 古いロール削除のため
   - 申請期間: 作業完了まで（短期間）

2. 新しい IAM ロール作成（インフラ命名規則に従って）

   - ロール名: `role-dev-dlpf-cdp-appdeploy`
   - 信頼関係: CodeBuild サービス

3. 必要な権限を付与
   - CodeBuild 実行に必要な権限（詳細はインフラチームに連携済み）

#### **確認事項**

- [ ] IAM ロール削除権限申請完了・権限付与確認
- [ ] 新しいロール `role-dev-dlpf-cdp-appdeploy` が作成完了
- [ ] 必要な権限がすべて付与完了
- [ ] ロールの信頼関係が正しく設定

### **Step 2: CloudFormation テンプレート一時修正**

#### **担当**: 黄さん（アプリ開発チーム）

#### **作業内容**

1. CloudFormation テンプレート修正
   - ファイル: `cicd/cloudformation/templates/codebuild/cicd-codebuild-deploy.yml`
   - 修正内容: ServiceRole を新しいロール名に変更
   - 変更: `cdp-dev-dlpf-deploy-role` → `role-dev-dlpf-cdp-appdeploy`

#### **確認事項**

- [ ] テンプレート修正完了
- [ ] 新しいロール名が正しく設定
- [ ] 他の箇所に影響がないことを確認

### **Step 3: CloudFormation デプロイ実行**

#### **担当**: 黄さん（アプリ開発チーム）

#### **実行前確認**

- [ ] Step 1（インフラチーム作業）が完了
- [ ] Step 2（テンプレート修正）が完了
- [ ] チームメンバーへの事前通知完了

#### **実行内容**

1. CloudFormation デプロイ実行
   - 新しいロールを参照してデプロイ
   - CFN の差分比較により古い IAM ロールが自動削除される

#### **期待される結果**

- CloudFormation デプロイ成功
- 古い IAM ロール自動削除成功（CFN の差分検出機能により）
- CodeBuild プロジェクトが新しいロールで動作

### **Step 4: 動作確認**

#### **担当**: 黄さん（アプリ開発チーム）

#### **確認内容**

1. 古い IAM ロール削除確認

   - `cdp-dev-dlpf-deploy-role` が削除されていることを確認

2. CodeBuild プロジェクト実行テスト
   - テスト実行で VPC エラーが解消されることを確認
   - CloudWatch ログが正常出力されることを確認

#### **確認事項**

- [ ] 古い IAM ロール `cdp-dev-dlpf-deploy-role` が削除完了
- [ ] VPC ネットワークエラーが解消
- [ ] CodeBuild プロジェクトが正常実行
- [ ] CloudWatch ログが正常出力

### **Step 5: インフラチーム - 旧ロール削除と権限取り消し申請**

#### **担当**: 滝澤さん（インフラチーム）

#### **作業内容**

1. 旧ロール削除

   - ロール名: `cdp-dev-dlpf-deploy-role`
   - 削除タイミング: 新ロールでの動作確認完了後

2. IAM ロール削除権限取り消し申請
   - 対象権限: Step 1 で申請した削除権限
   - 申請理由: 作業完了のため権限不要
   - セキュリティ強化: 不要な権限の削除

#### **確認事項**

- [ ] 旧ロール `cdp-dev-dlpf-deploy-role` が削除完了
- [ ] 権限が正しく移行されていることを確認
- [ ] IAM ロール削除権限取り消し申請完了
- [ ] 権限削除確認・セキュリティ設定の正常化確認

### **Step 6: 最終確認**

#### **担当**: 黄さん（アプリ開発チーム）

#### **確認事項**

- [ ] 新ロール `role-dev-dlpf-cdp-appdeploy` で正常動作
- [ ] 旧ロール `cdp-dev-dlpf-deploy-role` が削除完了
- [ ] CodeBuild プロジェクトが継続的に正常動作

## ✅ 完了確認

### 最終確認事項

- [ ] CloudFormation スタック正常状態
- [ ] 古い IAM ロール `cdp-dev-dlpf-deploy-role` が削除完了
- [ ] 新しい IAM ロール `role-dev-dlpf-cdp-appdeploy` が正しく設定
- [ ] CodeBuild プロジェクト正常実行
- [ ] CloudWatch ログ正常出力
- [ ] VPC ネットワークエラー解消

## 📞 連絡先・エスカレーション

### 担当者

- **アプリ開発チーム**: 黄さん (<EMAIL>)
- **インフラチーム**: 滝澤さん

### 問題発生時

1. 作業を中断
2. チーム内で状況共有
3. 必要に応じてロールバック検討

## 📝 作業ログ

### 実施日時・担当者・結果

| Step   | 実施日時   | 担当者   | 結果 | 備考                                        |
| ------ | ---------- | -------- | ---- | ------------------------------------------- |
| Step 1 | 2025-07-25 | 滝澤さん | 完了 | 新ロール `role-dev-dlpf-cdp-appdeploy` 作成 |
| Step 2 | 2025-07-25 | 黄さん   | 完了 | CFN テンプレート修正・PR #1771 マージ       |
| Step 3 | 2025-07-25 | 黄さん   | 完了 | CFN デプロイ・新ロールへの移行完了          |
| Step 4 | 2025-07-25 | 黄さん   | 完了 | 動作確認・CodeBuild 正常実行確認            |
| Step 5 | 2025-07-25 | 滝澤さん | 完了 | 旧ロール削除・権限取り消し申請              |
| Step 6 | 2025-07-25 | 黄さん   | 完了 | 最終確認・継続動作確認                      |

## 🚨 実際の問題と対処法（2025-07-25 実施結果）

### **発生した問題**

#### **問題の詳細**

- **滝澤さん**: 旧ロール `cdp-dev-dlpf-deploy-role` を先に削除
- **CloudFormation**: CodeBuild プロジェクトを新ロール `role-dev-dlpf-cdp-appdeploy` に更新しようとする
- **CodeBuild**: 現在設定されている旧ロール `cdp-dev-dlpf-deploy-role` が存在しないため、AssumeRole エラー
- **CloudFormation**: 更新失敗

#### **エラーメッセージ**

```
CodeBuild is not authorized to perform: sts:AssumeRole on service role.
Please verify that:
1) The provided service role exists,
2) The role name is case-sensitive and matches exactly,
3) The role has the necessary trust policy configured.
```

### **根本原因**

**手順の順序違反**:

1. ❌ **実際の実施順序**: 旧ロール削除 → CFN テンプレート更新 → CFN デプロイ
2. ✅ **正しい順序**: CFN テンプレート更新 → CFN デプロイ → 旧ロール削除

### **対処法**

#### **順序違反が発生した場合の緊急対処手順**

1. **AWS CLI で直接 CodeBuild プロジェクトのサービスロールを更新**

   ```bash
   aws codebuild update-project --name cdp-dev-dlpf-deploy \
     --service-role arn:aws:iam::ACCOUNT_ID:role/NEW_ROLE_NAME
   ```

2. **更新確認**

   ```bash
   aws codebuild batch-get-projects --names cdp-dev-dlpf-deploy \
     --query 'projects[0].serviceRole'
   ```

3. **CloudFormation デプロイ再実行**

   ```bash
   cd cicd/cloudformation
   ./scripts/cfn_deploy.sh -e dev -n -y codebuild cicd-codebuild-deploy
   ```

4. **動作確認**
   ```bash
   aws codebuild start-build --project-name cdp-dev-dlpf-deploy \
     --environment-variables-override name=DRY_RUN,value=true
   ```

### **予防策**

#### **正しい IAM ロール移行手順**

1. **新ロール作成** ✅
2. **CloudFormation テンプレート更新** ✅
3. **CloudFormation デプロイ** ✅
4. **動作確認** ✅
5. **旧ロール削除** ✅ ← **この順序が重要！**

#### **チェックリスト**

- [ ] 新ロールが作成済みであることを確認
- [ ] CFN テンプレートが新ロール名に更新済み
- [ ] CFN デプロイが成功していることを確認
- [ ] CodeBuild プロジェクトが新ロールで正常動作することを確認
- [ ] **最後に**旧ロールを削除

### **教訓**

1. **IAM ロール移行は段階的に実施**する
2. **CloudFormation の更新を先に完了**させる
3. **旧リソースの削除は最後**に実施する
4. **順序違反が発生した場合は、AWS CLI での直接修正**が有効

## 🎯 検証環境（stg）での正常系移行手順

### **概要**

開発環境での経験を踏まえ、検証環境では**正常系の手順**で実施します。

### **前提条件**

- 開発環境での移行作業完了・経験蓄積済み
- 滝澤さんとの合意済み命名規則: `role-{env}-dlpf-cdp-appdeploy`
- 検証環境の新ロール名: `role-stg-dlpf-cdp-appdeploy`

### **🔧 正常系実施手順**

#### **Step 1: インフラチーム - 新ロール作成**

**担当**: 滝澤さん（インフラチーム）

**作業内容**:

1. **新しい IAM ロール作成**

   - ロール名: `role-stg-dlpf-cdp-appdeploy`
   - 信頼関係: `codebuild.amazonaws.com`
   - 権限: 開発環境と同等の権限を設定

2. **権限設定確認**
   - CloudWatch Logs 権限
   - VPC 関連権限（ec2:CreateNetworkInterface 等）
   - CloudFormation 権限
   - その他業務用権限

**確認事項**:

- [ ] 新ロール `role-stg-dlpf-cdp-appdeploy` 作成完了
- [ ] 信頼関係が正しく設定
- [ ] 必要な権限が全て付与済み
- [ ] **旧ロールは削除しない**（重要）

#### **Step 2: CFN デプロイ実行**

**担当**: 黄さん（アプリ開発チーム）

**実行前確認**:

- [ ] Step 1（新ロール作成）完了
- [ ] **リードによる CherryPick で release ブランチにマージ完了**
- [ ] 検証環境 AWS 認証情報設定完了

**作業内容**:

1. **最新コード取得（検証環境用）**

   ```bash
   git checkout release
   git pull origin release
   ```

2. **検証環境 AWS 認証設定**

   ```bash
   rm ~/.aws
   ln -s ~/.aws.stg ~/.aws
   ```

3. **CFN デプロイ実行**
   ```bash
   cd cicd/cloudformation
   ./scripts/cfn_deploy.sh -v -e stg codebuild cicd-codebuild-deploy  # 検証
   ./scripts/cfn_deploy.sh -d -e stg codebuild cicd-codebuild-deploy  # ドライラン
   ./scripts/cfn_deploy.sh -e stg -n -y codebuild cicd-codebuild-deploy  # 実行
   ```

**期待される結果**:

- CloudFormation デプロイ成功
- CodeBuild プロジェクトが新ロールで更新
- **旧ロールは残存**（まだ削除しない）

**確認事項**:

- [ ] CFN デプロイ成功
- [ ] CodeBuild プロジェクトが新ロール参照
- [ ] エラーが発生していない

#### **Step 3: 動作確認**

**担当**: 黄さん（アプリ開発チーム）

**作業内容**:

1. **CodeBuild プロジェクト確認**

   ```bash
   aws codebuild batch-get-projects --names cdp-stg-dlpf-deploy \
     --query 'projects[0].serviceRole'
   ```

2. **テスト実行**

   ```bash
   aws codebuild start-build --project-name cdp-stg-dlpf-deploy \
     --environment-variables-override name=DRY_RUN,value=true
   ```

3. **ログ確認**
   - CloudWatch Logs でビルドログ確認
   - エラーが発生していないことを確認

**確認事項**:

- [ ] CodeBuild プロジェクトが新ロールで正常動作
- [ ] CloudWatch Logs 正常出力
- [ ] VPC ネットワークエラーなし

#### **Step 4: インフラチーム - 旧ロール削除**

**担当**: 滝澤さん（インフラチーム）

**実行前確認**:

- [ ] Step 3（動作確認）完了
- [ ] CodeBuild プロジェクトが新ロールで正常動作確認済み

**作業内容**:

1. **旧ロール削除**
   - 削除対象: 検証環境の旧ロール
   - 削除タイミング: 動作確認完了後

**確認事項**:

- [ ] 旧ロール削除完了
- [ ] CodeBuild プロジェクト継続動作確認

### **🎯 正常系手順のポイント**

#### **重要な順序**

1. ✅ **新ロール作成**
2. ✅ **CFN デプロイ**（リードによる CherryPick 完了後）
3. ✅ **動作確認**
4. ✅ **旧ロール削除** ← **最後に実施**

#### **開発環境との違い**

- **開発環境**: 順序違反 → 緊急対処が必要
- **検証環境**: 正常系手順 → スムーズな移行

#### **成功要因**

1. **段階的実施**: 各ステップの完了確認
2. **順序遵守**: 旧リソース削除は最後
3. **十分な確認**: 動作確認完了後に削除
4. **経験活用**: 開発環境での学習を反映

### **📋 検証環境移行チェックリスト**

- [ ] **Step 1**: 新ロール `role-stg-dlpf-cdp-appdeploy` 作成完了
- [ ] **Step 2**: CFN デプロイ成功
- [ ] **Step 3**: CodeBuild プロジェクト正常動作確認
- [ ] **Step 4**: 旧ロール削除完了
- [ ] **最終確認**: 検証環境で継続的に正常動作

## 🎯 本番環境（prd）での正常系移行手順

### **概要**

検証環境での移行作業完了後、本番環境でも同様の手順で実施します。

### **前提条件**

- 開発環境・検証環境での移行作業完了・経験蓄積済み
- 滝澤さんとの合意済み命名規則: `role-{env}-dlpf-cdp-appdeploy`
- 本番環境の新ロール名: `role-prd-dlpf-cdp-appdeploy`

### **🔧 正常系実施手順**

#### **Step 1: インフラチーム - 新ロール作成**

**担当**: 滝澤さん（インフラチーム）

**作業内容**:

1. **新しい IAM ロール作成**
   - ロール名: `role-prd-dlpf-cdp-appdeploy`
   - 信頼関係: `codebuild.amazonaws.com`
   - 権限: 検証環境と同等の権限を設定

**確認事項**:

- [ ] 新ロール `role-prd-dlpf-cdp-appdeploy` 作成完了
- [ ] 信頼関係が正しく設定
- [ ] 必要な権限が全て付与済み
- [ ] **旧ロールは削除しない**（重要）

#### **Step 2: CFN デプロイ実行**

**担当**: 黄さん（アプリ開発チーム）

**実行前確認**:

- [ ] Step 1（新ロール作成）完了
- [ ] **リードによる CherryPick で master ブランチにマージ完了**
- [ ] 本番環境 AWS 認証情報設定完了

**作業内容**:

1. **最新コード取得（本番環境用）**

   ```bash
   git checkout master
   git pull origin master
   ```

2. **本番環境 AWS 認証設定**

   ```bash
   rm ~/.aws
   ln -s ~/.aws.prd ~/.aws
   ```

3. **CFN デプロイ実行**
   ```bash
   cd cicd/cloudformation
   ./scripts/cfn_deploy.sh -v -e prd codebuild cicd-codebuild-deploy  # 検証
   ./scripts/cfn_deploy.sh -d -e prd codebuild cicd-codebuild-deploy  # ドライラン
   ./scripts/cfn_deploy.sh -e prd -n -y codebuild cicd-codebuild-deploy  # 実行
   ```

**期待される結果**:

- CloudFormation デプロイ成功
- CodeBuild プロジェクトが新ロールで更新
- **旧ロールは残存**（まだ削除しない）

**確認事項**:

- [ ] CFN デプロイ成功
- [ ] CodeBuild プロジェクトが新ロール参照
- [ ] エラーが発生していない

#### **Step 3: 動作確認**

**担当**: 黄さん（アプリ開発チーム）

**作業内容**:

1. **CodeBuild プロジェクト確認**

   ```bash
   aws codebuild batch-get-projects --names cdp-prd-dlpf-deploy \
     --query 'projects[0].serviceRole'
   ```

2. **テスト実行**

   ```bash
   aws codebuild start-build --project-name cdp-prd-dlpf-deploy \
     --environment-variables-override name=DRY_RUN,value=true
   ```

3. **ログ確認**
   - CloudWatch Logs でビルドログ確認
   - エラーが発生していないことを確認

**確認事項**:

- [ ] CodeBuild プロジェクトが新ロールで正常動作
- [ ] CloudWatch Logs 正常出力
- [ ] VPC ネットワークエラーなし

#### **Step 4: インフラチーム - 旧ロール削除**

**担当**: 滝澤さん（インフラチーム）

**実行前確認**:

- [ ] Step 3（動作確認）完了
- [ ] CodeBuild プロジェクトが新ロールで正常動作確認済み

**作業内容**:

1. **旧ロール削除**
   - 削除対象: 本番環境の旧ロール
   - 削除タイミング: 動作確認完了後

**確認事項**:

- [ ] 旧ロール削除完了
- [ ] CodeBuild プロジェクト継続動作確認

### **📋 本番環境移行チェックリスト**

- [ ] **Step 1**: 新ロール `role-prd-dlpf-cdp-appdeploy` 作成完了
- [ ] **Step 2**: CFN デプロイ成功（**master ブランチから実行**）
- [ ] **Step 3**: CodeBuild プロジェクト正常動作確認
- [ ] **Step 4**: 旧ロール削除完了
- [ ] **最終確認**: 本番環境で継続的に正常動作

## 📝 ブランチ戦略の重要事項

### **環境別ブランチ対応**

- **開発環境（dev）**: `develop` ブランチ
- **検証環境（stg）**: `release` ブランチ（リードが CherryPick でマージ）
- **本番環境（prd）**: `master` ブランチ（リードが CherryPick でマージ）

### **デプロイ時の注意点**

1. **CFN テンプレート修正**: `develop` ブランチで作業・PR 作成（開発環境で実施済み）
2. **リードの作業**: CherryPick で `develop` → `release` → `master` の順にマージ
3. **検証環境デプロイ**: `release` ブランチをチェックアウト
4. **本番環境デプロイ**: `master` ブランチをチェックアウト

---

**作成者**: Augment Agent
**レビュー**: 黄さん
**承認**: 二宮さん・原田さん
**更新**: 2025-07-25 実施結果反映・検証環境手順追加・ブランチ戦略反映
