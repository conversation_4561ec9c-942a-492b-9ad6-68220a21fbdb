# IAM ロール作成申請書（インフラチーム向け）

## 📋 申請概要

**申請日**: 2025-07-22
**申請者**: TIS 黄
**プロジェクト**: DLPF CI/CD システム
**目的**: 本番環境 CI/CD システム用 IAM ロールの作成

## 📖 経緯

### **既存環境の状況**

- **開発環境・検証環境**: 以前にデ連チームが CloudFormation テンプレートで作成済み
- **作成時期**: 2025 年 5 月頃（プロジェクト初期段階）
- **現在の状況**: 正常稼働中、最終使用日 2025-07-24

### **権限変更の経緯**

- **初期**: IAM ロール作成権限あり（CloudFormation での自動作成が可能）
- **現在**: セキュリティポリシー強化により、IAM ロール作成権限が制限
- **影響**: 新規ロール作成はインフラチーム申請が必要

### **今回の申請理由**

- **本番環境 CI/CD 構築**: 開発・検証環境と同様のシステムを本番環境に展開
- **権限制限**: 現在の Group には IAM ロール作成権限がないため申請が必要
- **既存権限**: `iam:GetRole`権限は既に Group に付与済み（既存ロール参照は可能）

## 🎯 申請内容

### **作成対象 IAM ロール**

本番環境用 IAM ロールの作成を申請いたします：

| 環境         | ロール名                   | 用途                                   | 状況            |
| ------------ | -------------------------- | -------------------------------------- | --------------- |
| 開発環境     | `cdp-dev-dlpf-deploy-role` | CodeBuild プロジェクト用サービスロール | ✅ 作成済み     |
| 検証環境     | `cdp-stg-dlpf-deploy-role` | CodeBuild プロジェクト用サービスロール | ✅ 作成済み     |
| **本番環境** | `cdp-prd-dlpf-deploy-role` | CodeBuild プロジェクト用サービスロール | 🔄 **作成要請** |

### **使用目的**

- CI/CD パイプラインでの AWS リソース自動デプロイ
- CloudFormation テンプレートによるインフラ管理
- GitHub 連携による継続的デプロイメント

## 🔐 IAM ロール仕様

### **信頼関係ポリシー**

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
```

### **アクセス許可ポリシー**

#### **1. CloudWatch Logs 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "logs:CreateLogGroup",
    "logs:CreateLogStream",
    "logs:PutLogEvents",
    "logs:DescribeLogGroups",
    "logs:DescribeLogStreams"
  ],
  "Resource": [
    "arn:aws:logs:ap-northeast-1:879381279381:log-group:/aws/codebuild/cdp-prd-dlpf-deploy",
    "arn:aws:logs:ap-northeast-1:879381279381:log-group:/aws/codebuild/cdp-prd-dlpf-deploy:*",
    "arn:aws:logs:ap-northeast-1:879381279381:log-group:/aws/vendedlogs/states/*",
    "arn:aws:logs:ap-northeast-1:879381279381:log-group::log-stream:",
    "arn:aws:logs:ap-northeast-1:879381279381:log-group:*"
  ]
}
```

#### **2. Secrets Manager 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "secretsmanager:GetSecretValue",
    "secretsmanager:CreateSecret",
    "secretsmanager:UpdateSecret",
    "secretsmanager:DeleteSecret",
    "secretsmanager:DescribeSecret",
    "secretsmanager:PutSecretValue",
    "secretsmanager:ListSecrets",
    "secretsmanager:TagResource",
    "secretsmanager:UntagResource"
  ],
  "Resource": [
    "arn:aws:secretsmanager:ap-northeast-1:*:secret:github-app-credentials*",
    "arn:aws:secretsmanager:ap-northeast-1:*:secret:dlpf*"
  ]
}
```

#### **3. Systems Manager Parameter Store 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "ssm:GetParameter",
    "ssm:GetParameters",
    "ssm:PutParameter",
    "ssm:DeleteParameter",
    "ssm:GetParametersByPath",
    "ssm:DescribeParameters",
    "ssm:AddTagsToResource",
    "ssm:RemoveTagsFromResource",
    "ssm:ListTagsForResource"
  ],
  "Resource": [
    "arn:aws:ssm:ap-northeast-1:*:parameter/dlpf/*/deploy-baseline-time",
    "arn:aws:ssm:ap-northeast-1:*:parameter/dlpf/*/notification-email",
    "arn:aws:ssm:ap-northeast-1:*:parameter/dlpf/*"
  ]
}
```

#### **4. AWS Glue 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "glue:GetJob",
    "glue:UpdateJob",
    "glue:CreateJob",
    "glue:DeleteJob",
    "glue:BatchGetJobs",
    "glue:ListJobs",
    "glue:GetConnection",
    "glue:CreateConnection",
    "glue:UpdateConnection",
    "glue:DeleteConnection",
    "glue:GetConnections"
  ],
  "Resource": [
    "arn:aws:glue:ap-northeast-1:*:job/*",
    "arn:aws:glue:ap-northeast-1:*:connection/*"
  ]
}
```

#### **5. CloudFormation 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "cloudformation:DescribeStacks",
    "cloudformation:CreateStack",
    "cloudformation:UpdateStack",
    "cloudformation:DeleteStack",
    "cloudformation:CreateChangeSet",
    "cloudformation:DescribeChangeSet",
    "cloudformation:ExecuteChangeSet",
    "cloudformation:DeleteChangeSet",
    "cloudformation:ListStacks",
    "cloudformation:ValidateTemplate",
    "cloudformation:GetTemplateSummary",
    "cloudformation:DescribeStackResources",
    "cloudformation:DescribeStackEvents",
    "cloudformation:GetTemplate"
  ],
  "Resource": [
    "arn:aws:cloudformation:ap-northeast-1:*:stack/*",
    "arn:aws:cloudformation:ap-northeast-1:*:stackset/*"
  ]
}
```

#### **6. S3 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "s3:PutObject",
    "s3:GetObject",
    "s3:ListBucket",
    "s3:DeleteObject"
  ],
  "Resource": [
    "arn:aws:s3:::aws-glue-assets-*",
    "arn:aws:s3:::aws-glue-assets-*/*",
    "arn:aws:s3:::cdp-*-dlpf-*",
    "arn:aws:s3:::cdp-*-dlpf-*/*"
  ]
}
```

#### **7. VPC・EC2 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "ec2:DescribeSecurityGroups",
    "ec2:DescribeSubnets",
    "ec2:DescribeNetworkInterfaces",
    "ec2:CreateNetworkInterface",
    "ec2:DeleteNetworkInterface",
    "ec2:DescribeVpcs",
    "ec2:DescribeDhcpOptions",
    "ec2:CreateNetworkInterfacePermission",
    "ec2:CreateSecurityGroup",
    "ec2:DeleteSecurityGroup",
    "ec2:AuthorizeSecurityGroupIngress",
    "ec2:AuthorizeSecurityGroupEgress",
    "ec2:RevokeSecurityGroupIngress",
    "ec2:RevokeSecurityGroupEgress",
    "ec2:CreateTags",
    "ec2:DeleteTags"
  ],
  "Resource": "*"
}
```

#### **8. IAM 権限（限定的）**

```json
{
  "Effect": "Allow",
  "Action": [
    "iam:PassRole",
    "iam:GetRole",
    "iam:CreateRole",
    "iam:UpdateRole",
    "iam:DeleteRole",
    "iam:AttachRolePolicy",
    "iam:DetachRolePolicy",
    "iam:PutRolePolicy",
    "iam:DeleteRolePolicy",
    "iam:GetRolePolicy",
    "iam:ListRolePolicies",
    "iam:ListAttachedRolePolicies"
  ],
  "Resource": [
    "arn:aws:iam::*:role/AWSGlueServiceRole*",
    "arn:aws:iam::*:role/service-role/AWSGlueServiceRole*",
    "arn:aws:iam::*:role/cdp-*-dlpf-*",
    "arn:aws:iam::*:role/dlpf-*",
    "arn:aws:iam::*:role/role-*-dlpf-*"
  ]
}
```

#### **9. EventBridge 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "events:PutRule",
    "events:DeleteRule",
    "events:DescribeRule",
    "events:ListRules",
    "events:PutTargets",
    "events:RemoveTargets",
    "events:ListTargetsByRule",
    "events:EnableRule",
    "events:DisableRule"
  ],
  "Resource": "arn:aws:events:ap-northeast-1:*:rule/*"
}
```

#### **10. Lambda 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "lambda:CreateFunction",
    "lambda:UpdateFunctionCode",
    "lambda:UpdateFunctionConfiguration",
    "lambda:DeleteFunction",
    "lambda:GetFunction",
    "lambda:ListFunctions",
    "lambda:AddPermission",
    "lambda:RemovePermission",
    "lambda:GetPolicy"
  ],
  "Resource": "arn:aws:lambda:ap-northeast-1:*:function:*"
}
```

#### **11. Step Functions 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "states:CreateStateMachine",
    "states:UpdateStateMachine",
    "states:DeleteStateMachine",
    "states:DescribeStateMachine",
    "states:ListStateMachines"
  ],
  "Resource": "arn:aws:states:ap-northeast-1:*:stateMachine:*"
}
```

#### **12. Cognito 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "cognito-idp:CreateUserPool",
    "cognito-idp:UpdateUserPool",
    "cognito-idp:DeleteUserPool",
    "cognito-idp:DescribeUserPool",
    "cognito-idp:ListUserPools",
    "cognito-idp:CreateUserPoolClient",
    "cognito-idp:UpdateUserPoolClient",
    "cognito-idp:DeleteUserPoolClient",
    "cognito-idp:DescribeUserPoolClient",
    "cognito-idp:ListUserPoolClients"
  ],
  "Resource": "arn:aws:cognito-idp:ap-northeast-1:*:userpool/*"
}
```

#### **13. CodeConnections 権限**

```json
{
  "Effect": "Allow",
  "Action": [
    "codestar-connections:UseConnection",
    "codestar-connections:GetConnection",
    "codestar-connections:GetConnectionToken",
    "codestar-connections:ListConnections",
    "codestar-connections:PassConnection"
  ],
  "Resource": "arn:aws:codeconnections:ap-northeast-1:*:connection/*"
}
```

#### **14. SNS 権限（開発環境のみ）**

```json
{
  "Effect": "Allow",
  "Action": ["sns:Publish", "sns:GetTopicAttributes", "sns:ListTopics"],
  "Resource": "*"
}
```

## 🔒 セキュリティ考慮事項

### **最小権限の原則**

- 各権限は必要最小限に制限
- リソース ARN による細かい制御
- 環境別の権限分離

### **本番環境特有の制約**

- SNS 権限は本番環境では付与しない
- CloudFormation スタック削除権限の制限検討
- 監査ログの詳細記録

## 📅 作業スケジュール

| 項目                            | 予定日     | 担当           | 備考                |
| ------------------------------- | ---------- | -------------- | ------------------- |
| IAM ロール作成申請              | 2025-07-22 | TIS 黄         | 本申請書            |
| インフラチーム審査              | 2025-07-23 | インフラチーム | 本番環境のみ        |
| IAM ロール作成                  | 2025-07-24 | インフラチーム | 本番環境のみ        |
| CloudFormation テンプレート修正 | 2025-07-25 | TIS 黄         | ✅ 完了済み         |
| 動作確認                        | 2025-07-26 | TIS 黄         | 本番環境 CI/CD 構築 |

## 📝 **補足事項**

### **技術的な準備状況**

- **CloudFormation テンプレート**: 既存ロール参照方式に修正済み ✅
- **パラメータファイル**: 本番環境用設定完了 ✅
- **デプロイスクリプト**: 全環境統一アプローチで準備完了 ✅

### **作成後の確認事項**

- ロール作成後、即座に CI/CD システム構築テスト実施予定
- 開発・検証環境と同等の機能確認を実施

## 📞 連絡先

**申請者**: TIS 黄
**メール**: <EMAIL>
**プロジェクト**: DLPF CI/CD システム構築

---

**承認欄**

| 役職                 | 氏名 | 承認日 | 署名 |
| -------------------- | ---- | ------ | ---- |
| 技術責任者           |      |        |      |
| インフラチーム責任者 |      |        |      |
| セキュリティ責任者   |      |        |      |
