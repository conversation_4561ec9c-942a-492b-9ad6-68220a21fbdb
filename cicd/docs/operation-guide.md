# デプロイシステム運用ガイド

本ガイドは、当システムのデプロイシステムの運用方法について説明します。

## 1. システム概要

本システムは以下のコンポーネントで構成されています：

1. **CodeBuildプロジェクト**: デプロイ処理を実行します
2. **Parameter Store**: 前回デプロイ基準時刻を保存します
3. **デプロイスクリプト**: CodeBuildプロジェクトを手動で起動します
4. **GitHub CLI**: PRマージ履歴を取得します
5. **AWSリソース**: CloudFormationテンプレートやGlueジョブなどをデプロイします
6. **SNSトピック**: 開発環境のみ、エラー通知を送信します

```mermaid
flowchart LR
    User[ユーザー] --> Script[デプロイスクリプト]
    Script --> CodeBuild[CodeBuild<br>デプロイプロジェクト]
    CodeBuild --> GitHub[GitHub API]
    CodeBuild --> SSM[Parameter Store]
    CodeBuild --> AWS[AWSリソース<br>デプロイ]
    CodeBuild --> PRComment[PRコメント追加]
    CodeBuild -->|エラー発生時<br>開発環境のみ| SNS[SNSトピック]
    SNS -->|メール通知| User
```

## 2. 環境別の設定

本システムは開発環境、検証環境、本番環境で別々に設定されています：

| 項目 | 開発環境 | 検証環境 | 本番環境 |
|-----|---------|---------|---------|
| 対象ブランチ | develop | release | master |
| CodeBuildプロジェクト | cdp-dev-dlpf-deploy | cdp-stg-dlpf-deploy | cdp-prd-dlpf-deploy |
| Parameter Store | /dlpf/dev/deploy-baseline-time<br>/dlpf/dev/notification-email | /dlpf/stg/deploy-baseline-time | /dlpf/prd/deploy-baseline-time |
| GitHub App認証情報 | github-app-credentials | github-app-credentials | github-app-credentials |
| SNSトピック | cdp-dev-dlpf-deploy-error-notification | なし | なし |
| エラー通知 | 有効 | 無効 | 無効 |

## 3. 日常運用手順

### 3.1 デプロイの実行方法

デプロイは以下の3つの方法で実行できます：

#### 3.1.0 定時自動デプロイ（推奨・運用中）

開発環境では、EventBridge Schedulerによる定時自動デプロイが稼働中です：

- **実行スケジュール**: 毎日9:00〜19:00の毎時00分（1時間ごと）
- **対象環境**: 開発環境（dev）のみ
- **対象ブランチ**: develop
- **実行内容**: 前回デプロイ以降にマージされたPRを自動検出・デプロイ

**定時自動デプロイの確認方法**:
```bash
# EventBridge Ruleの確認
aws events describe-rule --name hourly_build

# 最新の実行履歴確認
aws codebuild list-builds-for-project --project-name cdp-dev-dlpf-deploy --sort-order DESCENDING

# 実行ログの確認
aws logs describe-log-streams --log-group-name "/aws/codebuild/dlpf-cicd-codebuild-deploy" --order-by LastEventTime --descending --max-items 5
```

**定時自動デプロイの動作**:
- マージされたPRがある場合: 自動デプロイ実行
- マージされたPRがない場合: "No new merged PRs to deploy" でスキップ
- エラー発生時: SNS通知でメール送信（開発環境のみ）

#### 3.1.1 AWSコンソールからの手動実行

1. AWSコンソールにログインし、CodeBuildサービスに移動
2. 該当するCodeBuildプロジェクト（`cdp-dev-dlpf-deploy`または`cdp-stg-dlpf-deploy`）を選択
3. 「ビルドの開始」ボタンを押下
4. ビルドの進行状況とログをリアルタイムで確認
5. ビルド完了後、結果（成功/失敗）を確認
6. エラーが発生した場合：
   - デプロイ対象資材に起因するエラー → 業務ロジック担当者に修正を依頼
   - CodeBuildプロジェクト自体の不具合 → デプロイプロジェクト担当者に対応を依頼

#### 3.1.2 コマンドラインからの実行（デバッグ用）

以下のデプロイスクリプトを使用して、デプロイを実行します：

```bash
# 開発環境に最後のデプロイ以降のすべてのPRをデプロイする場合
./cicd/scripts/deploy.sh dev

# 検証環境に最後のデプロイ以降のすべてのPRをデプロイする場合
./cicd/scripts/deploy.sh stg

# 特定の時刻以降のPRをデプロイする場合
./cicd/scripts/deploy.sh dev "2025-05-27T10:00:00Z"

# DRY-RUNモードで実行（実際のデプロイは行わず、ログのみ出力）
./cicd/scripts/deploy.sh dev --dry-run
```

### 3.2 特定時刻以降のPRをデプロイ

特定時刻以降のPRをデプロイする場合は、以下のようにdeploy_baseline_timeを指定します：

```bash
# 開発環境に2025年5月27日15時以降のPRをデプロイする場合
./cicd/scripts/deploy.sh dev "2025-05-27T15:00:00Z"

# 検証環境に特定日以降のすべてのPRをデプロイする場合
./cicd/scripts/deploy.sh stg "2025-05-20T00:00:00Z"
```

### 3.3 デプロイ状況の確認

1. AWSコンソールにログインし、CodeBuildサービスに移動
2. 該当するCodeBuildプロジェクト（`cdp-dev-dlpf-deploy`または`cdp-stg-dlpf-deploy`）を選択
3. ビルド履歴からデプロイの進行状況を確認
4. ビルドログでデプロイの詳細を確認

### 3.4 デプロイ履歴の確認

Parameter Storeに保存されている前回デプロイ基準時刻を確認します：

```bash
# 開発環境の前回デプロイ基準時刻を確認
aws ssm get-parameter --name "/dlpf/dev/deploy-baseline-time" --query "Parameter.Value" --output text

# 検証環境の前回デプロイ基準時刻を確認
aws ssm get-parameter --name "/dlpf/stg/deploy-baseline-time" --query "Parameter.Value" --output text

# JSON形式で見やすく表示
aws ssm get-parameter --name "/dlpf/dev/deploy-baseline-time" --query "Parameter.Value" --output text | jq .
```

## 4. トラブルシューティング

### 4.1 デプロイが失敗する場合

1. CodeBuildログでエラーメッセージを確認します
2. 一般的なエラー原因：
   - GitHub App認証情報の問題
   - IAM権限の不足
   - デプロイ対象のPRが存在しない
   - デプロイ処理自体のエラー
   - Parameter Storeの値が不正

対応策：
- エラーの原因を特定し、修正します
- 特定の時刻以降からデプロイを再実行します（deploy_baseline_timeを調整）
- Parameter Storeの値が不正な場合は、正しい形式に修正します（詳細は「4.4 Parameter Storeの値が不正な場合」を参照）

### 4.1.0 エラーハンドリングの仕組み

デプロイスクリプトには以下のエラーハンドリング機能が実装されています：

1. スクリプト内でエラーが発生した場合、即座に処理を中断し、エラーメッセージをログに出力
2. エラーログはAWS CloudWatchに保存され、後から参照可能
3. CodeBuildプロジェクト自体がエラー終了するため、デプロイの失敗が明確に通知される
4. 開発環境では、エラー発生時にSNSトピックを通じてメール通知が送信される
   - エラーメッセージ、ビルド情報、CodeBuildコンソールへのリンクが含まれる
   - 検証環境と本番環境では通知機能は無効

エラーハンドリングの動作確認方法：
```bash
# 意図的にエラーを発生させる例（存在しないPR番号を指定）
./cicd/scripts/deploy.sh dev 999999 999999

# 存在しないシークレットを指定してエラーを発生させる例
GITHUB_APP_SECRET_NAME=non-existent-secret ./cicd/scripts/deploy.sh dev

# SNS通知機能のテスト（開発環境のみ）
./cicd/scripts/utils/test-sns-notification.sh <SNSトピックARN>
```

### 4.1.1 初回デプロイの場合

初回デプロイの場合は、以下の点に注意してください：

1. 初回デプロイでは、すべてのマージ済みPRが自動的にデプロイ対象に設定されます
2. Parameter Storeに値が存在しない場合、deploy_baseline_time = "1970-01-01T00:00:00Z"として処理されます
3. 次回デプロイ時の基準点として、現在時刻がParameter Storeに保存されます

```bash
# 初回デプロイを実行する場合
./cicd/scripts/deploy.sh dev
```

### 4.2 特定のPRをスキップする方法

問題のあるPRをスキップしてデプロイを続行する場合は、以下の方法を実行します：

```bash
# 問題のあるPRより後の時刻を指定してスキップする例
# （例：PR #105が2025-05-27T16:00:00Zにマージされた場合、その後の時刻を指定）
./cicd/scripts/deploy.sh dev "2025-05-27T16:30:00Z"

# または、Parameter Storeの値を手動で調整
aws ssm put-parameter --name "/dlpf/dev/deploy-baseline-time" \
  --value '{"deploy_baseline_time": "2025-05-27T16:30:00Z"}' \
  --type "String" --overwrite
```

### 4.3 デプロイ状態のリセット

デプロイ状態をリセットする必要がある場合は、以下のコマンドを実行します：

```bash
# 開発環境のデプロイ状態をリセットする場合（最初からデプロイ）
aws ssm put-parameter --name "/dlpf/dev/deploy-baseline-time" \
  --value '{"deploy_baseline_time": "1970-01-01T00:00:00Z"}' \
  --type "String" --overwrite

# その後、通常のデプロイを実行します
./cicd/scripts/deploy.sh dev
```

> **重要**: デプロイ状態のリセットは慎重に行ってください。時刻を"1970-01-01T00:00:00Z"にリセットすると、すべてのPRが再度デプロイ対象になります。これにより、不要なリソース消費や予期しない副作用が発生する可能性があります。

### 4.4 Parameter Storeの値が不正な場合

Parameter Storeの値が不正な場合、デプロイが失敗します。以下のエラーメッセージが表示される場合があります：

- `Parameter Storeから値を取得できませんでした`
- `Parameter Storeの値が有効なJSON形式ではありません`
- `Parameter Storeの値に有効な'deploy_baseline_time'フィールドがありません`
- `deploy_baseline_time (***) が有効な日時形式ではありません`

これらのエラーが発生した場合、Parameter Storeの値を正しい形式に修正する必要があります：

```bash
# Parameter Storeの現在の値を確認
aws ssm get-parameter --name "/dlpf/dev/deploy-baseline-time" --query "Parameter.Value" --output text

# 正しい形式で値を更新（例：2025年5月27日10時以降をデプロイ基準とする場合）
aws ssm put-parameter --name "/dlpf/dev/deploy-baseline-time" \
  --value '{"deploy_baseline_time": "2025-05-27T10:00:00Z"}' \
  --type "String" --overwrite
```

正しい形式は以下の通りです：
- JSON形式であること
- `deploy_baseline_time`フィールドが含まれていること（ISO 8601形式の日時）

> **重要**: Parameter Storeの値を手動で変更する場合は、特に`deploy_baseline_time`フィールドの値を慎重に設定してください。この値は、次回デプロイ時にどの時刻以降のPRをデプロイ対象とするかを決定します。誤って古い時刻（特に"1970-01-01T00:00:00Z"）に設定すると、既にデプロイ済みのPRが再度デプロイされる可能性があります。

### 4.5 CodeBuild権限エラーの対応

2025-05-27にCodeBuildの権限不足によるAccessDeniedエラーが発生し、包括的な権限修正を実施しました。

#### 4.5.1 典型的なエラーメッセージ

```
An error occurred (AccessDenied) when calling the GetTemplateSummary operation:
User: arn:aws:sts::886436956581:assumed-role/cdp-dev-dlpf-deploy-role/AWSCodeBuild-xxx
is not authorized to perform: cloudformation:GetTemplateSummary
```

#### 4.5.2 対応済み権限

以下の権限が追加され、業務用CloudFormationテンプレートのデプロイが可能になりました：

- **CloudFormation**: GetTemplateSummary, DescribeStackResources, DescribeStackEvents, GetTemplate
- **IAM**: ロール管理の包括的権限
- **EventBridge**: ルール・ターゲット管理
- **Lambda**: 関数管理・権限設定
- **Step Functions**: ステートマシン管理
- **Cognito**: ユーザープール管理
- **Secrets Manager**: シークレット管理（dlpf*パターン）
- **EC2**: セキュリティグループ管理
- **Systems Manager**: パラメータストア拡張
- **Glue**: ジョブ・コネクター管理

#### 4.5.3 権限エラー発生時の対応手順

1. **エラーログの確認**:
   ```bash
   # CodeBuildログでエラーメッセージを確認
   aws logs describe-log-groups --log-group-name-prefix "/aws/codebuild/cdp-dev-dlpf-deploy"
   ```

2. **権限不足の特定**:
   - エラーメッセージから不足している権限を特定
   - 対象AWSサービスとアクションを確認

3. **CloudFormationテンプレート更新**:
   ```bash
   # CodeBuild権限テンプレートを更新
   cd cicd/cloudformation
   ./scripts/cfn_deploy.sh -e dev -n -y codebuild cicd-codebuild-deploy
   ```

4. **権限反映の確認**:
   - CodeBuildを再実行して権限エラーが解決されたことを確認

## 5. メンテナンス手順

### 5.1 CodeBuildプロジェクトの更新

CloudFormationテンプレートを更新し、デプロイします：

```bash
# 開発環境の場合
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e dev -y codebuild cicd-codebuild-deploy

# 検証環境の場合
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e stg -y codebuild cicd-codebuild-deploy
```

### 5.2 GitHub App認証情報の更新

1. AWS Secrets Managerで認証情報を更新
2. 必要に応じてCodeBuildプロジェクトを再デプロイ

### 5.3 通知メールアドレスの更新

1. Parameter Storeで通知メールアドレスを更新
   ```bash
   # 開発環境の通知メールアドレスを更新
   aws ssm put-parameter --name "/dlpf/dev/notification-email" --value "<EMAIL>" --type "String" --overwrite
   ```
2. SNSトピックの購読を更新（必要に応じて）
   - 古いメールアドレスの購読を削除
   - 新しいメールアドレスで購読を追加

### 5.4 ビルド仕様の更新

1. `cicd/buildspec/deploy-buildspec.yml`を編集
2. 変更をコミットしてPRを作成
3. PRをマージ
4. 必要に応じてCodeBuildプロジェクトを再デプロイ

### 5.5 AWS リソースの管理

AWS リソースは種類別に分けて専用のCloudFormationテンプレートで管理されています：

#### 5.5.1 Parameter Storeリソース

Parameter Storeリソースは以下のテンプレートで管理されています：

```bash
# Parameter Storeリソースを初期作成する場合（新環境構築時のみ）
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e dev -y -n system-manager cicd-parameter-store

# ステージング環境の場合
./scripts/cfn_deploy.sh -e stg -y -n system-manager cicd-parameter-store

# 本番環境の場合
./scripts/cfn_deploy.sh -e prd -y -n system-manager cicd-parameter-store
```

> **重要**: このテンプレートは初期設定時に一度だけデプロイしてください。再デプロイすると、Parameter Storeの値が初期値にリセットされる可能性があります。

#### 5.5.2 SNSトピックリソース

SNSトピックリソースは以下のテンプレートで管理されています：

```bash
# SNSトピックリソースを初期作成する場合（新環境構築時のみ）
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e dev -y -n sns cicd-notification-topic

# ステージング環境の場合
./scripts/cfn_deploy.sh -e stg -y -n sns cicd-notification-topic

# 本番環境の場合
./scripts/cfn_deploy.sh -e prd -y -n sns cicd-notification-topic
```

> **重要**: このテンプレートは初期設定時に一度だけデプロイしてください。再デプロイすると、SNSトピックの購読が再作成される可能性があります。

#### 5.5.3 CodeBuildリソース

CodeBuildリソースは以下のテンプレートで管理されています：

```bash
# CodeBuildリソースをデプロイする場合
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e dev -y -n codebuild cicd-codebuild-deploy

# ステージング環境の場合
./scripts/cfn_deploy.sh -e stg -y -n codebuild cicd-codebuild-deploy

# 本番環境の場合
./scripts/cfn_deploy.sh -e prd -y -n codebuild cicd-codebuild-deploy
```

#### 5.5.4 リソース管理の特徴

これらのテンプレートは以下の特徴を持っています：

1. **リソースの種類別管理**:
   - リソースを種類別に分けて管理することで、一部のリソースを変更しても他のリソースに影響を与えません
   - 特に、Parameter StoreやSNSトピックなどの状態を保持するリソースは、再作成されると値がリセットされる問題を防ぎます

2. **DeletionPolicy: Retain**:
   - 重要なリソースにはDeletionPolicy: Retainを設定し、スタックが削除されてもリソースは保持されます

3. **UpdateReplacePolicy: Retain**:
   - 重要なリソースにはUpdateReplacePolicy: Retainを設定し、リソースが置き換えられる場合でも元のリソースは保持されます

4. **初期値のみを設定**:
   - Parameter Storeなどのテンプレートは初期値のみを設定し、その後の値の更新はデプロイスクリプトが行います

## 6. 監視とアラート

### 6.1 監視すべき項目

1. **定時自動デプロイの実行状況**
   - EventBridge Ruleの有効性
   - 定時実行の成功/失敗状況
   - デプロイ対象PRの検出状況

2. **CodeBuildプロジェクトのビルド状態**
   - ビルドの成功/失敗率
   - ビルド実行頻度
   - ビルド所要時間

3. **デプロイの成功/失敗率**
   - 自動デプロイの成功率
   - 手動デプロイの成功率
   - エラーパターンの分析

### 6.2 定時自動デプロイの監視

#### 6.2.1 実行状況の確認

```bash
# 今日の定時実行履歴確認
aws codebuild list-builds-for-project --project-name cdp-dev-dlpf-deploy \
  --sort-order DESCENDING | jq '.ids[0:10]'

# 特定ビルドの詳細確認
aws codebuild batch-get-builds --ids <BUILD_ID> \
  --query 'builds[0].{status:buildStatus,startTime:startTime,endTime:endTime,duration:durationInSeconds}'

# EventBridge Ruleの状態確認
aws events describe-rule --name hourly_build
```

#### 6.2.2 エラー通知の確認

開発環境では、以下の場合にSNS経由でメール通知が送信されます：

- **CodeBuild実行エラー**: CodeBuild自体の失敗
- **デプロイ失敗**: CloudFormationテンプレートのデプロイ失敗
- **認証エラー**: GitHub App認証の問題

#### 6.2.3 ログ監視（二宮さん担当）

CloudWatchログの「デプロイ失敗」キーワード監視により、以下の場合に追加通知が送信されます：

- 業務用CloudFormationテンプレートのデプロイ失敗
- 特定ファイルのデプロイエラー
- その他の重要なデプロイ関連エラー

### 6.3 アラート設定

現在設定済みのアラート：

1. **CodeBuildプロジェクトのビルド失敗時のアラート**（開発環境のみ）
   - SNSトピック: `cdp-dev-dlpf-deploy-error-notification`
   - 通知内容: エラーメッセージ、ビルド情報、コンソールリンク

2. **デプロイ失敗時のアラート**（二宮さんのLambda監視）
   - CloudWatchログのキーワード監視
   - 「デプロイ失敗」キーワード検出時にメール通知

推奨する追加アラート：

1. **定時実行の停止検知**
   - 2時間以上定時実行がない場合のアラート
2. **デプロイ所要時間の異常検知**
   - 通常の3倍以上の時間がかかった場合のアラート

## 7. バックアップと復旧

### 7.1 バックアップ対象

1. CloudFormationテンプレート
2. GitHub App認証情報
3. Parameter Storeの内容

### 7.2 復旧手順

1. CloudFormationテンプレートを使用してインフラを再作成
2. GitHub App認証情報を復元
3. 必要に応じてParameter Storeの内容を復元

## 8. セキュリティ上の考慮事項

1. GitHub App認証情報は常にAWS Secrets Managerで管理
2. IAMロールには最小権限の原則を適用
3. CodeBuildプロジェクトはVPC内で実行（GitHub App認証のための固定IP対応）
4. GitHub Appの権限は必要最小限に設定
5. GitHub APIへのアクセスにはプロキシ設定が不要（インフラチームの確認済み）

## 9. GitHub Actions無効化手順

既存のGitHub Actionsワークフローを無効化するには、以下の手順を実行します：

1. GitHubリポジトリの設定ページに移動
2. 「Actions」タブを選択
3. 「General」セクションで「Actions permissions」を確認
4. 「Disable actions」または「Disable all actions」を選択して保存

または、特定のワークフローファイルを無効化する場合：

1. `.github/workflows/aws-deploy-with-codebuild.yml`と`.github/workflows/pr-event-collector.yml`ファイルを編集
2. ファイルの先頭に以下のコメントを追加して無効化：

```yaml
# DISABLED: このワークフローは新しいデプロイシステムに置き換えられました
# name: AWS Deploy with CodeBuild
```

## 10. 最近の変更点

1. **定時自動デプロイシステムの稼働開始**（2025-06-03）:
   - EventBridge Scheduler（hourly_build）による定時自動デプロイが稼働中
   - 毎日9:00〜19:00の毎時00分に自動実行
   - 開発環境（dev）でのマージ済みPRの自動検出・デプロイ機能が動作中

2. **GitHubラベル方式の実装完了**（2025-06-03）:
   - PR追越し問題を解決するGitHubラベル方式を実装
   - `deployed`ラベルによるデプロイ済みPRの管理
   - Parameter Store依存からの段階的移行完了

3. **Lambda自動デプロイ機能の実装完了**（2025-06-03）:
   - Lambda関数の変更検出・自動デプロイ機能を実装
   - Lambda関数変更→PR作成→自動デプロイの一連の流れを検証完了

4. **Parameter Storeのエラーハンドリング強化**:
   - Parameter Storeの値が不正な場合のエラーハンドリングを改善
   - 自動的にPR番号を0にリセットしないように修正
   - PR番号の重要性を考慮し、より安全な処理を実装

5. **Parameter Store用の専用CFNテンプレート作成**:
   - Parameter Store用の専用CloudFormationテンプレートを作成
   - DeletionPolicyとUpdateReplacePolicyを設定して既存の値を保護
   - 初期設定時に一度だけデプロイする運用方法を導入

6. **プロキシ関連コードの削除**:
   - GitHub APIアクセス時のプロキシ関連コードを削除
   - インフラチームからのフィードバックに基づき、GitHub APIへのアクセスにはプロキシが不要であることを明示

7. **PR #0〜0 の処理の修正**:
   - 初回デプロイ（PR #0〜0）の場合は、すべてのデプロイ対象が自動的にTRUEに設定
   - get-merged-prs.shは呼び出されず、最新のPRが追跡用に設定
   - 次回デプロイ時の基準点として、最新のPRがParameter Storeに保存

8. **既存のCodeBuildランナーの削除**:
   - GitHub Actions用のself-hostedランナーとして使用していたCodeBuildプロジェクトを削除
   - 関連するCloudFormationスタックも削除

## 11. 時刻ベース管理運用

### 11.1 時刻ベース管理の概要

PR追越し問題を解決するため、時刻ベース管理方式を導入しました。

#### 11.1.1 基本方針

- **時刻ベース管理**: PR番号ではなく、デプロイ基準時刻で管理
- **確実性**: PR番号の大小関係に依存しない
- **直感性**: 「X時刻以降の変更をデプロイ」という明確な概念

#### 11.1.2 運用方法

```bash
# Deploy対象PR確認（dev環境）
deploy_baseline_time=$(aws ssm get-parameter --name "/dlpf/dev/deploy-baseline-time" --query "Parameter.Value" --output text | jq -r '.deploy_baseline_time')
gh pr list --state merged --base develop --json number,title,mergedAt \
  --jq ".[] | select(.mergedAt >= \"${deploy_baseline_time}\")"

# 特定時刻以降のPR確認
gh pr list --state merged --base develop --json number,title,mergedAt \
  --jq ".[] | select(.mergedAt >= \"2025-05-27T10:00:00Z\")"

# 手動時刻調整（緊急時）
aws ssm put-parameter --name "/dlpf/dev/deploy-baseline-time" \
  --value '{"deploy_baseline_time": "2025-05-27T15:00:00Z"}' \
  --type "String" --overwrite
```

#### 11.1.3 利点

1. **追越し問題の完全解決**: PR番号に依存しない管理
2. **直感的運用**: 時刻ベースの明確な概念
3. **運用柔軟性**: 人間による手動時刻調整も可能

### 11.2 課題管理プロセス

システム運用で発見された課題は `cicd/docs/issues-list.md` で管理されます。

#### 11.2.1 課題解決フロー

1. **課題特定** → issues-list.mdに追加
2. **影響分析** → 優先度・影響範囲の評価
3. **対応方針検討** → 技術的解決策の検討
4. **方針決定** → 実装方式の確定
5. **ToDoList移行** → 具体的タスクとして`ToDoList.md`に移動
6. **実装・テスト** → 開発作業実施
7. **課題クローズ** → 解決済みセクションに移動

#### 11.2.2 現在の主要課題

- **課題 #001**: Deploy済PRのラベル管理
- **課題 #002**: PR追越し問題（時刻ベース管理方式で解決済み）
- **課題 #003**: 削除ファイルの複雑パターン対応

## 12. 参考資料

1. [デプロイシステム設計書](deployment-system.md)
2. [課題管理リスト](issues-list.md)
3. [デプロイルール仕様書](deployment-rules.md)
4. [AWS CodeBuild ドキュメント](https://docs.aws.amazon.com/codebuild/latest/userguide/welcome.html)
5. [GitHub API ドキュメント](https://docs.github.com/en/rest)
6. [AWS Systems Manager Parameter Store ドキュメント](https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-parameter-store.html)
