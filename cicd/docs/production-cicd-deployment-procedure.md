# 本番環境 CI/CD システム構築 操作手順書

## 📋 概要

**対象環境**: 本番環境（prd）
**目的**: 本番環境 CI/CD システムの構築・稼働開始
**重要度**: 🔴 **最高** - 本番環境への影響を考慮した慎重な作業が必要

## ⚠️ 重要な前提条件

### 🔴 **本番環境の特性と制約事項**

#### **基本方針**

- **手動リリースのみ**: 自動デプロイメカニズムは使用しない
- **最小権限の原則**: 必要最小限の権限のみ付与
- **承認プロセス**: デプロイ前の多段階承認が必要
- **監査ログ**: すべての操作を詳細に記録

#### **環境固有設定**

| 項目                   | 本番環境設定                   |
| ---------------------- | ------------------------------ |
| 対象ブランチ           | **master** ⚠️                    |
| CodeBuild プロジェクト | cdp-prd-dlpf-deploy            |
| Parameter Store        | /dlpf/prd/deploy-baseline-time |
| GitHub App 認証情報    | github-app-credentials         |
| SNS トピック           | なし（エラー通知無効）         |
| エラー通知             | 無効                           |
| 自動デプロイ           | 無効（手動実行のみ）           |
| AWS Account ID         | ************                   |

### 🔐 **セキュリティ要件**

- VPC 内での CodeBuild 実行（プライベートサブネット）
- 最小権限 IAM ポリシー
- CloudTrail による全操作ログ記録
- Secrets Manager での認証情報管理

### 📋 **必要な権限・アクセス**

- AWS 本番環境アカウント（************）へのアクセス権限
- CloudFormation スタック作成権限
- IAM ロール・ポリシー作成権限
- Secrets Manager 操作権限
- 本番環境 VPC の設定情報

## 🎯 実施手順

### **Phase 0: セキュリティルーム事前準備**

#### **Step 0-1: AWS CLI 環境の確認・インストール**

```bash
# AWS CLIのインストール確認
aws --version

# インストールされていない場合
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

#### **Step 0-2: AWS 接続情報の設定**

```bash
# プロジェクトディレクトリに移動
cd ~/works/tis-dlpf-app

# AWS環境設定ファイルの解凍（パスワード: cicd-******!）
unzip cicd/docs/aws-environments.zip -d ~/
# パスワード入力プロンプトで「cicd-******!」を入力

# 解凍内容の確認
ls -la ~/.aws.*

# 本番環境に切り替え
rm ~/.aws 2>/dev/null || true
ln -s ~/.aws.prd ~/.aws

# 接続確認
aws sts get-caller-identity
# 期待値: Account ID ************ (本番環境)
```

**📝 重要**: ZIP ファイルのパスワードは `cicd-******!` です。

### **Phase 1: 環境準備・確認**

#### **Step 1-1: AWS 環境切り替え**

```bash
# 本番環境に切り替え
cd ~/works/tis-dlpf-app
rm ~/.aws
ln -s ~/.aws.prd ~/.aws

# 環境確認
aws sts get-caller-identity
# 期待値: Account ID ************ (本番環境)
```

#### **Step 1-2: 既存リソース確認**

```bash
# 本番環境パラメータファイルの確認
ls -la cicd/cloudformation/environments/prd/parameters/
cat cicd/cloudformation/environments/prd/parameters/default.json

# VPC確認（本番環境のVPC IDを確認）
aws ec2 describe-vpcs --filters "Name=tag:Environment,Values=prd" --query 'Vpcs[0].{VpcId:VpcId,State:State}'

# CodeConnections確認（本番環境のCodeConnections ARNを確認）
aws codeconnections list-connections --provider-type GitHub --query 'Connections[?ConnectionStatus==`AVAILABLE`]'
```

#### **Step 1-3: 本番環境パラメータファイルの設定確認**

```bash
# CodeBuildパラメータファイルの内容確認
cat cicd/cloudformation/environments/prd/parameters/codebuild/default.json

# 必要な値が設定されていることを確認:
# - VpcId: 本番環境のVPC ID
# - SubnetIds: プライベートサブネットのID（カンマ区切り）
# - SecurityGroupIds: 適切なセキュリティグループID
# - CodeConnectionsArn: 本番環境のCodeConnections ARN
```

### **Phase 2: GitHub App 認証情報設定**

#### **Step 2-1: 検証環境から認証情報取得**

```bash
# 一時的に検証環境に切り替え
rm ~/.aws
ln -s ~/.aws.stg ~/.aws

# 認証情報取得（出力をメモ）
aws secretsmanager get-secret-value --secret-id github-app-credentials --query 'SecretString' --output text
```

**📝 取得した認証情報をメモ**:

```json
{
  "app_id": "[取得した値をメモ]",
  "installation_id": "[取得した値をメモ]",
  "private_key": "[取得した値をメモ]"
}
```

#### **Step 2-2: 本番環境に認証情報設定**

```bash
# 本番環境に切り替え
rm ~/.aws
ln -s ~/.aws.prd ~/.aws

# 認証情報をSecrets Managerに設定
aws secretsmanager create-secret \
  --name github-app-credentials \
  --description "GitHub App credentials for production environment" \
  --secret-string '{"app_id":"[Step2-1で取得した値]","installation_id":"[Step2-1で取得した値]","private_key":"[Step2-1で取得した値]"}'
```

#### **Step 2-3: 設定確認**

```bash
# 設定確認
aws secretsmanager describe-secret --secret-id github-app-credentials
# 期待値: Name: "github-app-credentials", ARN確認
```

### **Phase 3: CloudFormation スタックデプロイ**

#### **Step 3-1: デプロイ対象テンプレート確認**

**デプロイ対象**: 2 つのテンプレート

1. `cicd-codebuild-deploy.yml` - CodeBuild プロジェクト
2. `cicd-system-manager-parameter-store.yml` - Parameter Store

**対象外**:

- `cicd-sns-notification-topic.yml` (本番環境では通知機能無効)

#### **Step 3-2: 既存手順書に従ったデプロイ**

```bash
# 手順書の場所確認
ls -la cicd/cloudformation/docs/CloudFormation手動デプロイ手順書.md

# 手順書に従ってデプロイ実行
# 1. Parameter Store テンプレート
cd cicd/cloudformation
./scripts/cfn_deploy.sh -e prd -n system-manager parameter-store

# 2. CodeBuild テンプレート
./scripts/cfn_deploy.sh -e prd -n codebuild cicd-codebuild-deploy
```

#### **Step 3-3: デプロイ完了確認**

```bash
# CloudFormationスタック確認
aws cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE --query 'StackSummaries[?contains(StackName, `prd`) && contains(StackName, `dlpf`)].StackName'

# CodeBuildプロジェクト確認
aws codebuild list-projects --query 'projects[?contains(@, `prd`) && contains(@, `dlpf`)]'

# Parameter Store確認
aws ssm get-parameter --name "/dlpf/prd/deploy-baseline-time"
```

### **Phase 4: 動作確認テスト**

#### **Step 4-1: DRY-RUN モード動作確認**

```bash
# ⚠️ 重要: DRY-RUNモードで実行（実際のデプロイは行わない）
aws codebuild start-build \
  --project-name cdp-prd-dlpf-deploy \
  --environment-variables-override '[{"name":"DRY_RUN","value":"true","type":"PLAINTEXT"}]'

# または、デプロイスクリプトを使用（推奨）
./cicd/scripts/deploy.sh prd --dry-run
```
**⚠️ 注意**:

- `aws codebuild start-build --project-name cdp-prd-dlpf-deploy` だけでは**実際のデプロイが実行**されます
- DRY-RUN モードにするには `DRY_RUN=true` の環境変数設定が必須です

#### **Step 4-2: 実行ログ確認**
```bash
# ビルド実行状況確認
aws codebuild list-builds-for-project --project-name cdp-prd-dlpf-deploy --sort-order DESCENDING

# ログ確認（CloudWatch Logs）
aws logs describe-log-groups --log-group-name-prefix "/aws/codebuild/dlpf-prd"
```
#### **Step 4-3: 期待される動作**

- ✅ **master**ブランチからのソース取得
- ✅ GitHub API 接続成功
- ✅ DRY-RUN モードでの実行（実際のデプロイは行わない）
- ✅ ログ出力正常

## 🔍 トラブルシューティング

### **認証エラーの場合**
```bash
# Secrets Manager確認
aws secretsmanager get-secret-value --secret-id github-app-credentials

# GitHub App設定確認（検証環境と同じ設定か）
```
### **CodeBuild 実行エラーの場合**
```bash
# CloudWatch Logsでエラー詳細確認
aws logs filter-log-events --log-group-name "/aws/codebuild/dlpf-prd-dlpf-deploy" --start-time $(date -d '1 hour ago' +%s)000
```
### **VPC 接続エラーの場合**
```bash
# ネットワーク設定の確認
aws ec2 describe-route-tables --filters "Name=vpc-id,Values=<vpc-id>"
```
## ✅ 完了チェックリスト

### **Phase 0: セキュリティルーム事前準備**

- [ ] AWS CLI 環境の確認・インストール完了
- [ ] AWS 接続情報の解凍・設定完了
- [ ] 本番環境への接続確認完了

### **Phase 1: 環境準備**

- [ ] AWS 環境切り替え完了
- [ ] 既存リソース確認完了
- [ ] 本番環境パラメータファイル設定確認完了

### **Phase 2: 認証情報設定**

- [ ] 検証環境から認証情報取得完了
- [ ] 本番環境に認証情報設定完了
- [ ] 設定確認完了

### **Phase 3: CloudFormation デプロイ**

- [ ] Parameter Store テンプレートデプロイ完了
- [ ] CodeBuild テンプレートデプロイ完了
- [ ] デプロイ完了確認

### **Phase 4: 動作確認**

- [ ] DRY-RUN モード実行完了
- [ ] ログ確認完了
- [ ] 期待動作確認完了

## 🚀 本番環境デプロイ手順（構築後）

### **事前準備**

#### **デプロイ対象の確認**
```bash
# masterブランチの最新状態確認
git checkout master
git pull origin master

# デプロイ対象PRの確認
./cicd/scripts/deploy.sh prd --dry-run
```
#### **承認プロセス**

1. **技術責任者による承認**

   - デプロイ対象の変更内容レビュー
   - 影響範囲の確認
   - ロールバック計画の確認

2. **運用責任者による承認**
   - デプロイタイミングの承認
   - 業務影響の確認
   - 緊急連絡体制の確認

### **デプロイ実行**
```bash
# 本番環境への手動デプロイ（実際のデプロイを実行）
./cicd/scripts/deploy.sh prd "2025-01-01T00:00:00Z"

# または特定時刻以降のPRをデプロイ
./cicd/scripts/deploy.sh prd "2025-07-20T10:00:00Z"

# 事前確認（DRY-RUNモード）
./cicd/scripts/deploy.sh prd "2025-07-20T10:00:00Z" --dry-run
```

**⚠️ 重要**:

- 上記コマンドは**実際のデプロイを実行**します
- 事前に必ず DRY-RUN モードで動作確認を行ってください
承認プロセス**

1. **技術責任者による承認**

   - デプロイ対象の変更内容レビュー
   - 影響範囲の確認
   - ロールバック計画の確認

2. **運用責任者による承認**
   - デプロイタイミングの承認
   - 業務影響の確認
   - 緊急連絡体制の確認

### **デプロイ実行**

```bash
# 本番環境への手動デプロイ（実際のデプロイを実行）
./cicd/scripts/deploy.sh prd "2025-01-01T00:00:00Z"

# または特定時刻以降のPRをデプロイ
./cicd/scripts/deploy.sh prd "2025-07-20T10:00:00Z"

# 事前確認（DRY-RUNモード）
./cicd/scripts/deploy.sh prd "2025-07-20T10:00:00Z" --dry-run
```

**⚠️ 重要**:

- 上記コマンドは**実際のデプロイを実行**します
- 事前に必ず DRY-RUN モードで動作確認を行ってください

### **デプロイ監視**

```bash
# CodeBuildの実行状況監視
aws codebuild list-builds-for-project --project-name cdp-prd-dlpf-deploy

# CloudWatchログの確認
aws logs describe-log-groups --log-group-name-prefix "/aws/codebuild/cdp-prd-dlpf"
```

### **デプロイ後検証**

```bash
# CloudFormationスタックの状態確認
aws cloudformation describe-stacks --stack-name dlpf-prd-codebuild-deploy

# Glueジョブの更新確認
aws glue get-jobs --query 'Jobs[?contains(Name, `dlpf`)].Name'

# Lambda関数の更新確認
aws lambda list-functions --query 'Functions[?contains(FunctionName, `dlpf`)].FunctionName'

# 本番環境での基本動作テスト
# データ処理パイプラインの動作確認
# エラーログの確認
```

## 🚨 緊急時対応

### **ロールバック手順**

```bash
# 前回の正常状態への復旧
./cicd/scripts/deploy.sh prd "2025-07-19T00:00:00Z"

# 特定のCloudFormationスタックのロールバック
aws cloudformation cancel-update-stack --stack-name <stack-name>
```

### **緊急連絡体制**

- 技術責任者: [連絡先]
- 運用責任者: [連絡先]
- AWS 技術サポート: [契約に応じて]

## 🧹 作業完了後のクリーンアップ

### **セキュリティルーム作業後の必須処理**

```bash
# AWS認証情報の完全削除
rm -rf ~/.aws*

# 一時ファイルの削除
rm -f ~/awscliv2.zip
rm -rf ~/aws/

# 作業ディレクトリのクリーンアップ
cd ~
rm -rf ~/works/tis-dlpf-app

# 履歴のクリア（セキュリティ対策）
history -c
history -w
```

**⚠️ 重要**: セキュリティルームでの作業完了後は、必ず上記のクリーンアップを実行してください。

## 📞 緊急時連絡

**問題発生時**: 即座に作業を停止し、状況を報告
**ロールバック**: 必要に応じて CloudFormation スタック削除

## 📚 関連ドキュメント

- [AWS インフラリソース一覧](aws-resources.md)
- [デプロイシステム設計書](deployment-system.md)
- [運用ガイド](operation-guide.md)
- [CloudFormation 手動デプロイ手順書](../../cloudformation/docs/CloudFormation手動デプロイ手順書.md)
- [検証環境 CI/CD システム構築手順書](staging-cicd-system-deployment-procedure.md)

---

**作成日**: 2025-07-22
**作成者**: TIS 黄
**承認者**: [承認者名]
