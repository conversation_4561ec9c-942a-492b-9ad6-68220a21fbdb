# 検証環境CI/CDパラメータ更新 詳細実装計画書

## 1. 実装概要

本計画書は、開発環境での動作確認完了を受けて、検証環境へのCI/CDシステム移行を実現するための詳細な実装手順を定義します。

### 1.1 前提条件
- 開発環境での動作確認がほぼ完了
- AWS CLI が検証環境向けに切り替え済み（`~/.aws.stg`）
- Parameter Store・Secrets Manager の基本調査完了
- VPC・CodeConnections の詳細値が未確認

### 1.2 目標
- 検証環境CI/CDシステムの完全稼働
- 開発環境と同等の機能性確保（SNS通知除く）
- 安全なロールバック体制の確立

## 2. 実装フェーズ

```mermaid
gantt
    title 検証環境CI/CDパラメータ更新実装スケジュール
    dateFormat  YYYY-MM-DD
    section Phase 1: 環境調査
    AWS環境調査           :phase1-1, 2025-06-05, 2h
    GitHub App認証情報設定 :phase1-2, after phase1-1, 1h
    section Phase 2: パラメータ更新
    パラメータファイル更新  :phase2-1, after phase1-2, 1h
    検証・テスト          :phase2-2, after phase2-1, 1h
    section Phase 3: 本格稼働
    CloudFormation更新    :phase3-1, after phase2-2, 30m
    動作検証             :phase3-2, after phase3-1, 30m
```

## 3. Phase 1: AWS環境調査・GitHub App認証情報設定

### 3.1 AWS環境調査項目

#### 3.1.1 VPC・ネットワーク情報調査（✅ 完了 - 2025-06-05）
```bash
# VPC情報確認
aws ec2 describe-vpcs --filters "Name=tag:Name,Values=*stg*" --query 'Vpcs[0].VpcId' --output text
# 結果: vpc-0cfcb9b50e15ba692

# サブネット情報確認（プライベートサブネットを特定）
aws ec2 describe-subnets --filters "Name=vpc-id,Values=vpc-0cfcb9b50e15ba692" --query 'Subnets[].[SubnetId,Tags[?Key==`Name`].Value|[0]]' --output table
# 結果:
# - subnet-00d13a611d7bdb00a (snet-stg-dlpf-pri-c)
# - subnet-06d7cae16fde6f45c (snet-stg-dlpf-pri-a)

# セキュリティグループ確認
aws ec2 describe-security-groups --filters "Name=vpc-id,Values=vpc-0cfcb9b50e15ba692" --query 'SecurityGroups[].[GroupId,GroupName]' --output table
# 結果: sg-00fee2146f098b0d9 (sgroup-stg-dlpf-cdp-ecs-fargate-01) を使用予定
```

**✅ 調査完了結果**:
- **VPC ID**: `vpc-0cfcb9b50e15ba692`
- **サブネット ID**: `subnet-00d13a611d7bdb00a,subnet-06d7cae16fde6f45c`
- **セキュリティグループ ID**: `sg-00fee2146f098b0d9`

#### 3.1.2 CodeConnections確認（✅ インフラチーム作成完了）
```bash
# CodeConnections一覧確認
aws codeconnections list-connections --provider-type GitHub --query 'Connections[?ConnectionStatus==`AVAILABLE`]'
# 結果: gitconnection-stg-dlpf-app作成完了
# ARN: arn:aws:codeconnections:ap-northeast-1:869935081854:connection/654e5c05-e227-451c-9f51-b1ac8a21e262
```

**✅ 完了結果**:
- **リソース名**: `gitconnection-stg-dlpf-app`
- **ARN**: `arn:aws:codeconnections:ap-northeast-1:869935081854:connection/654e5c05-e227-451c-9f51-b1ac8a21e262`
- **ステータス**: `AVAILABLE`

#### 3.1.3 Parameter Store現状確認（✅ 確認完了）
```bash
# 既存Parameter Store確認
aws ssm get-parameter --name "/dlpf/stg/deploy-baseline-time" --query 'Parameter.Value' --output text
# 結果: ParameterNotFound (正常 - CloudFormationで作成予定)

# 通知メール設定確認（存在しない場合はエラーが正常）
aws ssm get-parameter --name "/dlpf/stg/notification-email" --query 'Parameter.Value' --output text
# 結果: ParameterNotFound (正常 - 検証環境では通知機能無効)
```

**✅ 確認完了**:
- Parameter Store未設定は正常（CloudFormationで自動作成される）
- 検証環境では通知機能無効のため `/dlpf/stg/notification-email` は不要

### 3.2 GitHub App認証情報設定（❌ チームリーダー承認必要）

#### 3.2.1 開発環境からの認証情報取得（✅ 準備完了）
```bash
# 開発環境の認証情報確認（AWS CLI開発環境に切り替え後）
rm ~/.aws
ln -s ~/.aws.dev ~/.aws
aws secretsmanager get-secret-value --secret-id github-app-credentials --query 'SecretString' --output text
```

#### 3.2.2 検証環境への認証情報設定（❌ 承認待ち）
```bash
# 検証環境に切り替え
rm ~/.aws
ln -s ~/.aws.stg ~/.aws

# 認証情報をSecrets Managerに設定（固定名: github-app-credentials）
aws secretsmanager create-secret \
  --name github-app-credentials \
  --description "GitHub App credentials for staging environment" \
  --secret-string '{"app_id":"[APP_ID]","installation_id":"[INSTALLATION_ID]","private_key":"[PRIVATE_KEY]"}'
```

**❌ チームリーダー承認必要**:
- **対象**: 検証環境への認証情報設定・デプロイ実行
- **承認者**: チームリーダー（二宮さん、原田さん）
- **内容**: 開発環境の認証情報をそのまま検証環境に複製（固定名: `github-app-credentials`）
- **理由**: 検証環境への変更・デプロイには事前承認が必要
- **状況**: 一時的に設定したが、承認前のため削除済み

### 3.3 Phase 1 成功基準
- [x] VPC ID、サブネット ID、セキュリティグループ IDの特定完了
- [x] CodeConnections ARNの特定完了（✅ インフラチーム作成完了）
- [ ] GitHub App認証情報の検証環境設定完了（❌ チームリーダー承認待ち）
- [x] Parameter Store基本構造の確認完了

## 4. 実装完了事項（2025-06-05）

### 4.1 パラメータファイル更新完了
- 検証環境用パラメータファイル更新完了
- 調査結果を反映（VPC、サブネット、セキュリティグループ、CodeConnections ARN）
- 通知機能無効化（検証環境仕様準拠）

### 4.2 本番環境対応実装完了
- 三分岐条件分岐ロジック実装（dev/stg/prd対応）
- ブランチ設定修正（develop/release/master）
- cfn-lint警告解決

### 4.3 通知機能依存関係修正完了
- 検証環境でのSNSテンプレート依存関係削除
- ImportValue削除による最適化

### 4.4 ドキュメント整備完了
- ToDoList進捗更新（セクション22追加）
- 設計書更新（本番環境対応履歴追加）

## 5. 次のステップ

### 5.1 チームリーダー承認申請
- 二宮さん、原田さんへの承認申請
- 検証環境デプロイ・認証情報設定の承認

### 5.2 承認後実施予定
- GitHub App認証情報設定
- CloudFormationスタックデプロイ（2テンプレートのみ）
- 動作確認テスト

---

**作成日**: 2025-06-04
**作成者**: TIS黄
**最終更新**: 2025-06-05
**承認者**: [承認者名]

この実装計画書に基づいて、検証環境CI/CDパラメータ更新を安全かつ確実に実施してください。
