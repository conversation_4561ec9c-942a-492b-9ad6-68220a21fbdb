# 検証環境CI/CDシステム構築 操作手順書

## 📋 概要

**実施日時**: 2025-06-05 13:00
**実施者**: TIS黄・二宮さん（相互点検）
**対象環境**: 検証環境（stg）
**目的**: 検証環境CI/CDシステムの構築・稼働開始

## ⚠️ 重要な前提条件

### 🔴 **二宮さんとの相互点検作業: release_20250606_1ブランチ作成**

**❗ 13時から二宮さんと実施する作業**

#### **Step 0-1: issue 641関連コミットの特定**
```bash
# 専用スクリプトを実行
cd cicd/docs
chmod +x get-issue-641-commits.sh
./get-issue-641-commits.sh

# スクリプト実行結果:
# - issue 641関連PRの一覧表示
# - 各PRのマージコミットハッシュ表示
# - cherry-pickコマンド例の出力
# - 時系列順に整理された実行可能なコマンド
```

**📝 スクリプトの出力例**:
```
📋 cherry-pickコマンド例:
git checkout release
git pull origin release
git checkout -b release_20250606_1
git cherry-pick [コミットハッシュ1]
git cherry-pick [コミットハッシュ2]
git push origin release_20250606_1
```

#### **Step 0-2: release_20250606_1ブランチ作成とcherry-pick**
```bash
# Step 0-1のスクリプト出力結果をコピー&ペーストして実行
# 例:
git checkout release
git pull origin release
git checkout -b release_20250606_1

# スクリプトが出力したcherry-pickコマンドを順次実行
git cherry-pick [スクリプト出力のコミットハッシュ1]
git cherry-pick [スクリプト出力のコミットハッシュ2]
git cherry-pick [スクリプト出力のコミットハッシュ3]
# ... (スクリプト出力に従って全て実行)

# リモートにプッシュ
git push origin release_20250606_1
```

#### **Step 0-3: release_20250606_1 → release PR作成**
```bash
# PR作成
gh pr create --title "Release 20250606_1 : issue 641 CI/CDパイプライン構築" \
  --body "## 概要
issue 641 CI/CDパイプライン構築関連の変更をreleaseブランチに反映

## 含まれる変更
- 検証環境CI/CDシステム構築準備
- 本番環境対応（三分岐ロジック）
- CloudFormationテンプレート修正
- パラメータファイル更新

## cherry-pick対象コミット
- [Step 0-1で特定したコミットハッシュリスト]" \
  --base release

```

**📝 注意**: PRマージは別途実施（二宮さんとの作業範囲外）

## 🎯 実施手順

### **Phase 1: 環境準備・確認**

#### **Step 1-1: AWS環境切り替え**
```bash
# 検証環境に切り替え
cd ~/works/tis-dlpf-app
rm ~/.aws
ln -s ~/.aws.stg ~/.aws

# 環境確認
aws sts get-caller-identity
# 期待値: Account ID ************ (検証環境)
```

#### **Step 1-2: 既存リソース確認**
```bash
# VPC確認
aws ec2 describe-vpcs --vpc-ids vpc-0cfcb9b50e15ba692 --query 'Vpcs[0].State'
# 期待値: "available"

# CodeConnections確認
aws codeconnections get-connection --connection-arn arn:aws:codeconnections:ap-northeast-1:************:connection/654e5c05-e227-451c-9f51-b1ac8a21e262
# 期待値: ConnectionStatus: "AVAILABLE"
```

### **Phase 2: GitHub App認証情報設定**

#### **Step 2-1: 開発環境から認証情報取得**
```bash
# 一時的に開発環境に切り替え
rm ~/.aws
ln -s ~/.aws.dev ~/.aws

# 認証情報取得（出力をメモ）
aws secretsmanager get-secret-value --secret-id github-app-credentials --query 'SecretString' --output text
```

**📝 取得した認証情報をメモ**:
```json
{
  "app_id": "[取得した値をメモ]",
  "installation_id": "[取得した値をメモ]",
  "private_key": "[取得した値をメモ]"
}
```

#### **Step 2-2: 検証環境に認証情報設定**
```bash
# 検証環境に切り替え
rm ~/.aws
ln -s ~/.aws.stg ~/.aws

# 認証情報をSecrets Managerに設定
aws secretsmanager create-secret \
  --name github-app-credentials \
  --description "GitHub App credentials for staging environment" \
  --secret-string '{"app_id":"[Step2-1で取得した値]","installation_id":"[Step2-1で取得した値]","private_key":"[Step2-1で取得した値]"}'
```

#### **Step 2-3: 設定確認**
```bash
# 設定確認
aws secretsmanager describe-secret --secret-id github-app-credentials
# 期待値: Name: "github-app-credentials", ARN確認
```

### **Phase 3: CloudFormationスタックデプロイ**

#### **Step 3-1: デプロイ対象テンプレート確認**
**デプロイ対象**: 2つのテンプレート
1. `cicd-codebuild-deploy.yml` - CodeBuildプロジェクト
2. `cicd-system-manager-parameter-store.yml` - Parameter Store

**対象外**:
- `cicd-sns-notification-topic.yml` (検証環境では通知機能無効)

#### **Step 3-2: 既存手順書に従ったデプロイ**
```bash
# 手順書の場所確認
ls -la cicd/cloudformation/docs/CloudFormation手動デプロイ手順書.md

# 手順書に従ってデプロイ実行
# 1. Parameter Store テンプレート
# 2. CodeBuild テンプレート
```

**📋 デプロイパラメータ**:
- **Environment**: `stg`
- **VpcId**: `vpc-0cfcb9b50e15ba692`
- **SubnetIds**: `subnet-00d13a611d7bdb00a,subnet-06d7cae16fde6f45c`
- **SecurityGroupIds**: `sg-00fee2146f098b0d9`
- **CodeConnectionsArn**: `arn:aws:codeconnections:ap-northeast-1:************:connection/654e5c05-e227-451c-9f51-b1ac8a21e262`

#### **Step 3-3: デプロイ完了確認**
```bash
# CloudFormationスタック確認
aws cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE --query 'StackSummaries[?contains(StackName, `stg`) && contains(StackName, `dlpf`)].StackName'

# CodeBuildプロジェクト確認
aws codebuild list-projects --query 'projects[?contains(@, `stg`) && contains(@, `dlpf`)]'

# Parameter Store確認
aws ssm get-parameter --name "/dlpf/stg/deploy-baseline-time"
```

### **Phase 4: 動作確認テスト**

#### **Step 4-1: DRY-RUNモード動作確認**
```bash
# CodeBuildプロジェクト実行（DRY-RUNモード）
aws codebuild start-build --project-name cdp-stg-dlpf-deploy
```

#### **Step 4-2: 実行ログ確認**
```bash
# ビルド実行状況確認
aws codebuild list-builds-for-project --project-name cdp-stg-dlpf-deploy --sort-order DESCENDING

# ログ確認（CloudWatch Logs）
aws logs describe-log-groups --log-group-name-prefix "/aws/codebuild/dlpf-stg"
```

#### **Step 4-3: 期待される動作**
- ✅ releaseブランチからのソース取得
- ✅ GitHub API接続成功
- ✅ DRY-RUNモードでの実行（実際のデプロイは行わない）
- ✅ ログ出力正常

## 🔍 トラブルシューティング

### **認証エラーの場合**
```bash
# Secrets Manager確認
aws secretsmanager get-secret-value --secret-id github-app-credentials

# GitHub App設定確認（開発環境と同じ設定か）
```

### **CodeBuild実行エラーの場合**
```bash
# CloudWatch Logsでエラー詳細確認
aws logs filter-log-events --log-group-name "/aws/codebuild/dlpf-stg-dlpf-deploy" --start-time $(date -d '1 hour ago' +%s)000
```

## ✅ 完了チェックリスト

### **前提条件（二宮さんとの作業）**
- [ ] issue 641関連コミット特定完了
- [ ] release_20250606_1ブランチ作成完了
- [ ] cherry-pick実行完了
- [ ] release_20250606_1 → release PR作成完了

### **Phase 1: 環境準備**
- [ ] AWS環境切り替え完了
- [ ] 既存リソース確認完了

### **Phase 2: 認証情報設定**
- [ ] 開発環境から認証情報取得完了
- [ ] 検証環境に認証情報設定完了
- [ ] 設定確認完了

### **Phase 3: CloudFormationデプロイ**
- [ ] Parameter Storeテンプレートデプロイ完了
- [ ] CodeBuildテンプレートデプロイ完了
- [ ] デプロイ完了確認

### **Phase 4: 動作確認**
- [ ] DRY-RUNモード実行完了
- [ ] ログ確認完了
- [ ] 期待動作確認完了

## 📞 緊急時連絡

**問題発生時**: 即座に作業を停止し、状況を報告
**ロールバック**: 必要に応じてCloudFormationスタック削除

---

**作成日**: 2025-06-05
**作成者**: TIS黄
**承認者**: 二宮さん（相互点検）
