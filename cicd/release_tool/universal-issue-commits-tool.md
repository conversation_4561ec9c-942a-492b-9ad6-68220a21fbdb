# 🚀 Universal Issue Commits Tool

**バージョン**: 1.0.0
**作成日**: 2025-06-06
**作成者**: TIS黄
**用途**: GitHub Issue関連PRのcherry-pick自動化ツール

## 📋 **概要**

指定したGitHub Issue番号に関連付けられたPRを自動検出し、時系列順でcherry-pickコマンドを生成・実行するツールです。

### **主な機能**
- ✅ 指定Issue番号の関連PR自動検出
- ✅ 関連PRのマージコミット取得
- ✅ 時系列順でのcherry-pickコマンド生成
- ✅ 自動cherry-pick実行（オプション）
- ✅ コンフリクト自動解決（incoming優先）
- ✅ 差分確認促進機能

## 🎯 **使用場面**

### **典型的なユースケース**
1. **リリースブランチ作成**: 開発完了機能のリリース準備
2. **検証環境デプロイ**: 特定機能群の検証環境反映
3. **ホットフィックス適用**: 緊急修正の本番反映
4. **機能統合**: 特定Issue関連の統合ブランチ作成

### **対象プロジェクト**
- GitHub Issue管理プロジェクト
- 機能開発・バグ修正作業
- CI/CDパイプライン構築プロジェクト

## 🔧 **使用方法**

### **基本構文**
```bash
./universal-issue-commits-tool.sh <issue-number> --base <branch> --branch <name> [options]
```

### **Mode 1: 情報表示モード（デフォルト）**
```bash
# 基本使用法 - コマンド生成のみ
./universal-issue-commits-tool.sh 641 --base release --branch release_20250606_2

# 実行例
./universal-issue-commits-tool.sh 1234 --base develop --branch feature_1234_fix
```

**動作内容:**
- Issue検出とPR一覧表示
- cherry-pickコマンド生成・表示
- **実際のcherry-pickは実行しない**（安全）

### **Mode 2: 自動実行モード**
```bash
# 自動実行
./universal-issue-commits-tool.sh 641 --base release --branch release_20250606_2 --auto-cherry-pick
```

**動作内容:**
- Issue検出・PR取得
- 新ブランチ自動作成
- cherry-pick自動実行
- コンフリクト自動解決
- 差分確認促進

## 📊 **オプション一覧**

| No. | オプション | 必須 | 説明 | 使用例 |
|-----|-----------|------|------|--------|
| 1 | `<issue-number>` | ✅ | GitHub Issue番号 | `641` |
| 2 | `--base <branch>` | ✅ | ベースブランチ指定 | `--base release` |
| 3 | `--branch <name>` | ✅ | 作成するブランチ名指定 | `--branch release_20250606_2` |
| 4 | `--auto-cherry-pick` | ❌ | 自動cherry-pick実行 | `--auto-cherry-pick` |
| 5 | `--dry-run` | ❌ | ドライラン実行 | `--dry-run` |
| 6 | `--help` | ❌ | ヘルプ表示 | `--help` |

## 🛡️ **安全機能**

### **事故防止策**
1. **デフォルト安全モード**: 情報表示のみ
2. **処理前確認**: 対象コミット数・内容表示
3. **ブランチバックアップ**: 処理前状態保存
4. **段階的実行**: エラー時の中断・復旧

### **コンフリクト自動解決**
```bash
# 自動解決戦略: incoming優先（theirs）
git checkout --theirs .
git add .
git cherry-pick --continue
```

### **差分確認促進**
処理完了後に以下の確認コマンドを表示：
```bash
# フォルダ単位確認
git diff --name-status release..HEAD

# ファイル単位確認
git diff release..HEAD --stat

# 詳細確認
git diff release..HEAD

# ディレクトリ別確認
git diff release..HEAD -- cicd/
git diff release..HEAD -- job/
```

## 📝 **実行例**

### **例1: 情報確認（安全モード）**
```bash
$ ./universal-issue-commits-tool.sh 641 --base release --branch release_20250606_2

🔍 Issue #641 にリンクされたPR一覧を取得中...
✅ 対象issue数: 1件
✅ 重複除去後のマージ済みPR数: 9件

📋 cherry-pick対象コミット一覧 (時系列順):
PR #638: 458e242d... - 機能A実装 (merged: 2025-04-14)
PR #651: 073d0e28... - 機能B実装 (merged: 2025-04-14)
...

📝 cherry-pickコマンド例:
git checkout release
git checkout -b release_20250606_2
git cherry-pick 458e242d...
git cherry-pick 073d0e28...
...
```

### **例2: 自動実行**
```bash
$ ./universal-issue-commits-tool.sh 641 --base release --branch release_20250606_2 --auto-cherry-pick

🔍 Issue #641 関連コミット取得開始...
✅ 9件のマージ済みPRを検出

🚀 自動cherry-pick実行開始...
✅ ブランチ作成: release_20250606_2
🔄 cherry-pick進行中: 1/84
⚠️  コンフリクト検出: 自動解決中...
✅ 自動解決完了
🔄 cherry-pick進行中: 2/84
...
✅ 全84件のcherry-pick完了

🔍 差分確認を実施してください:
git diff release..HEAD --stat
```

## 📦 **事前準備**

### **GitHub CLI (gh) のインストール**

#### **WSL環境（Ubuntu）**
```bash
# 公式リポジトリを追加
curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null

# インストール
sudo apt update
sudo apt install gh
```

### **GitHub CLI認証設定**
```bash
# 認証開始
gh auth login

# 認証状態確認
gh auth status
```

## ⚠️ **注意事項**

### **使用前の確認**
1. **GitHub CLI認証**: `gh auth status`
2. **適切なブランチ**: `git branch`で現在位置確認
3. **作業ディレクトリ**: リポジトリルートで実行

### **制限事項**
1. **GitHub API制限**: 大量Issue処理時のレート制限
2. **ネットワーク依存**: GitHub API接続必須
3. **権限要件**: リポジトリ読み取り権限必要

### **トラブルシューティング**

#### **GitHub CLIが見つからない**
```bash
# インストール確認
which gh
gh --version

# インストールされていない場合は上記の手順でインストール
```

#### **認証エラー**
```bash
# 認証状態確認
gh auth status

# 再認証
gh auth login

# トークンでの認証（CI/CD環境）
gh auth login --with-token < token.txt
```

#### **API制限エラー**
```bash
# レート制限確認
gh api rate_limit

# 認証済みユーザーは5000回/時間、未認証は60回/時間
```

#### **権限エラー**
```bash
# リポジトリアクセス確認
gh repo view TIS-DSDev/tis-dlpf-app

# 組織のプライベートリポジトリの場合、適切な権限が必要
```

## 🔗 **関連ツール**

- `get-issue-641-commits.sh`: 特定Issue専用版
- `universal-issue-commits-tool.sh`: 汎用Issue対応版（本ツール）
- `cfn_deploy.sh`: CloudFormationデプロイ
- `deploy.sh`: 統合デプロイスクリプト

## 📞 **サポート**

**技術的問題**: TIS黄 (<EMAIL>)
**機能要望**: GitHub Issue作成
**緊急対応**: 24時間対応可能

---

**📝 備考**: このツールは開発効率化と品質向上を目的としています。不明点があれば遠慮なく相談してください。
