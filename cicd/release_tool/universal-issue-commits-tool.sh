#!/bin/bash

# =============================================================================
# Universal Issue Commits Tool
# =============================================================================
# 目的: 指定したGitHub Issue番号とその全サブIssueに関連付けられたPRを自動検出し、
#       時系列順でcherry-pickコマンドを生成・実行するツール
# 作成者: TIS黄
# 作成日: 2025-06-06
# バージョン: 1.0.0
# =============================================================================

set -e  # エラー時に停止

# =============================================================================
# 設定・定数
# =============================================================================
SCRIPT_NAME="universal-issue-commits-tool.sh"
VERSION="1.0.0"
GITHUB_OWNER="TIS-DSDev"
GITHUB_REPO="tis-dlpf-app"

# デフォルト値
AUTO_CHERRY_PICK=false
DRY_RUN=false
BRANCH_NAME=""
BASE_BRANCH=""
ISSUE_NUMBER=""

# =============================================================================
# ヘルプ表示
# =============================================================================
show_help() {
    cat << EOF
🚀 Universal Issue Commits Tool v${VERSION}

使用方法:
    $SCRIPT_NAME <issue-number> --base <branch> --branch <name> [options]

必須引数:
    issue-number        GitHub Issue番号
    --base <branch>     ベースブランチ指定（例: release, develop, master）
    --branch <name>     作成するブランチ名指定

オプション:
    --auto-cherry-pick  自動cherry-pick実行
    --dry-run          ドライラン実行
    --help             このヘルプを表示

使用例:
    # 情報表示モード（デフォルト）
    $SCRIPT_NAME 641 --base release --branch release_20250606_2

    # 自動実行モード
    $SCRIPT_NAME 641 --base release --branch release_20250606_2 --auto-cherry-pick

EOF
}

# =============================================================================
# ログ関数
# =============================================================================
log_info() {
    echo "🔍 $1" >&2
}

log_success() {
    echo "✅ $1" >&2
}

log_warn() {
    echo "⚠️  $1" >&2
}

log_error() {
    echo "❌ エラー: $1" >&2
}

# =============================================================================
# 引数解析
# =============================================================================
parse_arguments() {
    # ヘルプオプションの早期チェック
    for arg in "$@"; do
        if [ "$arg" = "--help" ]; then
            show_help
            exit 0
        fi
    done

    if [ $# -eq 0 ]; then
        log_error "Issue番号が指定されていません"
        show_help
        exit 1
    fi

    ISSUE_NUMBER="$1"
    shift

    while [[ $# -gt 0 ]]; do
        case $1 in
            --auto-cherry-pick)
                AUTO_CHERRY_PICK=true
                shift
                ;;
            --branch)
                BRANCH_NAME="$2"
                shift 2
                ;;
            --base)
                BASE_BRANCH="$2"
                shift 2
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            *)
                log_error "不明なオプション: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # Issue番号の検証
    if ! [[ "$ISSUE_NUMBER" =~ ^[0-9]+$ ]]; then
        log_error "Issue番号は数値で指定してください: $ISSUE_NUMBER"
        exit 1
    fi

    # ベースブランチの必須チェック
    if [ -z "$BASE_BRANCH" ]; then
        log_error "ベースブランチの指定が必要です: --base <branch>"
        echo "   例: --base release, --base develop, --base master" >&2
        show_help
        exit 1
    fi

    # ブランチ名の必須チェック
    if [ -z "$BRANCH_NAME" ]; then
        log_error "ブランチ名の指定が必要です: --branch <name>"
        echo "   例: --branch release_20250606_2" >&2
        show_help
        exit 1
    fi
}

# =============================================================================
# 前提条件チェック
# =============================================================================
check_prerequisites() {
    log_info "前提条件をチェック中..."

    # GitHub CLIの確認
    if ! command -v gh &> /dev/null; then
        log_error "GitHub CLI (gh) がインストールされていません"
        echo "   インストール方法: https://cli.github.com/" >&2
        exit 1
    fi

    # GitHub認証確認
    if ! gh auth status >/dev/null 2>&1; then
        log_error "GitHub CLIの認証が必要です"
        echo "   実行してください: gh auth login" >&2
        exit 1
    fi

    # Gitリポジトリ確認
    if ! git rev-parse --git-dir &> /dev/null; then
        log_error "Gitリポジトリ内で実行してください"
        exit 1
    fi

    log_success "前提条件チェック完了"
}

# =============================================================================
# ブランチ名検証
# =============================================================================
validate_branch_name() {
    if [ "$AUTO_CHERRY_PICK" = true ]; then
        # 既存ブランチとの重複チェック
        if git show-ref --verify --quiet refs/heads/$BRANCH_NAME 2>/dev/null || \
           git show-ref --verify --quiet refs/remotes/origin/$BRANCH_NAME 2>/dev/null; then
            log_error "ブランチ '$BRANCH_NAME' は既に存在します"
            echo "   別のブランチ名を指定してください" >&2
            exit 1
        fi
    fi
}

# =============================================================================
# Issue関連PR取得
# =============================================================================
# 再帰的にSub-issuesを取得する関数
get_all_sub_issues_recursive() {
    local issue_number=$1
    local depth=${2:-0}
    local max_depth=${3:-8}  # GitHub Sub-issuesの最大階層

    # 深度制限チェック
    if [ $depth -ge $max_depth ]; then
        echo "   ⚠️  最大階層($max_depth)に達しました: Issue #$issue_number" >&2
        return
    fi

    # 直接の子Issueを取得
    local sub_issues=$(gh api "/repos/${GITHUB_OWNER}/${GITHUB_REPO}/issues/${issue_number}/sub_issues" --jq '.[].number' 2>/dev/null || true)

    if [ -n "$sub_issues" ]; then
        echo "   📋 Issue #$issue_number の子Issue: $(echo "$sub_issues" | tr '\n' ' ')" >&2

        # 各子Issueを出力し、再帰的に処理
        for sub_issue in $sub_issues; do
            echo $sub_issue
            get_all_sub_issues_recursive $sub_issue $((depth + 1)) $max_depth
        done
    fi
}

get_related_prs() {
    log_info "Issue #${ISSUE_NUMBER} および全階層Sub-issuesにリンクされたPR一覧を取得中..."

    # 指定されたissueの存在確認
    log_info "Issue #${ISSUE_NUMBER} の存在確認中..."
    local issue_info=$(gh issue view $ISSUE_NUMBER --json number,title 2>/dev/null || true)

    if [ -z "$issue_info" ]; then
        log_error "Issue #${ISSUE_NUMBER} が見つかりません"
        exit 1
    fi

    local issue_title=$(echo "$issue_info" | jq -r '.title')
    log_info "対象Issue: #${ISSUE_NUMBER} - ${issue_title}"

    # GitHub Sub-issues機能を使用して階層的にIssueを取得
    log_info "GitHub Sub-issues機能で階層的Issue取得中..."

    # ルートIssueから開始
    local all_issues="$ISSUE_NUMBER"

    # 再帰的にSub-issuesを取得
    local sub_issues=$(get_all_sub_issues_recursive $ISSUE_NUMBER)

    if [ -n "$sub_issues" ]; then
        all_issues="$all_issues
$sub_issues"
    fi

    # 重複除去とソート
    all_issues=$(echo "$all_issues" | sort -n | uniq)

    local issue_count=$(echo "$all_issues" | wc -l)
    log_success "階層的Issue数: ${issue_count}件"
    echo "   対象issue: $(echo "$all_issues" | tr '\n' ' ')" >&2

    # 各issueのConnectedEventを取得
    log_info "各issueのConnectedEventを取得中..."
    local all_pr_data=""

    while read -r issue_num; do
        [ -z "$issue_num" ] && continue
        echo "     issue #$issue_num を処理中..." >&2

        local issue_pr_data=$(gh api graphql -f query="
        query {
          repository(owner: \"$GITHUB_OWNER\", name: \"$GITHUB_REPO\") {
            issue(number: $issue_num) {
              timelineItems(first: 100, itemTypes: [CONNECTED_EVENT]) {
                nodes {
                  __typename
                  ... on ConnectedEvent {
                    subject {
                      ... on PullRequest {
                        number
                        title
                        mergeCommit {
                          oid
                        }
                        state
                        mergedAt
                      }
                    }
                  }
                }
              }
            }
          }
        }" --jq '.data.repository.issue.timelineItems.nodes[] | select(.subject.mergeCommit != null) | .subject' 2>/dev/null || true)

        if [ -n "$issue_pr_data" ]; then
            if [ -z "$all_pr_data" ]; then
                all_pr_data="$issue_pr_data"
            else
                all_pr_data="$all_pr_data"$'\n'"$issue_pr_data"
            fi
            local issue_pr_count=$(echo "$issue_pr_data" | jq -s 'length')
            echo "       → ${issue_pr_count}件のマージ済みPRを発見" >&2
        else
            echo "       → マージ済みPRなし" >&2
        fi
    done <<< "$all_issues"

    if [ -z "$all_pr_data" ]; then
        log_error "関連issueにリンクされたマージ済みPRが見つかりません"
        exit 1
    fi

    # 重複を除去（PR番号でユニーク化）
    PR_DATA=$(echo "$all_pr_data" | jq -s 'unique_by(.number)')
    local pr_count=$(echo "$PR_DATA" | jq 'length')
    log_success "重複除去後のマージ済みPR数: ${pr_count}件"
}

# =============================================================================
# コミット情報処理
# =============================================================================
process_commits() {
    log_info "各PRのマージコミット情報を処理中..."

    # 結果を格納する配列
    declare -g -a commit_hashes
    declare -g -a commit_info
    commit_hashes=()
    commit_info=()

    # 各PRのマージコミット情報を処理（マージ日時順でソート）
    while IFS='|' read -r pr_num merge_commit pr_title merged_at; do
        echo "🔍 PR #$pr_num を処理中..." >&2

        if [ -z "$merge_commit" ] || [ "$merge_commit" = "null" ]; then
            log_warn "PR #$pr_num のマージコミットが見つかりません"
            continue
        fi

        # 結果を配列に追加
        commit_hashes+=("$merge_commit")
        commit_info+=("PR #$pr_num: $merge_commit - $pr_title (merged: $merged_at)")

        echo "   ✅ $merge_commit - $pr_title" >&2
    done < <(echo "$PR_DATA" | jq -r '.[] | "\(.number)|\(.mergeCommit.oid)|\(.title)|\(.mergedAt)"' | sort -t'|' -k4)
}

# =============================================================================
# 結果表示
# =============================================================================
show_results() {
    echo "" >&2
    echo "==================================================" >&2
    echo "📋 cherry-pick対象コミット一覧 (時系列順):" >&2
    echo "==================================================" >&2

    if [ ${#commit_hashes[@]} -eq 0 ]; then
        log_error "有効なマージコミットが見つかりませんでした"
        exit 1
    fi

    # 結果を表示
    for info in "${commit_info[@]}"; do
        echo "$info" >&2
    done

    echo "" >&2
    echo "📝 cherry-pickコマンド例:" >&2
    echo "==================================================" >&2
    echo "# ${BASE_BRANCH}ブランチベースで新ブランチ作成" >&2
    echo "git checkout $BASE_BRANCH" >&2
    echo "git pull origin $BASE_BRANCH" >&2
    echo "git checkout -b $BRANCH_NAME" >&2
    echo "" >&2
    echo "# コミットを古い順にcherry-pick" >&2
    for hash in "${commit_hashes[@]}"; do
        echo "git cherry-pick $hash" >&2
    done
    echo "" >&2
    echo "# リモートにプッシュ" >&2
    echo "git push origin $BRANCH_NAME" >&2
}

# =============================================================================
# 自動cherry-pick実行
# =============================================================================
execute_auto_cherry_pick() {
    if [ "$AUTO_CHERRY_PICK" = false ]; then
        return 0
    fi

    log_info "🚀 自動cherry-pick実行開始..."

    # デバッグ: 配列の内容確認
    echo "🔍 デバッグ: commit_hashes配列の要素数: ${#commit_hashes[@]}" >&2
    if [ ${#commit_hashes[@]} -eq 0 ]; then
        log_error "commit_hashes配列が空です"
        exit 1
    fi

    # 現在のブランチを保存
    local current_branch=$(git branch --show-current)

    # ベースブランチに切り替え
    log_info "ベースブランチ ${BASE_BRANCH} に切り替え中..."
    git checkout "$BASE_BRANCH" || {
        log_error "ベースブランチ ${BASE_BRANCH} への切り替えに失敗"
        exit 1
    }

    git pull origin "$BASE_BRANCH" || {
        log_error "ベースブランチの更新に失敗"
        exit 1
    }

    # 新ブランチ作成
    log_info "新ブランチ ${BRANCH_NAME} を作成中..."
    git checkout -b "$BRANCH_NAME" || {
        log_error "ブランチ ${BRANCH_NAME} の作成に失敗"
        exit 1
    }

    # cherry-pick実行
    local total_commits=${#commit_hashes[@]}
    local current_count=0
    local conflict_count=0

    echo "🔍 デバッグ: cherry-pickループ開始 - 対象コミット数: $total_commits" >&2
    echo "🔍 デバッグ: 最初の3つのハッシュ: ${commit_hashes[0]} ${commit_hashes[1]} ${commit_hashes[2]}" >&2

    for hash in "${commit_hashes[@]}"; do
        ((current_count++))
        echo "🔄 cherry-pick進行中: ${current_count}/${total_commits} - $hash" >&2

        # コミットが既に存在するかチェック
        echo "🔍 デバッグ: コミット存在チェック中..." >&2
        if git merge-base --is-ancestor "$hash" HEAD 2>/dev/null; then
            echo "   ⏭️  スキップ（既に存在）" >&2
            continue
        fi

        echo "🔍 デバッグ: cherry-pick実行中..." >&2

        if git cherry-pick "$hash"; then
            echo "   ✅ 成功" >&2
        else
            # cherry-pickのエラー状態をチェック
            if git status --porcelain | grep -q "^UU\|^AA\|^DD"; then
                # コンフリクトの場合
                ((conflict_count++))
                log_warn "コンフリクト検出: 自動解決中..."

                # incoming優先で自動解決
                git checkout --theirs . || {
                    log_error "自動解決に失敗: $hash"
                    echo "手動解決が必要です" >&2
                    exit 1
                }

                git add . || {
                    log_error "ファイル追加に失敗: $hash"
                    exit 1
                }

                git cherry-pick --continue || {
                    log_error "cherry-pick継続に失敗: $hash"
                    exit 1
                }

                echo "   ✅ 自動解決完了" >&2
            else
                # その他のエラー（空のコミットなど）
                echo "   ⏭️  スキップ（空のコミットまたはその他のエラー）" >&2
                git cherry-pick --skip || true
            fi
        fi
    done

    log_success "全${total_commits}件のcherry-pick完了"
    if [ $conflict_count -gt 0 ]; then
        echo "   コンフリクト自動解決: ${conflict_count}件" >&2
    fi

    # 差分確認促進
    show_diff_check_guide
}

# =============================================================================
# 差分確認ガイド表示
# =============================================================================
show_diff_check_guide() {
    echo "" >&2
    echo "🔍 差分確認を実施してください:" >&2
    echo "==================================================" >&2
    echo "1. 📁 フォルダ単位の差分確認:" >&2
    echo "   git diff --name-status ${BASE_BRANCH}..HEAD" >&2
    echo "" >&2
    echo "2. 📄 ファイル単位の差分確認:" >&2
    echo "   git diff ${BASE_BRANCH}..HEAD --stat" >&2
    echo "" >&2
    echo "3. 🔍 詳細な差分確認:" >&2
    echo "   git diff ${BASE_BRANCH}..HEAD" >&2
    echo "" >&2
    echo "4. 🎯 特定ディレクトリの確認:" >&2
    echo "   git diff ${BASE_BRANCH}..HEAD -- cicd/" >&2
    echo "   git diff ${BASE_BRANCH}..HEAD -- job/" >&2
    echo "" >&2
    echo "⚠️  差分確認後、問題がなければ以下を実行:" >&2
    echo "   git push origin $BRANCH_NAME" >&2
}

# =============================================================================
# メイン処理
# =============================================================================
main() {
    echo "🚀 Universal Issue Commits Tool v${VERSION}" >&2
    echo "==================================================" >&2

    parse_arguments "$@"
    check_prerequisites
    validate_branch_name

    log_info "Issue #${ISSUE_NUMBER} 関連コミット取得開始..."
    log_info "ブランチ名: $BRANCH_NAME"
    log_info "ベースブランチ: $BASE_BRANCH"
    log_info "自動実行モード: $AUTO_CHERRY_PICK"

    get_related_prs
    process_commits
    show_results

    # 自動cherry-pick実行
    execute_auto_cherry_pick

    echo "" >&2
    log_success "Issue #${ISSUE_NUMBER} 関連コミット取得完了"
    echo "   対象コミット数: ${#commit_hashes[@]}件" >&2

    if [ "$AUTO_CHERRY_PICK" = false ]; then
        echo "" >&2
        echo "📝 次のステップ:" >&2
        echo "   1. 上記のcherry-pickコマンドを確認" >&2
        echo "   2. 自動実行する場合: $SCRIPT_NAME $ISSUE_NUMBER --base $BASE_BRANCH --branch $BRANCH_NAME --auto-cherry-pick" >&2
        echo "   3. 手動実行する場合: 上記コマンドをコピーして実行" >&2
    fi
}

# スクリプト実行
main "$@"
