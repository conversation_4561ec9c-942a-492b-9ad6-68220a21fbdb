#!/bin/bash
set -e

# CloudFormationテンプレートをデプロイするスクリプト
# 使用方法: ./deploy-cfn.sh <環境> <DRY_RUNフラグ> <変更フラグ>
# 例: ./deploy-cfn.sh "dev" "false" "true"

# 引数の検証（最低3つの引数）
if [ $# -lt 3 ]; then
  echo "Error: Invalid number of arguments"
  echo "Usage: $0 <environment> <dry_run> <has_cfn_changes> [<changed_files>...] [<cfn_metadata>]"
  exit 1
fi

ENVIRONMENT="$1"
DRY_RUN="$2"
HAS_CFN_CHANGES="$3"

# 残りの引数を処理
shift 3
ALL_ARGS=("$@")

# 最後の引数がJSONかどうかチェック（CFN_METADATA）
CFN_METADATA=""
CHANGED_FILES_ARRAY=()

if [ ${#ALL_ARGS[@]} -gt 0 ]; then
  # 最後の引数をチェック
  LAST_ARG="${ALL_ARGS[-1]}"
  if [[ "$LAST_ARG" =~ ^\{.*\}$ ]]; then
    # 最後の引数がJSONの場合、それをCFN_METADATAとして扱う
    CFN_METADATA="$LAST_ARG"
    # 最後の引数を除いた残りをCHANGED_FILESとして扱う
    CHANGED_FILES_ARRAY=("${ALL_ARGS[@]:0:$((${#ALL_ARGS[@]}-1))}")
  else
    # 最後の引数がJSONでない場合、すべてをCHANGED_FILESとして扱う
    CHANGED_FILES_ARRAY=("${ALL_ARGS[@]}")
  fi
fi

# CHANGED_FILESを文字列として結合
CHANGED_FILES=""
if [ ${#CHANGED_FILES_ARRAY[@]} -gt 0 ]; then
  CHANGED_FILES=$(printf "%s\n" "${CHANGED_FILES_ARRAY[@]}")
fi

echo "Deploying CloudFormation templates for environment: ${ENVIRONMENT}"
echo "DRY_RUN: ${DRY_RUN}"
echo "HAS_CFN_CHANGES: ${HAS_CFN_CHANGES}"

# パラメータ変更情報の解析
PARAMETER_CHANGE_TYPE=""
AFFECTED_RESOURCE_TYPES=""
if [ -n "$CFN_METADATA" ]; then
  PARAMETER_CHANGE_TYPE=$(echo "$CFN_METADATA" | jq -r '.parameter_change_type // ""')
  AFFECTED_RESOURCE_TYPES=$(echo "$CFN_METADATA" | jq -r '.affected_resource_types // ""')
  echo "Parameter change type: $PARAMETER_CHANGE_TYPE"
  echo "Affected resource types: $AFFECTED_RESOURCE_TYPES"
fi

# DRY-RUN用の関数定義
show_changed_templates_dry_run() {
  local changed_files="$1"

  # 変更されたCloudFormationテンプレートを抽出（cicd/cloudformationは除外）
  CHANGED_TEMPLATES=$(echo "$changed_files" | grep -E "^cloudformation/templates/.*\.ya?ml$" | grep -v -E "cloudformation/templates/(api-gateway|secrets-manager)/" || true)

  if [ -n "$CHANGED_TEMPLATES" ]; then
    echo "【DRY-RUN】変更されたテンプレート:"
    echo "$CHANGED_TEMPLATES"
    echo "【DRY-RUN】以下のテンプレートがデプロイされます："

    for template_file in $CHANGED_TEMPLATES; do
      # ファイル存在チェック（PRから削除されたファイル対応）
      if [ ! -f "$template_file" ]; then
        echo "【DRY-RUN】Warning: Template file does not exist (deleted from PR): $template_file"
        echo "【DRY-RUN】Skipping deployment for deleted template."
        continue
      fi
      
      # テンプレートファイルから情報を抽出
      resource_type=$(echo "$template_file" | sed -E 's|cloudformation/templates/([^/]+)/.*|\1|')
      template_name=$(basename "$template_file" .yaml)
      template_name=${template_name%.yml}  # .yml拡張子にも対応

      echo "【DRY-RUN】テンプレート: $template_file (Type: $resource_type, Name: $template_name)"
      echo "【DRY-RUN】実行されるコマンド: ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name"
    done
  else
    echo "【DRY-RUN】変更されたCloudFormationテンプレートが見つかりません"
  fi
}

show_type_specific_templates_dry_run() {
  local affected_types="$1"

  echo "【DRY-RUN】以下のテンプレートがデプロイされます："

  # カンマ区切りのリソースタイプを処理
  IFS=',' read -ra TYPES <<< "$affected_types"
  for resource_type in "${TYPES[@]}"; do
    resource_type=$(echo "$resource_type" | xargs)  # 前後の空白を除去

    # 除外対象のディレクトリをスキップ
    if [[ "$resource_type" == "api-gateway" || "$resource_type" == "secrets-manager" ]]; then
      echo "【DRY-RUN】除外対象ディレクトリのためスキップ: $resource_type"
      continue
    fi

    template_dir="cloudformation/templates/$resource_type"
    if [ -d "$template_dir" ]; then
      for template_file in "$template_dir"/*.yml "$template_dir"/*.yaml; do
        if [ -f "$template_file" ]; then
          template_name=$(basename "$template_file" .yml)
          template_name=${template_name%.yaml}  # Also handle .yaml extension
          echo "【DRY-RUN】テンプレート: $template_file (Type: $resource_type, Name: $template_name)"
          echo "【DRY-RUN】実行されるコマンド: ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name"
        fi
      done
    else
      echo "【DRY-RUN】警告: リソースタイプディレクトリが見つかりません: $resource_type"
    fi
  done
}

show_all_templates_dry_run() {
  echo "【DRY-RUN】以下のテンプレートがデプロイされます："

  for template_dir in cloudformation/templates/*; do
    if [ -d "$template_dir" ]; then
      resource_type=$(basename "$template_dir")

      # 除外対象のディレクトリをスキップ
      if [[ "$resource_type" == "api-gateway" || "$resource_type" == "secrets-manager" ]]; then
        echo "【DRY-RUN】除外対象ディレクトリのためスキップ: $resource_type"
        continue
      fi

      for template_file in "$template_dir"/*.yml "$template_dir"/*.yaml; do
        if [ -f "$template_file" ]; then
          template_name=$(basename "$template_file" .yml)
          template_name=${template_name%.yaml}  # Also handle .yaml extension
          echo "【DRY-RUN】テンプレート: $template_file (Type: $resource_type, Name: $template_name)"
          echo "【DRY-RUN】実行されるコマンド: ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name"
        fi
      done
    fi
  done
}

# 変更がない場合はスキップ
if [ "$HAS_CFN_CHANGES" != "true" ]; then
  echo "No CloudFormation changes detected, skipping CloudFormation deployment."
  exit 0
fi

# DRY-RUNモードの場合
if [ "$DRY_RUN" = "true" ]; then
  echo "【DRY-RUN】CloudFormationテンプレートのデプロイをスキップします"

  # パラメータ変更タイプに応じたデプロイ対象決定
  case "$PARAMETER_CHANGE_TYPE" in
    "environment-common")
      echo "【DRY-RUN】環境共通パラメータ変更のため、全テンプレートをデプロイします"
      show_all_templates_dry_run
      ;;
    "type-common")
      echo "【DRY-RUN】タイプ共通パラメータ変更のため、影響を受けるリソースタイプのテンプレートをデプロイします"
      echo "【DRY-RUN】影響を受けるリソースタイプ: $AFFECTED_RESOURCE_TYPES"
      show_type_specific_templates_dry_run "$AFFECTED_RESOURCE_TYPES"
      ;;
    "resource-specific")
      echo "【DRY-RUN】リソース固有パラメータ変更のため、影響を受けるリソースタイプのテンプレートをデプロイします"
      echo "【DRY-RUN】影響を受けるリソースタイプ: $AFFECTED_RESOURCE_TYPES"
      show_type_specific_templates_dry_run "$AFFECTED_RESOURCE_TYPES"
      ;;
    "template"|"")
      # テンプレート変更または変更タイプ不明の場合
      if [ -n "$CHANGED_FILES" ]; then
        echo "【DRY-RUN】変更されたファイルに基づいてテンプレートをデプロイします"
        show_changed_templates_dry_run "$CHANGED_FILES"
      else
        echo "【DRY-RUN】変更ファイルリストが提供されていません。全テンプレートをデプロイします"
        show_all_templates_dry_run
      fi
      ;;
    *)
      echo "【DRY-RUN】不明なパラメータ変更タイプ: $PARAMETER_CHANGE_TYPE"
      echo "【DRY-RUN】安全のため全テンプレートをデプロイします"
      show_all_templates_dry_run
      ;;
  esac
  exit 0
fi

# 実際のデプロイ処理
chmod +x cloudformation/scripts/cfn_deploy.sh
cd cloudformation

# 変更されたテンプレートのみデプロイ
if [ -n "$CHANGED_FILES" ]; then
  echo "変更されたファイルに基づいてテンプレートをデプロイします"

  # 変更されたCloudFormationテンプレートを抽出（cicd/cloudformationは除外）
  CHANGED_TEMPLATES=$(echo "$CHANGED_FILES" | grep -E "^cloudformation/templates/.*\.ya?ml$" | grep -v -E "cloudformation/templates/(api-gateway|secrets-manager)/" || true)

  if [ -n "$CHANGED_TEMPLATES" ]; then
    echo "変更されたテンプレート:"
    echo "$CHANGED_TEMPLATES"

    for template_file in $CHANGED_TEMPLATES; do
      # ファイル存在チェック（PRから削除されたファイル対応）
      if [ ! -f "$template_file" ]; then
        echo "Warning: Template file does not exist (deleted from PR): $template_file"
        echo "Skipping deployment for deleted template."
        continue
      fi
      
      # テンプレートファイルから情報を抽出
      resource_type=$(echo "$template_file" | sed -E 's|cloudformation/templates/([^/]+)/.*|\1|')
      template_name=$(basename "$template_file" .yaml)
      template_name=${template_name%.yml}  # .yml拡張子にも対応

      echo "Deploying template: $template_file (Type: $resource_type, Name: $template_name)"
      ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name
    done
  else
    echo "変更されたCloudFormationテンプレートが見つかりません"
  fi
else
  echo "変更ファイルリストが提供されていません。全テンプレートをデプロイします"

  # 従来の全テンプレートデプロイ（後方互換性のため）
  for template_dir in templates/*; do
    if [ -d "$template_dir" ]; then
      resource_type=$(basename "$template_dir")

      # 除外対象のディレクトリをスキップ
      if [[ "$resource_type" == "api-gateway" || "$resource_type" == "secrets-manager" ]]; then
        echo "除外対象ディレクトリのためスキップ: $resource_type"
        continue
      fi

      for template_file in "$template_dir"/*.yml "$template_dir"/*.yaml; do
        if [ -f "$template_file" ]; then
          template_name=$(basename "$template_file" .yml)
          template_name=${template_name%.yaml}  # Also handle .yaml extension

          echo "Deploying template: $template_file (Type: $resource_type, Name: $template_name)"
          ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name
        fi
      done
    fi
  done
fi

cd ..

echo "CloudFormation deployment completed."
exit 0
