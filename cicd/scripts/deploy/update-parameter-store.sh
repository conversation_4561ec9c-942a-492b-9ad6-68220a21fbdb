#!/bin/bash
set -e

# Parameter Storeを更新するスクリプト（締切時刻対応版）
# 使用方法: ./update-parameter-store.sh <環境> <DRY_RUNフラグ> [締切時刻]
# 例: ./update-parameter-store.sh "dev" "false" "2025-06-05T11:00:00Z"

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# 引数の検証（最低2つ、最大3つ）
if [ $# -lt 2 ] || [ $# -gt 3 ]; then
  log_error "Invalid number of arguments"
  log_error "Usage: $0 <environment> <dry_run> [deploy_deadline_time]"
  exit 1
fi

ENVIRONMENT="$1"
DRY_RUN="$2"
DEPLOY_DEADLINE_TIME="${3:-}"

# Parameter Store名の設定
DEPLOY_BASELINE_TIME_PARAM="/dlpf/${ENVIRONMENT}/deploy-baseline-time"

# 基準時刻の決定
if [ -n "$DEPLOY_DEADLINE_TIME" ]; then
  # 締切時刻が指定されている場合はそれを使用
  DEPLOYMENT_TIME="$DEPLOY_DEADLINE_TIME"
  log_info "Using provided deadline time as new baseline: ${DEPLOYMENT_TIME}"
else
  # 従来通り現在時刻を使用
  DEPLOYMENT_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
  log_info "Using current time as new baseline: ${DEPLOYMENT_TIME}"
fi

log_info "Updating Parameter Store for environment: ${ENVIRONMENT}"
log_info "New deploy baseline time: ${DEPLOYMENT_TIME}"

# DRY-RUNモードの場合
if [ "$DRY_RUN" = "true" ]; then
  log_dry_run "Parameter Storeの更新をスキップします"
  BASELINE_JSON="{\"deploy_baseline_time\": \"${DEPLOYMENT_TIME}\"}"
  log_dry_run "実行されるコマンド: aws ssm put-parameter --name \"${DEPLOY_BASELINE_TIME_PARAM}\" --value '${BASELINE_JSON}' --type String --overwrite"
  exit 0
fi

# デプロイ基準時刻を更新（JSON形式）
log_info "Updating deploy baseline time parameter: ${DEPLOY_BASELINE_TIME_PARAM}"
BASELINE_JSON="{\"deploy_baseline_time\": \"${DEPLOYMENT_TIME}\"}"
log_info "Parameter value: ${BASELINE_JSON}"
run_with_retry "aws ssm put-parameter --name \"${DEPLOY_BASELINE_TIME_PARAM}\" --value '${BASELINE_JSON}' --type String --overwrite" 3 2 "Failed to update deploy baseline time parameter"

log_info "Parameter Store update completed successfully"
echo "{\"DEPLOYMENT_TIME\":\"${DEPLOYMENT_TIME}\"}"
