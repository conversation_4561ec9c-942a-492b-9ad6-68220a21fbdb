#!/bin/bash
set -e

# CloudFormation変更を検出するスクリプト
# 使用方法: ./detect-cfn.sh <変更ファイルリスト>
# 例: ./detect-cfn.sh "$(cat changed_files.txt)"

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# 引数の検証
if [ $# -eq 0 ]; then
  echo "Usage: $0 <changed_files>"
  exit 1
fi

# すべての引数を改行区切りで結合
CHANGED_FILES=""
for arg in "$@"; do
  if [ -z "$CHANGED_FILES" ]; then
    CHANGED_FILES="$arg"
  else
    CHANGED_FILES="$CHANGED_FILES
$arg"
  fi
done

# 初期化
HAS_CFN_CHANGES=false
EXCLUDED_ONLY=true
PARAMETER_CHANGE_TYPE=""
AFFECTED_RESOURCE_TYPES=""

# パラメータファイル変更分析関数
analyze_parameter_changes() {
  local files="$1"

  # 環境共通パラメータファイル変更の検出
  ENV_COMMON_PARAMS=$(echo "$files" | grep -E "cloudformation/environments/[^/]+/parameters/default\.json$" || true)
  if [ -n "$ENV_COMMON_PARAMS" ]; then
    PARAMETER_CHANGE_TYPE="environment-common"
    log_info "Detected environment-common parameter changes: $ENV_COMMON_PARAMS"
    return
  fi

  # タイプ共通パラメータファイル変更の検出
  TYPE_COMMON_PARAMS=$(echo "$files" | grep -E "cloudformation/environments/[^/]+/parameters/[^/]+/default\.json$" || true)
  if [ -n "$TYPE_COMMON_PARAMS" ]; then
    PARAMETER_CHANGE_TYPE="type-common"
    # 影響を受けるリソースタイプを抽出
    for param_file in $TYPE_COMMON_PARAMS; do
      resource_type=$(echo "$param_file" | sed -E 's|cloudformation/environments/[^/]+/parameters/([^/]+)/default\.json|\1|')
      if [ -n "$AFFECTED_RESOURCE_TYPES" ]; then
        AFFECTED_RESOURCE_TYPES="$AFFECTED_RESOURCE_TYPES,$resource_type"
      else
        AFFECTED_RESOURCE_TYPES="$resource_type"
      fi
    done
    log_info "Detected type-common parameter changes for resource types: $AFFECTED_RESOURCE_TYPES"
    return
  fi

  # リソース固有パラメータファイル変更の検出
  RESOURCE_SPECIFIC_PARAMS=$(echo "$files" | grep -E "cloudformation/environments/[^/]+/parameters/[^/]+/[^/]+\.json$" | grep -v "/default\.json$" || true)
  if [ -n "$RESOURCE_SPECIFIC_PARAMS" ]; then
    PARAMETER_CHANGE_TYPE="resource-specific"
    # 影響を受けるリソースタイプを抽出
    for param_file in $RESOURCE_SPECIFIC_PARAMS; do
      resource_type=$(echo "$param_file" | sed -E 's|cloudformation/environments/[^/]+/parameters/([^/]+)/[^/]+\.json|\1|')
      if [ -n "$AFFECTED_RESOURCE_TYPES" ]; then
        AFFECTED_RESOURCE_TYPES="$AFFECTED_RESOURCE_TYPES,$resource_type"
      else
        AFFECTED_RESOURCE_TYPES="$resource_type"
      fi
    done
    log_info "Detected resource-specific parameter changes for resource types: $AFFECTED_RESOURCE_TYPES"
    return
  fi

  # テンプレートファイル変更の場合
  TEMPLATE_FILES=$(echo "$files" | grep -E "cloudformation/templates/.*\.ya?ml$" || true)
  if [ -n "$TEMPLATE_FILES" ]; then
    PARAMETER_CHANGE_TYPE="template"
    log_info "Detected template file changes"
  fi
}

log_info "Detecting CloudFormation changes..."

# CloudFormation変更の検出（cicd/cloudformationは除外）
CFN_FILES=$(echo "$CHANGED_FILES" | grep -E '^cloudformation/' || true)
if [ -n "$CFN_FILES" ]; then
  log_debug "Found CloudFormation directory changes, checking for excluded directories..."
  log_debug "CloudFormation files: $CFN_FILES"

  # 除外対象以外の変更があるかチェック
  NON_EXCLUDED_FILES=""
  for file in $CFN_FILES; do
    if [[ $file == cloudformation/* ]] && ! [[ $file =~ cloudformation/templates/(api-gateway|secrets-manager)/ ]]; then
      NON_EXCLUDED_FILES="${NON_EXCLUDED_FILES} ${file}"
      log_debug "Found non-excluded CloudFormation change: $file"
    else
      log_debug "Found excluded CloudFormation change: $file"
    fi
  done

  if [ -n "$NON_EXCLUDED_FILES" ]; then
    HAS_CFN_CHANGES=true
    EXCLUDED_ONLY=false
    log_info "Detected CloudFormation changes"
    log_debug "Non-excluded CloudFormation files: $NON_EXCLUDED_FILES"

    # パラメータファイル変更の詳細分析
    analyze_parameter_changes "$NON_EXCLUDED_FILES"
  else
    log_info "Detected CloudFormation changes only in excluded directories (api-gateway, secrets-manager) - skipping deployment"
  fi
else
  log_info "No CloudFormation changes detected"
fi

# 結果をJSON形式で出力
echo "{\"HAS_CFN_CHANGES\":${HAS_CFN_CHANGES},\"PARAMETER_CHANGE_TYPE\":\"${PARAMETER_CHANGE_TYPE}\",\"AFFECTED_RESOURCE_TYPES\":\"${AFFECTED_RESOURCE_TYPES}\"}"
