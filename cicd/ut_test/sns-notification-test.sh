#!/bin/bash
# SNS通知機能テストスクリプト
set -euo pipefail

if [[ "${DEBUG_MODE:-false}" == "true" ]]; then
    set -x
fi

# SNS通知が有効か確認
if [[ "${SNS_NOTIFICATION_ENABLED}" != "true" ]]; then
    echo "SNS通知が無効です。テストをスキップします。"
    exit 0
fi

# 必須パラメータチェック
if [[ -z "${SNS_TOPIC_ARN:-}" ]]; then
    echo "エラー: SNS_TOPIC_ARNが設定されていません"
    exit 1
fi

# AWSリージョン取得
REGION=$(echo "${SNS_TOPIC_ARN}" | cut -d: -f4)

# テストメッセージ送信
echo "SNSトピックにテストメッセージを送信します: ${SNS_TOPIC_ARN}"

aws sns publish \
    --topic-arn "${SNS_TOPIC_ARN}" \
    --region "${REGION}" \
    --subject "SNS通知テスト - ${ENVIRONMENT}環境" \
    --message "これは${ENVIRONMENT}環境のSNS通知機能テストメッセージです。正常に送信されました。"

echo "✅ SNS通知テストが正常に完了しました"
