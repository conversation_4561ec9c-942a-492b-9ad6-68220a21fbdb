#!/bin/bash
# 締切時刻仕組みの統合テスト
# エラーが発生してもスクリプトを続行する
set +e
# デバッグモードを有効にする
set -x

# 現在のディレクトリをスクリプトのディレクトリに変更
cd "$(dirname "$0")"
cd ../..  # リポジトリのルートディレクトリに移動

# 環境変数の設定
export DEBUG_MODE=true
export GITHUB_OWNER="TIS-DSDev"
export GITHUB_REPO="tis-dlpf-app"
export BASE_BRANCH="develop"

echo "=== 締切時刻仕組み統合テスト ==="

# GitHub認証トークンを取得
echo "Fetching GitHub App token..."
INSTALLATION_TOKEN=$(./cicd/scripts/auth/github-auth.sh github-app-credentials | tail -n 1)
if [ -z "$INSTALLATION_TOKEN" ]; then
  echo "Error: Failed to get GitHub App token"
  exit 1
fi

echo "Token obtained successfully. Length: ${#INSTALLATION_TOKEN}"
export GITHUB_TOKEN="$INSTALLATION_TOKEN"

# テスト用Parameter Store名
TEST_PARAM_NAME="/dlpf/test-integration/deploy-baseline-time"
TEST_ENVIRONMENT="test-integration"

# テスト前のクリーンアップ
echo "Cleaning up test parameter..."
aws ssm delete-parameter --name "$TEST_PARAM_NAME" 2>/dev/null || true

# 統合テストシナリオ: 実際のデプロイフローをシミュレート
echo ""
echo "=== 統合テストシナリオ: デプロイフローシミュレート ==="

# Step 1: 初期状態のParameter Store設定
echo ""
echo "Step 1: 初期状態のParameter Store設定"
INITIAL_TIME="2025-05-20T00:00:00Z"
INITIAL_JSON="{\"deploy_baseline_time\": \"$INITIAL_TIME\"}"
aws ssm put-parameter --name "$TEST_PARAM_NAME" --value "$INITIAL_JSON" --type String --overwrite
echo "Initial baseline time set: $INITIAL_TIME"

# Step 2: 現在の基準時刻を取得（build.shの処理をシミュレート）
echo ""
echo "Step 2: 基準時刻取得（build.shシミュレート）"
LAST_DEPLOYED=$(aws ssm get-parameter --name "$TEST_PARAM_NAME" --query "Parameter.Value" --output text 2>/dev/null)
deploy_baseline_time=$(echo $LAST_DEPLOYED | jq -r '.deploy_baseline_time // "1970-01-01T00:00:00Z"')
deploy_deadline_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

echo "Retrieved baseline time: $deploy_baseline_time"
echo "Set deadline time: $deploy_deadline_time"

# Step 3: PR取得（締切時刻を使用）
echo ""
echo "Step 3: PR取得（締切時刻使用）"
echo "Command: get-merged-prs.sh \"$deploy_baseline_time\" \"$deploy_deadline_time\""

TEMP_PR_FILE="/tmp/test_integration_prs_$(date +%s).json"
./cicd/scripts/pr/get-merged-prs.sh "$deploy_baseline_time" "$deploy_deadline_time" > "$TEMP_PR_FILE" 2>&1
PR_EXIT_CODE=$?

if [ $PR_EXIT_CODE -eq 0 ]; then
  PR_COUNT=$(cat "$TEMP_PR_FILE" | jq '. | length' 2>/dev/null || echo '0')
  echo "✅ PR取得成功: $PR_COUNT PRs found"
  
  # PRの詳細を表示（最初の3件）
  if [ "$PR_COUNT" -gt "0" ]; then
    echo "PR details (first 3):"
    cat "$TEMP_PR_FILE" | jq '.[0:3] | .[] | {number: .number, title: .title, merged_at: .merged_at}' 2>/dev/null || echo "Failed to parse PR details"
  fi
else
  echo "❌ PR取得失敗: Exit code $PR_EXIT_CODE"
  cat "$TEMP_PR_FILE"
fi

# Step 4: Parameter Store更新（締切時刻を新しい基準時刻として使用）
echo ""
echo "Step 4: Parameter Store更新（post_build.shシミュレート）"
echo "Command: update-parameter-store.sh \"$TEST_ENVIRONMENT\" \"false\" \"$deploy_deadline_time\""

UPDATE_RESULT=$(./cicd/scripts/deploy/update-parameter-store.sh "$TEST_ENVIRONMENT" "false" "$deploy_deadline_time" 2>&1)
UPDATE_EXIT_CODE=$?

echo "Update exit code: $UPDATE_EXIT_CODE"
echo "Update result: $UPDATE_RESULT"

if [ $UPDATE_EXIT_CODE -eq 0 ]; then
  echo "✅ Parameter Store更新成功"
  
  # 更新後の値を確認
  UPDATED_VALUE=$(aws ssm get-parameter --name "$TEST_PARAM_NAME" --query "Parameter.Value" --output text 2>/dev/null)
  UPDATED_TIME=$(echo "$UPDATED_VALUE" | jq -r '.deploy_baseline_time' 2>/dev/null)
  
  if [ "$UPDATED_TIME" = "$deploy_deadline_time" ]; then
    echo "✅ 基準時刻が締切時刻で正しく更新されました"
    echo "Expected: $deploy_deadline_time"
    echo "Actual: $UPDATED_TIME"
  else
    echo "❌ 基準時刻の更新が正しくありません"
    echo "Expected: $deploy_deadline_time"
    echo "Actual: $UPDATED_TIME"
  fi
else
  echo "❌ Parameter Store更新失敗"
fi

# Step 5: 次回デプロイのシミュレート（タイムラグ問題の解決確認）
echo ""
echo "Step 5: 次回デプロイシミュレート（タイムラグ問題解決確認）"

# 少し未来の時刻を締切時刻として設定
sleep 2
NEXT_DEADLINE_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

echo "Next deployment simulation:"
echo "Previous deadline (now baseline): $deploy_deadline_time"
echo "New deadline: $NEXT_DEADLINE_TIME"

# 次回のPR取得
TEMP_PR_FILE2="/tmp/test_integration_prs2_$(date +%s).json"
./cicd/scripts/pr/get-merged-prs.sh "$deploy_deadline_time" "$NEXT_DEADLINE_TIME" > "$TEMP_PR_FILE2" 2>&1
NEXT_PR_EXIT_CODE=$?

if [ $NEXT_PR_EXIT_CODE -eq 0 ]; then
  NEXT_PR_COUNT=$(cat "$TEMP_PR_FILE2" | jq '. | length' 2>/dev/null || echo '0')
  echo "✅ 次回PR取得成功: $NEXT_PR_COUNT PRs found"
  echo "Range: $deploy_deadline_time < merged_at <= $NEXT_DEADLINE_TIME"
else
  echo "❌ 次回PR取得失敗: Exit code $NEXT_PR_EXIT_CODE"
fi

# Step 6: タイムラグ問題解決の検証
echo ""
echo "Step 6: タイムラグ問題解決の検証"

echo "検証ポイント:"
echo "1. 基準時刻が締切時刻で更新されている: $([ "$UPDATED_TIME" = "$deploy_deadline_time" ] && echo 'OK' || echo 'NG')"
echo "2. 次回デプロイで前回の締切時刻が基準時刻として使用される: OK (confirmed by Step 5)"
echo "3. PR検索範囲が確定的である: OK (baseline < merged_at <= deadline)"

# 結果サマリー
echo ""
echo "=== 統合テスト結果サマリー ==="
echo "PR取得 (Step 3): $([ $PR_EXIT_CODE -eq 0 ] && echo 'PASS' || echo 'FAIL')"
echo "Parameter Store更新 (Step 4): $([ $UPDATE_EXIT_CODE -eq 0 ] && echo 'PASS' || echo 'FAIL')"
echo "次回PR取得 (Step 5): $([ $NEXT_PR_EXIT_CODE -eq 0 ] && echo 'PASS' || echo 'FAIL')"
echo "基準時刻更新検証: $([ "$UPDATED_TIME" = "$deploy_deadline_time" ] && echo 'PASS' || echo 'FAIL')"

# 全体的な成功判定
OVERALL_SUCCESS=true
[ $PR_EXIT_CODE -ne 0 ] && OVERALL_SUCCESS=false
[ $UPDATE_EXIT_CODE -ne 0 ] && OVERALL_SUCCESS=false
[ $NEXT_PR_EXIT_CODE -ne 0 ] && OVERALL_SUCCESS=false
[ "$UPDATED_TIME" != "$deploy_deadline_time" ] && OVERALL_SUCCESS=false

if [ "$OVERALL_SUCCESS" = "true" ]; then
  echo ""
  echo "🎉 統合テスト全体: PASS"
  echo "締切時刻仕組みが正常に動作しています"
else
  echo ""
  echo "❌ 統合テスト全体: FAIL"
  echo "一部のテストが失敗しました"
fi

# テスト後のクリーンアップ
echo ""
echo "Cleaning up test files and parameters..."
rm -f "$TEMP_PR_FILE" "$TEMP_PR_FILE2"
aws ssm delete-parameter --name "$TEST_PARAM_NAME" 2>/dev/null || true

echo ""
echo "=== 締切時刻仕組み統合テスト完了 ==="
echo "Tests completed!"
