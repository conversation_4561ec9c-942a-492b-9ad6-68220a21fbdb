#!/bin/bash
set -e

# 改修した検出スクリプトのテスト
echo "=== Testing Modified Detection Scripts ==="

cd ../..
export CODEBUILD_SRC_DIR=$(pwd)

# テスト用のファイルリスト（PR #1483の実際のファイル）
TEST_FILES="cicd/release_tool/universal-issue-commits-tool.md
cicd/release_tool/universal-issue-commits-tool.sh
cicd/scripts/codebuild/common.sh
cloudformation/templates/glue-connector/dlpf_glue_connection.yaml
cloudformation/templates/glue-job/dlpf_job_convert_character_encoding.yaml
cloudformation/templates/glue-job/dlpf_job_convert_format.yaml
cloudformation/templates/glue-job/dlpf_job_file_compress.yaml
job/source/glue_job_db_to_file.py"

echo "Testing with actual changed files from PR #1483:"
echo "$TEST_FILES"
echo ""

# Test 1: detect-glue.sh
echo "=== Test 1: detect-glue.sh ==="
GLUE_RESULT=$(./cicd/scripts/detect/detect-glue.sh $TEST_FILES)
echo "Result: $GLUE_RESULT"

# JSON解析
HAS_GLUE_DEPENDENCY_CHANGES=$(echo "$GLUE_RESULT" | jq -r '.HAS_GLUE_DEPENDENCY_CHANGES')
HAS_GLUE_SOURCE_CHANGES=$(echo "$GLUE_RESULT" | jq -r '.HAS_GLUE_SOURCE_CHANGES')
HAS_GLUE_JOB_SCRIPT_CHANGES=$(echo "$GLUE_RESULT" | jq -r '.HAS_GLUE_JOB_SCRIPT_CHANGES')

echo "Parsed results:"
echo "  HAS_GLUE_DEPENDENCY_CHANGES: $HAS_GLUE_DEPENDENCY_CHANGES"
echo "  HAS_GLUE_SOURCE_CHANGES: $HAS_GLUE_SOURCE_CHANGES"
echo "  HAS_GLUE_JOB_SCRIPT_CHANGES: $HAS_GLUE_JOB_SCRIPT_CHANGES"

# 期待値チェック（job/source/glue_job_db_to_file.pyが含まれているため）
if [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
  echo "✅ Glue job script detection: PASSED"
else
  echo "❌ Glue job script detection: FAILED"
  exit 1
fi

echo ""

# Test 2: detect-cfn.sh
echo "=== Test 2: detect-cfn.sh ==="
CFN_RESULT=$(./cicd/scripts/detect/detect-cfn.sh $TEST_FILES)
echo "Result: $CFN_RESULT"

# JSON解析
HAS_CFN_CHANGES=$(echo "$CFN_RESULT" | jq -r '.HAS_CFN_CHANGES')
PARAMETER_CHANGE_TYPE=$(echo "$CFN_RESULT" | jq -r '.PARAMETER_CHANGE_TYPE')

echo "Parsed results:"
echo "  HAS_CFN_CHANGES: $HAS_CFN_CHANGES"
echo "  PARAMETER_CHANGE_TYPE: $PARAMETER_CHANGE_TYPE"

# 期待値チェック（cloudformation/templates/が含まれているため）
if [ "$HAS_CFN_CHANGES" = "true" ]; then
  echo "✅ CloudFormation template detection: PASSED"
else
  echo "❌ CloudFormation template detection: FAILED"
  exit 1
fi

echo ""

# Test 3: detect-lambda.sh
echo "=== Test 3: detect-lambda.sh ==="
LAMBDA_RESULT=$(./cicd/scripts/detect/detect-lambda.sh $TEST_FILES)
echo "Result: $LAMBDA_RESULT"

# JSON解析
HAS_LAMBDA_CHANGES=$(echo "$LAMBDA_RESULT" | jq -r '.HAS_LAMBDA_CHANGES')

echo "Parsed results:"
echo "  HAS_LAMBDA_CHANGES: $HAS_LAMBDA_CHANGES"

# 期待値チェック（Lambdaファイルは含まれていないため）
if [ "$HAS_LAMBDA_CHANGES" = "false" ]; then
  echo "✅ Lambda detection (no changes): PASSED"
else
  echo "❌ Lambda detection (no changes): FAILED"
  exit 1
fi

echo ""
echo "=== All Detection Tests PASSED! ==="
