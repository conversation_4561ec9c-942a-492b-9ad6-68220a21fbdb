#!/bin/bash
# エラーが発生してもスクリプトを続行する
set +e
# デバッグモードを有効にする
set -x

# 現在のディレクトリをスクリプトのディレクトリに変更
cd "$(dirname "$0")"
cd ../..  # リポジトリのルートディレクトリに移動

# 環境変数の設定
export DEBUG_MODE=true
export GITHUB_OWNER="TIS-DSDev"
export GITHUB_REPO="tis-dlpf-app"
export BASE_BRANCH="develop"

# GitHub APIへのアクセスにはプロキシが不要（インフラチームからのフィードバックに基づく）
# 念のためプロキシ設定を確認
echo "Checking proxy settings..."
if [ -n "$http_proxy" ] || [ -n "$https_proxy" ]; then
  echo "Proxy settings detected, but not needed for GitHub API access:"
  echo "http_proxy: $http_proxy"
  echo "https_proxy: $https_proxy"
  echo "no_proxy: $no_proxy"
else
  echo "No proxy settings detected. This is fine for GitHub API access."
fi

# GitHub認証トークンを取得
echo "Fetching GitHub App token..."
# DEBUG_MODEを有効にしてトークンを取得
export DEBUG_MODE=true
INSTALLATION_TOKEN=$(./cicd/scripts/auth/github-auth.sh github-app-credentials | tail -n 1)
if [ -z "$INSTALLATION_TOKEN" ]; then
  echo "Error: Failed to get GitHub App token"
  exit 1
fi

echo "Token obtained successfully. Length: ${#INSTALLATION_TOKEN}"
echo "Token preview: ${INSTALLATION_TOKEN:0:10}..."

# get-merged-prs.shスクリプトのテスト
echo "Testing get-merged-prs.sh script..."

# 一時ファイルのパスを設定
TEMP_FILE1="/tmp/test_result_$(date +%s)_1.json"
TEMP_FILE2="/tmp/test_result_$(date +%s)_2.json"

# テストケース1: 初回デプロイ時刻として実行（エラーが返されることを確認）
echo "Test Case 1: Initial deployment time should return an error"
# GITHUB_TOKENを環境変数として設定
export GITHUB_TOKEN="$INSTALLATION_TOKEN"
./cicd/scripts/pr/get-merged-prs.sh "1970-01-01T00:00:00Z" > "$TEMP_FILE1" || true
EXIT_CODE=$?
RESULT=$(cat "$TEMP_FILE1")

echo "Exit code: $EXIT_CODE"
echo "Result:"
# 結果を表示
echo "$RESULT"
# エラーメッセージが含まれていることを確認
if echo "$RESULT" | grep -q "初回デプロイ（1970-01-01T00:00:00Z）は上位レベルで処理されるべきパターンです"; then
  echo "✅ Test passed: Error message detected"
else
  echo "❌ Test failed: Error message not detected"
fi

# テストケース2: 特定の時刻以降のPRを取得
echo "Test Case 2: Get PRs since 2025-05-20T00:00:00Z"
# 時刻ベースの引数で実行
./cicd/scripts/pr/get-merged-prs.sh "2025-05-20T00:00:00Z" > "$TEMP_FILE2"
EXIT_CODE=$?
RESULT=$(cat "$TEMP_FILE2")

echo "Exit code: $EXIT_CODE"
echo "Result:"
# 結果の最後の部分だけを表示（JSONデータ部分）
echo "$RESULT" | grep -A 100 "Found.*PRs to process" | tail -n 20

# 一時ファイルの削除
rm -f "$TEMP_FILE1" "$TEMP_FILE2"
echo "一時ファイルを削除しました: $TEMP_FILE1, $TEMP_FILE2"

# テスト完了
echo "Test execution completed successfully"

echo "Tests completed!"
