#!/bin/bash
# 締切時刻仕組み対応のPR取得テスト
# エラーが発生してもスクリプトを続行する
set +e
# デバッグモードを有効にする
set -x

# 現在のディレクトリをスクリプトのディレクトリに変更
cd "$(dirname "$0")"
cd ../..  # リポジトリのルートディレクトリに移動

# 環境変数の設定
export DEBUG_MODE=true
export GITHUB_OWNER="TIS-DSDev"
export GITHUB_REPO="tis-dlpf-app"
export BASE_BRANCH="develop"

echo "=== 締切時刻仕組み対応のPR取得テスト ==="

# GitHub認証トークンを取得
echo "Fetching GitHub App token..."
export DEBUG_MODE=true
INSTALLATION_TOKEN=$(./cicd/scripts/auth/github-auth.sh github-app-credentials | tail -n 1)
if [ -z "$INSTALLATION_TOKEN" ]; then
  echo "Error: Failed to get GitHub App token"
  exit 1
fi

echo "Token obtained successfully. Length: ${#INSTALLATION_TOKEN}"
export GITHUB_TOKEN="$INSTALLATION_TOKEN"

# 一時ファイルのパスを設定
TEMP_FILE1="/tmp/test_deadline_$(date +%s)_1.json"
TEMP_FILE2="/tmp/test_deadline_$(date +%s)_2.json"
TEMP_FILE3="/tmp/test_deadline_$(date +%s)_3.json"

# テストケース1: 締切時刻なし（従来方式）
echo ""
echo "=== Test Case 1: 締切時刻なし（従来方式） ==="
echo "Command: ./cicd/scripts/pr/get-merged-prs.sh \"2025-05-20T00:00:00Z\""
./cicd/scripts/pr/get-merged-prs.sh "2025-05-20T00:00:00Z" > "$TEMP_FILE1" 2>&1
EXIT_CODE1=$?
RESULT1=$(cat "$TEMP_FILE1")

echo "Exit code: $EXIT_CODE1"
if [ $EXIT_CODE1 -eq 0 ]; then
  PR_COUNT1=$(echo "$RESULT1" | jq '. | length' 2>/dev/null || echo '0')
  echo "✅ Test 1 passed: Found $PR_COUNT1 PRs (deadline time auto-set to current time)"
else
  echo "❌ Test 1 failed: Exit code $EXIT_CODE1"
  echo "Error output: $RESULT1"
fi

# テストケース2: 締切時刻指定（狭い範囲）
echo ""
echo "=== Test Case 2: 締切時刻指定（狭い範囲） ==="
echo "Command: ./cicd/scripts/pr/get-merged-prs.sh \"2025-05-20T00:00:00Z\" \"2025-05-25T00:00:00Z\""
./cicd/scripts/pr/get-merged-prs.sh "2025-05-20T00:00:00Z" "2025-05-25T00:00:00Z" > "$TEMP_FILE2" 2>&1
EXIT_CODE2=$?
RESULT2=$(cat "$TEMP_FILE2")

echo "Exit code: $EXIT_CODE2"
if [ $EXIT_CODE2 -eq 0 ]; then
  PR_COUNT2=$(echo "$RESULT2" | jq '. | length' 2>/dev/null || echo '0')
  echo "✅ Test 2 passed: Found $PR_COUNT2 PRs in specified range"
  echo "Range: 2025-05-20T00:00:00Z < merged_at <= 2025-05-25T00:00:00Z"
else
  echo "❌ Test 2 failed: Exit code $EXIT_CODE2"
  echo "Error output: $RESULT2"
fi

# テストケース3: 締切時刻指定（非常に狭い範囲 - 結果0件を期待）
echo ""
echo "=== Test Case 3: 締切時刻指定（非常に狭い範囲） ==="
NARROW_START="2025-01-01T00:00:00Z"
NARROW_END="2025-01-01T01:00:00Z"
echo "Command: ./cicd/scripts/pr/get-merged-prs.sh \"$NARROW_START\" \"$NARROW_END\""
./cicd/scripts/pr/get-merged-prs.sh "$NARROW_START" "$NARROW_END" > "$TEMP_FILE3" 2>&1
EXIT_CODE3=$?
RESULT3=$(cat "$TEMP_FILE3")

echo "Exit code: $EXIT_CODE3"
if [ $EXIT_CODE3 -eq 0 ]; then
  PR_COUNT3=$(echo "$RESULT3" | jq '. | length' 2>/dev/null || echo '0')
  echo "✅ Test 3 passed: Found $PR_COUNT3 PRs in narrow range (expected 0)"
  echo "Range: $NARROW_START < merged_at <= $NARROW_END"
else
  echo "❌ Test 3 failed: Exit code $EXIT_CODE3"
  echo "Error output: $RESULT3"
fi

# テストケース4: 不正な締切時刻（エラーを期待）
echo ""
echo "=== Test Case 4: 不正な締切時刻（エラーを期待） ==="
echo "Command: ./cicd/scripts/pr/get-merged-prs.sh \"2025-05-20T00:00:00Z\" \"invalid-date\""
./cicd/scripts/pr/get-merged-prs.sh "2025-05-20T00:00:00Z" "invalid-date" > /dev/null 2>&1
EXIT_CODE4=$?

if [ $EXIT_CODE4 -ne 0 ]; then
  echo "✅ Test 4 passed: Invalid deadline time correctly rejected (exit code: $EXIT_CODE4)"
else
  echo "❌ Test 4 failed: Invalid deadline time was accepted"
fi

# テストケース5: 基準時刻 > 締切時刻（論理エラー）
echo ""
echo "=== Test Case 5: 基準時刻 > 締切時刻（論理エラー） ==="
echo "Command: ./cicd/scripts/pr/get-merged-prs.sh \"2025-05-25T00:00:00Z\" \"2025-05-20T00:00:00Z\""
./cicd/scripts/pr/get-merged-prs.sh "2025-05-25T00:00:00Z" "2025-05-20T00:00:00Z" > /dev/null 2>&1
EXIT_CODE5=$?
RESULT5=$(cat /dev/null)

echo "Exit code: $EXIT_CODE5"
if [ $EXIT_CODE5 -eq 0 ]; then
  # 結果が空配列であることを確認
  echo "✅ Test 5 passed: Logical error handled gracefully (baseline > deadline)"
  echo "Note: This should return empty result, not an error"
else
  echo "❌ Test 5 failed: Unexpected error for baseline > deadline case"
fi

# 結果比較
echo ""
echo "=== 結果比較 ==="
echo "Test 1 (no deadline): $PR_COUNT1 PRs"
echo "Test 2 (with deadline): $PR_COUNT2 PRs"
echo "Test 3 (narrow range): $PR_COUNT3 PRs"
echo ""
echo "期待される結果:"
echo "- Test 1 >= Test 2 (締切時刻により結果が絞り込まれる)"
echo "- Test 3 = 0 (非常に狭い範囲では結果なし)"

# 論理チェック
if [ "$PR_COUNT1" -ge "$PR_COUNT2" ] && [ "$PR_COUNT3" -eq "0" ]; then
  echo "✅ Logic check passed: Results are consistent with deadline time filtering"
else
  echo "❌ Logic check failed: Results are inconsistent"
  echo "  PR_COUNT1($PR_COUNT1) should be >= PR_COUNT2($PR_COUNT2)"
  echo "  PR_COUNT3($PR_COUNT3) should be 0"
fi

# 一時ファイルの削除
rm -f "$TEMP_FILE1" "$TEMP_FILE2" "$TEMP_FILE3"
echo ""
echo "一時ファイルを削除しました"

# テスト完了
echo ""
echo "=== 締切時刻仕組みテスト完了 ==="
echo "Tests completed!"
