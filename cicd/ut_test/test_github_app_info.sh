#!/bin/bash
set -e

# 現在のディレクトリをスクリプトのディレクトリに変更
cd "$(dirname "$0")"
cd ../..  # リポジトリのルートディレクトリに移動

# シークレット名
SECRET_NAME="github-app-credentials"

# シークレットを取得
echo "Fetching secret: $SECRET_NAME"
SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id "$SECRET_NAME" --query SecretString --output text)

if [ -z "$SECRET_JSON" ]; then
  echo "Error: Failed to retrieve secret or empty secret content"
  exit 1
fi

# シークレットからGitHub App情報を抽出
APP_ID=$(echo "$SECRET_JSON" | jq -r '.app_id')
INSTALLATION_ID=$(echo "$SECRET_JSON" | jq -r '.installation_id')
PRIVATE_KEY=$(echo "$SECRET_JSON" | jq -r '.private_key')

echo "App ID: $APP_ID"
echo "Installation ID: $INSTALLATION_ID"
echo "Private Key length: ${#PRIVATE_KEY} characters"

# 現在時刻とJWT有効期限を設定
export TZ=UTC
NOW=$(date +%s)
EXP=$((NOW + 540))  # 9分後

echo "Current time: $NOW"
echo "Expiration time: $EXP"

# JWTを生成
echo "Generating JWT..."
JWT=$(./cicd/scripts/auth/generate_jwt.sh "$APP_ID" "$PRIVATE_KEY" "$NOW" "$EXP")
JWT_RESULT=$?

if [ $JWT_RESULT -ne 0 ]; then
  echo "Error: Failed to generate JWT token"
  exit 2
fi

if [ -z "$JWT" ]; then
  echo "Error: Empty JWT token generated"
  exit 2
fi

echo "JWT token generated successfully. Length: ${#JWT} characters"
echo "JWT token: ${JWT:0:20}...${JWT:(-20)}"

# GitHub APIへのアクセスにはプロキシが不要（インフラチームからのフィードバックに基づく）
echo "Checking proxy settings..."
if [ -n "$http_proxy" ] || [ -n "$https_proxy" ]; then
  echo "Proxy settings detected, but not needed for GitHub API access:"
  echo "http_proxy: $http_proxy"
  echo "https_proxy: $https_proxy"
  echo "no_proxy: $no_proxy"
else
  echo "No proxy settings detected. This is fine for GitHub API access."
fi

# GitHub Appの情報を取得
echo "Fetching GitHub App information..."
APP_INFO=$(curl -s --connect-timeout 10 \
  -H "Authorization: Bearer ${JWT}" \
  -H "Accept: application/vnd.github+json" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  "https://api.github.com/app" || echo '{"error":"Failed to get app info"}')

echo "App Info Response: $APP_INFO"

# GitHub Appのインストール情報を取得
echo "Fetching GitHub App installations..."
INSTALLATIONS=$(curl -s --connect-timeout 10 \
  -H "Authorization: Bearer ${JWT}" \
  -H "Accept: application/vnd.github+json" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  "https://api.github.com/app/installations" || echo '{"error":"Failed to get installations"}')

echo "Installations Response: $INSTALLATIONS"

# GitHub APIへのアクセス完了
echo "GitHub API access completed"

echo "Test completed!"
