#!/bin/bash
# エラーが発生してもスクリプトを続行する
set +e
# デバッグモードを有効にする
set -x

# 現在のディレクトリをスクリプトのディレクトリに変更
cd "$(dirname "$0")"
cd ../..  # リポジトリのルートディレクトリに移動

# シークレット名
SECRET_NAME="github-app-credentials"

# シークレットを取得
echo "Fetching secret: $SECRET_NAME"
SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id "$SECRET_NAME" --query SecretString --output text)

if [ -z "$SECRET_JSON" ]; then
  echo "Error: Failed to retrieve secret or empty secret content"
  exit 1
fi

# シークレットからGitHub App情報を抽出
APP_ID=$(echo "$SECRET_JSON" | jq -r '.app_id')
INSTALLATION_ID=$(echo "$SECRET_JSON" | jq -r '.installation_id')
PRIVATE_KEY=$(echo "$SECRET_JSON" | jq -r '.private_key')

echo "App ID: $APP_ID"
echo "Installation ID: $INSTALLATION_ID"
echo "Private Key length: ${#PRIVATE_KEY} characters"

# 現在時刻とJWT有効期限を設定
export TZ=UTC
NOW=$(date +%s)
EXP=$((NOW + 540))  # 9分後

echo "Current time: $NOW"
echo "Expiration time: $EXP"

# JWTを生成
echo "Generating JWT..."
JWT=$(./cicd/scripts/auth/generate_jwt.sh "$APP_ID" "$PRIVATE_KEY" "$NOW" "$EXP")
JWT_RESULT=$?

if [ $JWT_RESULT -ne 0 ]; then
  echo "Error: Failed to generate JWT token"
  exit 2
fi

if [ -z "$JWT" ]; then
  echo "Error: Empty JWT token generated"
  exit 2
fi

echo "JWT token generated successfully. Length: ${#JWT} characters"
echo "JWT token: ${JWT:0:20}...${JWT:(-20)}"

# GitHub APIにリクエストを送信してインストールアクセストークンを取得
echo "Requesting Installation Access Token..."

# GitHub APIへのアクセスにはプロキシが不要（インフラチームからのフィードバックに基づく）
echo "Checking proxy settings..."
if [ -n "$http_proxy" ] || [ -n "$https_proxy" ]; then
  echo "Proxy settings detected, but not needed for GitHub API access:"
  echo "http_proxy: $http_proxy"
  echo "https_proxy: $https_proxy"
  echo "no_proxy: $no_proxy"
else
  echo "No proxy settings detected. This is fine for GitHub API access."
fi

# GitHub APIにリクエストを送信
echo "Sending request to GitHub API..."
INSTALLATION_TOKEN_RESPONSE=$(curl -s -v --http1.1 -X POST \
  -H "Authorization: Bearer ${JWT}" \
  -H "Accept: application/vnd.github+json" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  -H "Connection: close" \
  "https://api.github.com/app/installations/${INSTALLATION_ID}/access_tokens" 2>&1 || echo '{"error":"Failed to get token"}')

# GitHub APIへのアクセス完了
echo "GitHub API access completed"

echo "Response: $INSTALLATION_TOKEN_RESPONSE"

# レスポンスからトークンを抽出
echo "Extracting token from response..."
INSTALLATION_TOKEN=$(echo "$INSTALLATION_TOKEN_RESPONSE" | grep -o '"token": *"[^"]*"' | sed 's/"token": *"\([^"]*\)"/\1/')

if [ -z "$INSTALLATION_TOKEN" ]; then
  echo "Error: Failed to get Installation Access Token"
  # エラーでも続行する
  INSTALLATION_TOKEN="unknown"
fi

echo "Installation Access Token obtained successfully. Token: ${INSTALLATION_TOKEN:0:10}..."

# トークンの有効期限を確認
echo "Extracting token expiration date..."
TOKEN_EXPIRES_AT=$(echo "$INSTALLATION_TOKEN_RESPONSE" | grep -o '"expires_at": *"[^"]*"' | sed 's/"expires_at": *"\([^"]*\)"/\1/')
if [ ! -z "$TOKEN_EXPIRES_AT" ]; then
  echo "Token expires at: $TOKEN_EXPIRES_AT"
fi

# トークンの権限を確認
echo "Extracting token permissions..."
TOKEN_PERMISSIONS=$(echo "$INSTALLATION_TOKEN_RESPONSE" | grep -o '"permissions": *{[^}]*}' | sed 's/"permissions": *{\([^}]*\)}/\1/')
if [ ! -z "$TOKEN_PERMISSIONS" ]; then
  echo "Token permissions: $TOKEN_PERMISSIONS"
fi

echo "========================================================"
echo "GitHub App認証テスト結果"
echo "========================================================"
echo "App ID: $APP_ID"
echo "Installation ID: $INSTALLATION_ID"
echo "JWT生成: 成功"
echo "インストールアクセストークン取得: 成功"
echo "トークン: ${INSTALLATION_TOKEN:0:10}..."
echo "有効期限: $TOKEN_EXPIRES_AT"
echo "権限: $TOKEN_PERMISSIONS"
echo "========================================================"
echo "GitHub App認証テストが正常に完了しました！"
echo "========================================================"
