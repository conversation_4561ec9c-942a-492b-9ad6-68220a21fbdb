#!/bin/bash
# SNS通知機能単体テストスクリプト
set -euo pipefail

echo "=== SNS通知機能テスト開始 ==="

# テスト用設定
export ENVIRONMENT="dev"
export SNS_NOTIFICATION_ENABLED="true"
export SNS_TOPIC_ARN="arn:aws:sns:ap-northeast-1:123456789012:dlpf-dev-error-notification-topic"

# モック用のAWS CLI関数（認証なし）
# モック用のAWS CLI関数（認証なし）
aws() {
  export AWS_ACCESS_KEY_ID="test"
  export AWS_SECRET_ACCESS_KEY="test"
  export AWS_DEFAULT_REGION="ap-northeast-1"
  export AWS_SESSION_TOKEN="test"
  export AWS_SDK_LOAD_CONFIG=0
  export AWS_SHARED_CREDENTIALS_FILE=""
  echo "[MOCK] aws $@"
  echo "[MOCK] AWS CLI version: aws-cli/2.0.0 (mocked)"
  if [[ "$1" == "sns" && "$2" == "publish" ]]; then
    echo "✅ テストメッセージ送信成功（モック）"
    echo "[MOCK] aws sns publish --topic-arn \$SNS_TOPIC_ARN --message テストメッセージ --subject テスト"
    return 0
  fi
  return 1
}

echo "テストケース1: 正常系テスト"
export -f aws  # モック関数をサブシェルにエクスポート
./sns-notification-test.sh

# テストケース2: SNS通知無効時のテスト
echo "テストケース2: SNS通知無効時のテスト"
export SNS_NOTIFICATION_ENABLED="false"
./sns-notification-test.sh

# テストケース3: SNSトピックARN未設定時のテスト（エラー期待テスト）
echo "テストケース3: SNSトピックARN未設定時のテスト"
export SNS_NOTIFICATION_ENABLED="true"
unset SNS_TOPIC_ARN
if ./sns-notification-test.sh; then
    echo "❌ テストケース3失敗: エラーが期待されましたが、正常終了しました"
    exit 1
else
    echo "✅ テストケース3成功: 期待通りエラーが発生しました（SNS_TOPIC_ARN未設定）"
fi

# テストケース4: エラーハンドリング改善後の正常系テスト
echo "テストケース4: エラーハンドリング改善後の正常系テスト"
export SNS_NOTIFICATION_ENABLED="true"
export SNS_TOPIC_ARN="arn:aws:sns:ap-northeast-1:123456789012:dlpf-dev-error-notification-topic"
./sns-notification-test.sh
echo "✅ テストケース4成功: エラーハンドリング改善後も正常動作"

echo "=== SNS通知機能テスト完了 ==="
