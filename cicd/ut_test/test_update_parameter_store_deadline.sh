#!/bin/bash
# 締切時刻仕組み対応のParameter Store更新テスト
# エラーが発生してもスクリプトを続行する
set +e
# デバッグモードを有効にする
set -x

# 現在のディレクトリをスクリプトのディレクトリに変更
cd "$(dirname "$0")"
cd ../..  # リポジトリのルートディレクトリに移動

# 環境変数の設定
export DEBUG_MODE=true

echo "=== 締切時刻仕組み対応のParameter Store更新テスト ==="

# テスト用Parameter Store名（実際のものとは別）
TEST_PARAM_NAME="/dlpf/test/deploy-baseline-time"
TEST_ENVIRONMENT="test"

# テスト前のクリーンアップ
echo "Cleaning up test parameter..."
aws ssm delete-parameter --name "$TEST_PARAM_NAME" 2>/dev/null || true

# テストケース1: 締切時刻なし（従来方式）
echo ""
echo "=== Test Case 1: 締切時刻なし（従来方式） ==="
echo "Command: ./cicd/scripts/deploy/update-parameter-store.sh \"$TEST_ENVIRONMENT\" \"true\""

# DRY-RUNモードで実行
RESULT1=$(./cicd/scripts/deploy/update-parameter-store.sh "$TEST_ENVIRONMENT" "true" 2>&1)
EXIT_CODE1=$?

echo "Exit code: $EXIT_CODE1"
echo "Result: $RESULT1"

if [ $EXIT_CODE1 -eq 0 ] && echo "$RESULT1" | grep -q "Parameter Storeの更新をスキップします"; then
  echo "✅ Test 1 passed: DRY-RUN mode works without deadline time"
else
  echo "❌ Test 1 failed: DRY-RUN mode failed"
fi

# テストケース2: 締切時刻指定（DRY-RUNモード）
echo ""
echo "=== Test Case 2: 締切時刻指定（DRY-RUNモード） ==="
DEADLINE_TIME="2025-06-05T11:00:00Z"
echo "Command: ./cicd/scripts/deploy/update-parameter-store.sh \"$TEST_ENVIRONMENT\" \"true\" \"$DEADLINE_TIME\""

RESULT2=$(./cicd/scripts/deploy/update-parameter-store.sh "$TEST_ENVIRONMENT" "true" "$DEADLINE_TIME" 2>&1)
EXIT_CODE2=$?

echo "Exit code: $EXIT_CODE2"
echo "Result: $RESULT2"

if [ $EXIT_CODE2 -eq 0 ] && echo "$RESULT2" | grep -q "Using provided deadline time as new baseline: $DEADLINE_TIME"; then
  echo "✅ Test 2 passed: DRY-RUN mode works with deadline time"
else
  echo "❌ Test 2 failed: DRY-RUN mode with deadline time failed"
fi

# テストケース3: 実際のParameter Store更新（締切時刻なし）
echo ""
echo "=== Test Case 3: 実際のParameter Store更新（締切時刻なし） ==="
echo "Command: ./cicd/scripts/deploy/update-parameter-store.sh \"$TEST_ENVIRONMENT\" \"false\""

RESULT3=$(./cicd/scripts/deploy/update-parameter-store.sh "$TEST_ENVIRONMENT" "false" 2>&1)
EXIT_CODE3=$?

echo "Exit code: $EXIT_CODE3"
echo "Result: $RESULT3"

if [ $EXIT_CODE3 -eq 0 ]; then
  # Parameter Storeから値を確認
  STORED_VALUE=$(aws ssm get-parameter --name "$TEST_PARAM_NAME" --query "Parameter.Value" --output text 2>/dev/null)
  if [ -n "$STORED_VALUE" ]; then
    STORED_TIME=$(echo "$STORED_VALUE" | jq -r '.deploy_baseline_time' 2>/dev/null)
    echo "✅ Test 3 passed: Parameter Store updated successfully"
    echo "Stored time: $STORED_TIME"
  else
    echo "❌ Test 3 failed: Parameter Store value not found"
  fi
else
  echo "❌ Test 3 failed: Parameter Store update failed"
fi

# テストケース4: 実際のParameter Store更新（締切時刻指定）
echo ""
echo "=== Test Case 4: 実際のParameter Store更新（締切時刻指定） ==="
DEADLINE_TIME_2="2025-06-05T12:00:00Z"
echo "Command: ./cicd/scripts/deploy/update-parameter-store.sh \"$TEST_ENVIRONMENT\" \"false\" \"$DEADLINE_TIME_2\""

RESULT4=$(./cicd/scripts/deploy/update-parameter-store.sh "$TEST_ENVIRONMENT" "false" "$DEADLINE_TIME_2" 2>&1)
EXIT_CODE4=$?

echo "Exit code: $EXIT_CODE4"
echo "Result: $RESULT4"

if [ $EXIT_CODE4 -eq 0 ]; then
  # Parameter Storeから値を確認
  STORED_VALUE_2=$(aws ssm get-parameter --name "$TEST_PARAM_NAME" --query "Parameter.Value" --output text 2>/dev/null)
  if [ -n "$STORED_VALUE_2" ]; then
    STORED_TIME_2=$(echo "$STORED_VALUE_2" | jq -r '.deploy_baseline_time' 2>/dev/null)
    if [ "$STORED_TIME_2" = "$DEADLINE_TIME_2" ]; then
      echo "✅ Test 4 passed: Parameter Store updated with deadline time"
      echo "Expected: $DEADLINE_TIME_2"
      echo "Actual: $STORED_TIME_2"
    else
      echo "❌ Test 4 failed: Stored time does not match deadline time"
      echo "Expected: $DEADLINE_TIME_2"
      echo "Actual: $STORED_TIME_2"
    fi
  else
    echo "❌ Test 4 failed: Parameter Store value not found"
  fi
else
  echo "❌ Test 4 failed: Parameter Store update with deadline time failed"
fi

# テストケース5: 不正な引数（エラーを期待）
echo ""
echo "=== Test Case 5: 不正な引数（エラーを期待） ==="
echo "Command: ./cicd/scripts/deploy/update-parameter-store.sh (no arguments)"

RESULT5=$(./cicd/scripts/deploy/update-parameter-store.sh 2>&1)
EXIT_CODE5=$?

echo "Exit code: $EXIT_CODE5"
if [ $EXIT_CODE5 -ne 0 ]; then
  echo "✅ Test 5 passed: Invalid arguments correctly rejected"
else
  echo "❌ Test 5 failed: Invalid arguments were accepted"
fi

# テストケース6: 過多な引数（エラーを期待）
echo ""
echo "=== Test Case 6: 過多な引数（エラーを期待） ==="
echo "Command: ./cicd/scripts/deploy/update-parameter-store.sh \"test\" \"false\" \"time1\" \"time2\""

RESULT6=$(./cicd/scripts/deploy/update-parameter-store.sh "test" "false" "time1" "time2" 2>&1)
EXIT_CODE6=$?

echo "Exit code: $EXIT_CODE6"
if [ $EXIT_CODE6 -ne 0 ]; then
  echo "✅ Test 6 passed: Too many arguments correctly rejected"
else
  echo "❌ Test 6 failed: Too many arguments were accepted"
fi

# 結果サマリー
echo ""
echo "=== テスト結果サマリー ==="
echo "Test 1 (DRY-RUN without deadline): $([ $EXIT_CODE1 -eq 0 ] && echo 'PASS' || echo 'FAIL')"
echo "Test 2 (DRY-RUN with deadline): $([ $EXIT_CODE2 -eq 0 ] && echo 'PASS' || echo 'FAIL')"
echo "Test 3 (Update without deadline): $([ $EXIT_CODE3 -eq 0 ] && echo 'PASS' || echo 'FAIL')"
echo "Test 4 (Update with deadline): $([ $EXIT_CODE4 -eq 0 ] && echo 'PASS' || echo 'FAIL')"
echo "Test 5 (Invalid args): $([ $EXIT_CODE5 -ne 0 ] && echo 'PASS' || echo 'FAIL')"
echo "Test 6 (Too many args): $([ $EXIT_CODE6 -ne 0 ] && echo 'PASS' || echo 'FAIL')"

# テスト後のクリーンアップ
echo ""
echo "Cleaning up test parameter..."
aws ssm delete-parameter --name "$TEST_PARAM_NAME" 2>/dev/null || true

echo ""
echo "=== 締切時刻仕組みParameter Store更新テスト完了 ==="
echo "Tests completed!"
