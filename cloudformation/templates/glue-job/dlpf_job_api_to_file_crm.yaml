AWSTemplateFormatVersion: "2010-09-09"
Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  AccountId:
    Type: String
    Description: AWS Account ID

Resources:
  ApiToFileJob:
    Type: AWS::Glue::Job
    Properties:
      Name: job_api_to_file_crm
      Description: DLPF Glue job for API to file conversion
      Role: !Sub "arn:aws:iam::${AccountId}:role/AWSGlueServiceRole-${Environment}-dlpf-glue-job"
      ExecutionProperty:
        MaxConcurrentRuns: 100
      Command:
        Name: pythonshell
        ScriptLocation: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/source/glue_job_api_to_file_crm.py"
        PythonVersion: 3.9
      DefaultArguments:
        --enable-metrics: true
        --extra-py-files: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/deps.zip"
        file_setting:
          {
            "sep": ",",
            "quoting": "csv.QUOTE_ALL",
            "quotechar": '"',
            "lineterminator": "\n",
            "header": true,
          }
        --enable-job-insights: false
        --enable-observability-metrics: false
        --enable-continuous-cloudwatch-log: true
        --job-language: python
        diff_flag: False
      Connections:
        Connections:
          - !Sub "conn-${Environment}-dlpf-private-01"
          - !Sub "conn-${Environment}-dlpf-private-02"
      MaxRetries: 0
      AllocatedCapacity: 0
      Timeout: 2880
      MaxCapacity: 0.0625
      GlueVersion: 3.0
      ExecutionClass: STANDARD
# Parameters:
#   SecretName:
#     Type: String
#     Description: DB Secrets Manager secret name

#   ApiSecretName:
#     Type: String
#     Description: API Secrets Manager secret name

#   DiffFlag:
#     Type: String
#     Description: Differential (True) or Full (False) data retrieval
#     Default: 'False'

#   FileName:
#     Type: String
#     Description: Target file name

#   OutputFileDir:
#     Type: String
#     Description: Output file directory path

#   FileSetting:
#     Type: String
#     Description: JSON format file settings
#     Default: '{"sep":",","quoting":"csv.QUOTE_ALL","quotechar":"\"","lineterminator":"¥n","header":true}'

#   ApiRequestParam:
#     Type: String
#     Description: API request parameters
#   ApiRequestMethod:
#     Type: String
#     Description: HTTP method for API request

#   RecordsKey:
#     Type: String
#     Description: Key to specify records in API response

#   OutputKeys:
#     Type: String
#     Description: Keys for output target fields (comma-separated)

#   ObjectKey:
#     Type: String
#     Description: Key for nested object in API response

#   S3BucketName:
#     Type: String
#     Description: S3 bucket name for Glue job scripts and data
#     Default: aws-glue-assets-886436956581-ap-northeast-1

#   GlueScriptPath:
#     Type: String
#     Description: S3 path to the Glue job script
#     Default: scripts/source/glue_job_api_to_file_crm.py

#   GlueRole:
#     Type: String
#     Description: IAM role ARN for Glue job execution
#     Default: arn:aws:iam::886436956581:role/AWSGlueServiceRole-dev-dlpf-glue-job

#   JobnetId:
#     Type: String
#     Description: Jobnet ID
