AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Character Encoding Conversion Glue Job

Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  AccountId:
    Type: String
    Description: AWS Account ID

Resources:
  ConvertCharacterEncodingJob:
    Type: AWS::Glue::Job
    Properties:
      Name: job_convert_character_encoding
      Description: DLPF Glue job for converting character encoding of files in S3
      Role: !Sub "arn:aws:iam::${AccountId}:role/AWSGlueServiceRole-${Environment}-dlpf-glue-job"
      ExecutionProperty:
        MaxConcurrentRuns: 100
      Command:
        Name: pythonshell
        ScriptLocation: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/source/glue_job_convert_character_encoding.py"
        PythonVersion: 3.9
      DefaultArguments:
        --enable-metrics: true
        --extra-py-files: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/deps.zip"
        --enable-continuous-cloudwatch-log: true
        --job-language: python
        backup_flag: False
      Connections:
        Connections:
          - !Sub "conn-${Environment}-dlpf-private-01"
          - !Sub "conn-${Environment}-dlpf-private-02"
      MaxRetries: 0
      AllocatedCapacity: 0
      Timeout: 2880
      MaxCapacity: 0.0625
      GlueVersion: 3.0
