AWSTemplateFormatVersion: "2010-09-09"
Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  AccountId:
    Type: String
    Description: AWS Account ID

Resources:
  InternalDbClearJob:
    Type: AWS::Glue::Job
    Properties:
      Name: job_internal_db_clear
      Description: DLPF Glue job for internal DB clearing
      Role: !Sub "arn:aws:iam::${AccountId}:role/AWSGlueServiceRole-${Environment}-dlpf-glue-job"
      ExecutionProperty:
        MaxConcurrentRuns: 100
      Command:
        Name: pythonshell
        ScriptLocation: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/source/glue_job_internal_db_clear.py"
        PythonVersion: 3.9
      DefaultArguments:
        --enable-metrics: true
        --extra-py-files: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/deps.zip"
        --enable-job-insights: false
        --job-language: python
        --enable-observability-metrics: false
        --enable-continuous-cloudwatch-log: true
      Connections:
        Connections:
          - !Sub "conn-${Environment}-dlpf-private-01"
          - !Sub "conn-${Environment}-dlpf-private-02"
      MaxRetries: 0
      AllocatedCapacity: 0
      Timeout: 2880
      MaxCapacity: 0.0625
      GlueVersion: 3.0
      ExecutionClass: STANDARD
# Parameters:
#   SecretName:
#     Type: String
#     Description: DB Secrets Manager secret name for DB connection

#   QueryDelete:
#     Type: String
#     Description: SQL query ID for DELETE operation
#     Default: ''

#   TruncateTable:
#     Type: String
#     Description: Table name for TRUNCATE operation
#     Default: ''

#   DeleteStart:
#     Type: String
#     Description: Start condition for deletion (if empty with end condition, delete all)
#     Default: ''

#   DeleteEnd:
#     Type: String
#     Description: End condition for deletion (if empty with start condition, delete all)
#     Default: ''

#   S3BucketName:
#     Type: String
#     Description: S3 bucket name for Glue job scripts and data
#     Default: aws-glue-assets-{AccountId}-ap-northeast-1

#   GlueScriptPath:
#     Type: String
#     Description: S3 path to the Glue job script
#     Default: scripts/source/glue_job_internal_db_clear.py

#   GlueRole:
#     Type: String
#     Description: IAM role ARN for Glue job execution
#     Default: arn:aws:iam::{AccountId}:role/AWSGlueServiceRole-{Environment}-dlpf-glue-job

#   JobnetId:
#     Type: String
#     Description: Jobnet ID
