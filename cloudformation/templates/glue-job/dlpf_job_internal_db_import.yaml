AWSTemplateFormatVersion: "2010-09-09"
Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  AccountId:
    Type: String
    Description: AWS Account ID

Resources:
  InternalDbImportJob:
    Type: AWS::Glue::Job
    Properties:
      Name: job_internal_db_import
      Description: DLPF Glue job for internal DB data import
      Role: !Sub "arn:aws:iam::${AccountId}:role/AWSGlueServiceRole-${Environment}-dlpf-glue-job"
      ExecutionProperty:
        MaxConcurrentRuns: 100
      Command:
        Name: pythonshell
        ScriptLocation: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/source/glue_job_internal_db_import.py"
        PythonVersion: 3.9
      DefaultArguments:
        --enable-metrics: true
        --extra-py-files: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/deps.zip"
        --enable-job-insights: false
        --enable-observability-metrics: false
        --enable-continuous-cloudwatch-log: true
        library-set: analytics
        --job-language: python
        backup_flag: False
      Connections:
        Connections:
          - !Sub "conn-${Environment}-dlpf-private-01"
          - !Sub "conn-${Environment}-dlpf-private-02"
      MaxRetries: 0
      AllocatedCapacity: 0
      Timeout: 2880
      MaxCapacity: 0.0625
      GlueVersion: 3.0
      ExecutionClass: STANDARD
# Parameters:
#   InputFormatOptions:
#     Type: String
#     Description: Options for aws_s3.table_import_from_s3 command (e.g., CSV HEADER QUOTE '"')

#   SecretName:
#     Type: String
#     Description: DB Secrets Manager secret name for DB connection

#   InputFileDir:
#     Type: String
#     Description: Input file directory path (e.g., 00_入力外部インタフェース入出庫情報)

#   InputFileName:
#     Type: String
#     Description: Input file name (e.g., IF-ST003-DF01.csv)

#   ImportTable:
#     Type: String
#     Description: Target table name for import (e.g., product_mst_work)

#   BackupFlag:
#     Type: String
#     Description: Backup flag (True = backup, False = no backup)
#     Default: 'False'

#   BackupFileDir:
#     Type: String
#     Description: Backup file directory path

#   QueryUpsert:
#     Type: String
#     Description: SQL query ID for UPSERT operation
#     Default: ''

#   S3BucketName:
#     Type: String
#     Description: S3 bucket name for Glue job scripts and data
#     Default: aws-glue-assets-{AccountId}-ap-northeast-1

#   GlueScriptPath:
#     Type: String
#     Description: S3 path to the Glue job script
#     Default: scripts/source/glue_job_internal_db_import.py

#   GlueRole:
#     Type: String
#     Description: IAM role ARN for Glue job execution
#     Default: arn:aws:iam::{AccountId}:role/AWSGlueServiceRole-{Environment}-dlpf-glue-job

#   JobnetId:
#     Type: String
#     Description: Jobnet ID
