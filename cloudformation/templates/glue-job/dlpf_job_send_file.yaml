AWSTemplateFormatVersion: "2010-09-09"
Description: "CloudFormation template for DLPF Glue Job - Send File"

Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  AccountId:
    Type: String
    Description: AWS Account ID

Resources:
  SendFileJob:
    Type: AWS::Glue::Job
    Properties:
      Name: job_send_file
      Description: DLPF Glue job for sending files to external system
      Role: !Sub "arn:aws:iam::${AccountId}:role/AWSGlueServiceRole-${Environment}-dlpf-glue-job"
      ExecutionProperty:
        MaxConcurrentRuns: 100
      Command:
        Name: pythonshell
        ScriptLocation: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/source/glue_job_send_file.py"
        PythonVersion: 3.9
      DefaultArguments:
        --enable-metrics: true
        --extra-py-files: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/deps.zip"
        --enable-job-insights: false
        --enable-observability-metrics: false
        --enable-continuous-cloudwatch-log: true
        --job-language: python
        backup_flag: False
      Connections:
        Connections:
          - !Sub "conn-${Environment}-dlpf-private-01"
          - !Sub "conn-${Environment}-dlpf-private-02"
      MaxRetries: 0
      AllocatedCapacity: 0
      Timeout: 2880
      MaxCapacity: 0.0625
      GlueVersion: 3.0
      ExecutionClass: STANDARD
# Parameters:
#   S3FullPath:
#     Type: String
#     Description: Full S3 path of file to send (e.g., /folder/file.txt)

#   SecretName:
#     Type: String
#     Description: Secrets Manager secret name for external system SFTP/FTP connection

#   ExternalSystemDestinationDirectory:
#     Type: String
#     Description: Destination directory path on external system (e.g., /remote/path/to/directory/)

#   ExternalSystemTransmissionFileName:
#     Type: String
#     Description: Target file name for transmission

#   BackupFlag:
#     Type: String
#     Description: Backup flag (True = backup, False = no backup)
#     Default: 'False'

#   BackupS3Directory:
#     Type: String
#     Description: S3 directory path for backup files

#   S3BucketName:
#     Type: String
#     Description: S3 bucket name for Glue job scripts and data
#     Default: aws-glue-assets-{AccountId}-ap-northeast-1

#   GlueScriptPath:
#     Type: String
#     Description: S3 path to the Glue job script
#     Default: scripts/source/glue_job_send_file.py

#   GlueRole:
#     Type: String
#     Description: IAM role ARN for Glue job execution
#     Default: arn:aws:iam::{AccountId}:role/AWSGlueServiceRole-{Environment}-dlpf-glue-job

#   JobnetId:
#     Type: String
#     Description: Jobnet ID
