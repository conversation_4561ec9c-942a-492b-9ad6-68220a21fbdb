AWSTemplateFormatVersion: "2010-09-09"
Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)

Resources:
    LambdaFunction:
        Type: "AWS::Lambda::Function"
        DeletionPolicy: Delete
        Properties:
            Description: ""
            FunctionName: "DLPF_CHECK_FILE_EXIST"
            Handler: "lambda_function.lambda_handler"
            Architectures: 
              - "x86_64"
            Code: 
                ZipFile: "def handler(event, context): return 'placeholder'"
            MemorySize: 128
            Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/role-${Environment}-dlpf-lambda-invokesfn"
            Runtime: "python3.13"
            Timeout: 15
            TracingConfig: 
                Mode: "PassThrough"
            EphemeralStorage: 
                Size: 512
            Environment:
                Variables:
                    retry_limit: '3'
            Layers:
                - >-
                    arn:aws:lambda:ap-northeast-1:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension:17