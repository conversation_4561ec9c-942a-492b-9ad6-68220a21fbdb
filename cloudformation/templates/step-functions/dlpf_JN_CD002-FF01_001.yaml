AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_CD002-FF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF-CD002-FF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}",
                "short_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01]') %}"
              },
              "Next": "1_job_get_file"
            },
            "1_job_get_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_get_file",
                "Arguments": {
                  "source_secret_name": "SMBC-GMO_SFTP_INFO",
                  "source_remote_file_full_path": "SMCC_1_*.txt",
                  "s3_storage_file_full_path": "{% 'input-output/SMBC-GMO_IN/JN_CD002-FF01_001_' & $full_date & '/SMCC_1_' & $short_date & '.txt' %}",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/SMBC-GMO_IN/JN_CD002-FF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_CD002-FF01_001",
                  "--TaskToken": "{% $states.context.Task.Token %}"
                }
              },
              "Catch": [
                {
                  "ErrorEquals": [
                    "FileNotFound"
                  ],
                  "Next": "file_not_found"
                }
              ],
              "Next": "2_job_convert_format"
            },
            "file_not_found": {
              "Type": "Pass",
              "End": true
            },
            "2_job_convert_format": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_convert_format",
                "Arguments": {
                  "etl_yaml_file_id": "fixed2tsv/etl_fixed2tsv_CD002-FF01_001",
                  "input_file_dir": "{% 'input-output/SMBC-GMO_IN/JN_CD002-FF01_001_' & $full_date & '/' %}",
                  "input_file_name": "{% 'SMCC_1_' & $short_date & '.txt' %}",
                  "output_file_dir": "{% 'tmp/job_convert_format/JN_CD002-FF01_001_' & $full_date & '/' %}",
                  "output_file_name": "EIOR0080.tsv",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/SMBC-GMO_IN/JN_CD002-FF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_CD002-FF01_001"
                }
              },
              "Next": "3_job_internal_db_import"
            },
            "3_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT 'csv', DELIMITER E'\t', ENCODING SJIS, HEADER FALSE)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "{% 'tmp/job_convert_format/JN_CD002-FF01_001_' & $full_date & '/' %}",
                  "input_file_name": "EIOR0080.tsv",
                  "import_table": "dhc_card_mst_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/job_convert_format/JN_CD002-FF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_CD002-FF01_001",
                  "query_upsert": "sql_CD002-FF01_upsert_001"
                }
              },
              "Next": "4_job_db_to_file"
            },
            "4_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "sql_CD002-FF01_select_001",
                  "batch_size": "1000",
                  "output_file_dir": "{% 'input-output/DLPF_OMS/JN_CD002-FF01_001_' & $full_date & '/' %}",
                  "file_setting": "{ \"delimiter\" : \"\\t\", \"line_ending\": \"\\r\\n\", \"header\": false}",
                  "file_name": "EIOR0080.tsv",
                  "file_type": "tsv",
                  "jobnet_id": "JN_CD002-FF01_001",
                  "diff_base_timestamp_query": "sql_CD002-FF01_timestamp_001",
                  "file_id": "EIOR0080",
                  "sync_update_later_flg": "1"
                }
              },
              "Next": "5_job_internal_db_clear"
            },
            "5_job_internal_db_clear": {
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Parameters": {
                "JobName": "job_internal_db_clear",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "truncate_table": "dhc_card_mst",
                  "jobnet_id": "JN_CD002-FF01_001"
                }
              },
              "Next": "6_sql_execute"
            },
            "6_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_CD002-FF01_001",
                  "sql_info": "{\"sql_info\": [{\"query_id\": \"sql_common_update_003\"}]}"
                }
              },
              "End": true
            }
          }
        }
