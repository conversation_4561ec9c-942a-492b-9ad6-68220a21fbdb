AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_CP002-DF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF-CP002-DF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}",
                "short_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01]') %}"
              },
              "Next": "1_job_db_to_file"
            },
            "1_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "OMS_DB_INFO",
                  "execute_query": "sql_CP002-DF01_select_001",
                  "batch_size": "1000",
                  "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP002-DF01_001_' & $full_date & '/' %}",
                  "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                  "file_name": "{% 'coupon_' & $full_date & '.csv' %}",
                  "file_type": "csv",
                  "jobnet_id": "JN_CP002-DF01_001",
                  "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_001",
                  "file_id": "coupon_oms",
                  "sync_update_later_flg": "1"
                }
              },
              "Next": "2_job_internal_db_import"
            },
            "2_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT CSV, HEADER FALSE)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "{% 'tmp/job_db_to_file/JN_CP002-DF01_001_' & $full_date & '/' %}",
                  "input_file_name": "{% 'coupon_' & $full_date & '.csv' %}",
                  "import_table": "coupon_view_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/job_db_to_file/JN_CP002-DF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_CP002-DF01_001",
                  "query_upsert": "sql_CP002-DF01_upsert_001"
                }
              },
              "Next": "3_job_db_to_file"
            },
            "3_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "OMS_DB_INFO",
                  "execute_query": "sql_CP002-DF01_select_002",
                  "batch_size": "1000",
                  "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP002-DF01_001_' & $full_date & '/' %}",
                  "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                  "file_name": "{% 'coupon_commodity_' & $full_date & '.csv' %}",
                  "file_type": "csv",
                  "jobnet_id": "JN_CP002-DF01_001",
                  "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_002",
                  "file_id": "coupon_commodity_oms",
                  "sync_update_later_flg": "1"
                }
              },
              "Next": "4_job_internal_db_import"
            },
            "4_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT CSV, HEADER FALSE)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "{% 'tmp/job_db_to_file/JN_CP002-DF01_001_' & $full_date & '/' %}",
                  "input_file_name": "{% 'coupon_commodity_' & $full_date & '.csv' %}",
                  "import_table": "coupon_commodity_view_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/job_db_to_file/JN_CP002-DF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_CP002-DF01_001",
                  "query_upsert": "sql_CP002-DF01_upsert_002"
                }
              },
              "Next": "parallel_5_to_13"
            },
            "parallel_5_to_13": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "5_job_db_to_file",
                  "States": {
                    "5_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_003",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'campaign_instructions_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_003",
                          "file_id": "campaign_instructions",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "6_job_db_to_file",
                  "States": {
                    "6_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_004",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'coupon_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_004",
                          "file_id": "coupon",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "7_job_db_to_file",
                  "States": {
                    "7_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_005",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'campaign_order_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_005",
                          "file_id": "campaign_order",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "8_job_db_to_file",
                  "States": {
                    "8_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_006",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'campaign_order_group_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_006",
                          "file_id": "campaign_order_group",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "9_job_db_to_file",
                  "States": {
                    "9_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_007",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'campaign_promotion_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_007",
                          "file_id": "campaign_promotion",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "10_job_db_to_file",
                  "States": {
                    "10_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_008",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'campaign_instructions_commodity_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_008",
                          "file_id": "campaign_instructions_commodity",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "11_job_db_to_file",
                  "States": {
                    "11_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_009",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'campaign_combi_limit_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_009",
                          "file_id": "campaign_combi_limit",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "12_job_db_to_file",
                  "States": {
                    "12_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_010",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'coupon_commodity_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_010",
                          "file_id": "coupon_commodity",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "13_job_db_to_file",
                  "States": {
                    "13_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_CP002-DF01_select_011",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/SQG_OUT/JN_CP002-DF01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\"}",
                          "file_name": "{% 'set_commodity_composition_' & $short_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP002-DF01_001",
                          "diff_base_timestamp_query": "sql_CP002-DF01_timestamp_011",
                          "file_id": "set_commodity_composition",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "InputPath": "$",
              "ResultPath": "$.parallel_14_to_22",
              "Next": "14_sql_execute"
            },
            "14_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_CP002-DF01_001",
                  "sql_info": "{\"sql_info\": [{\"query_id\": \"sql_common_update_003\"}]}"
                }
              },
              "End": true
            }
          }
        }
