AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prod)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_DW111-AF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  # 会員_001
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for JN_DW111-AF01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                  "now_timestamp": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}",
                  "extract_from": "{% $fromMillis($toMillis($fromMillis($toMillis($now()), '[Y0001]-[M01]-[D01]', '+0900')) - 57*60*60*1000) %}"
              },
              "Next": "1_job_api_to_file_crm"
            },
            "1_job_api_to_file_crm": {
              "Type": "Task",
              "QueryLanguage": "JSONata",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_api_to_file_crm",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "api_secret_name": "CRM-CS_API_INFO",
                  "diff_flag": "False",
                  "file_name": "IF-CRM-ME-001.csv",
                  "output_file_dir": "tmp/job_api_to_file_crm/JN_DW111-AF01_001/",
                  "file_setting": "{ \"sep\": \",\", \"quoting\": \"csv.QUOTE_MINIMAL\", \"lineterminator\": \"\\n\", \"header\": \"True\"  }",
                  "jobnet_id": "JN_DW111-AF01_001",
                  "api_request_param": "{% 'q=SELECT%20Id%2CIsDeleted%2CMasterRecordId%2CName%2CLastName%2CFirstName%2CSalutation%2CType%2CRecordTypeId%2CParentId%2CPersonMailingStreet%2CPersonMailingCity%2CPersonMailingState%2CPersonMailingStateCode%2CPersonMailingPostalCode%2CPersonMailingCountry%2CPersonMailingLatitude%2CPersonMailingLongitude%2CPersonMailingGeocodeAccuracy%2CShippingStreet%2CShippingCity%2CShippingState%2CShippingPostalCode%2CShippingCountry%2CShippingLatitude%2CShippingLongitude%2CShippingGeocodeAccuracy%2CPhone%2CFax%2CAccountNumber%2CWebsite%2CPhotoUrl%2CSic%2CIndustry%2CAnnualRevenue%2CNumberOfEmployees%2COwnership%2CTickerSymbol%2CDescription%2CRating%2CSite%2COwnerId%2CCreatedDate%2CCreatedById%2CLastModifiedDate%2CLastModifiedById%2CSystemModstamp%2CLastActivityDate%2CLastViewedDate%2CLastReferencedDate%2CIsPersonAccount%2CBillingStreet%2CBillingCity%2CBillingState%2CBillingPostalCode%2CBillingCountry%2CBillingLatitude%2CBillingLongitude%2CBillingGeocodeAccuracy%2CPersonOtherStreet%2CPersonOtherCity%2CPersonOtherState%2CPersonOtherPostalCode%2CPersonOtherCountry%2CPersonOtherLatitude%2CPersonOtherLongitude%2CPersonOtherGeocodeAccuracy%2CPersonMobilePhone%2CPersonOtherPhone%2CPersonAssistantPhone%2CPersonEmail%2CPersonTitle%2CPersonDepartment%2CPersonAssistantName%2CPersonLeadSource%2CPersonBirthdate%2CPersonHasOptedOutOfEmail%2CPersonHasOptedOutOfFax%2CPersonDoNotCall%2CPersonLastCURequestDate%2CPersonLastCUUpdateDate%2CPersonEmailBouncedReason%2CPersonEmailBouncedDate%2CPersonIndividualId%2CPersonPronouns%2CPersonGenderIdentity%2CJigsaw%2CJigsawCompanyId%2CAccountSource%2CSicDesc%2CGender__c%2CNumber__c%2CPreferredShipmentService__c%2CAccountClosedDate__c%2CAccountClosedReason__c%2CShopCardBarcode__c%2CPersonMailingAddress__c%2CIsEmployee__c%2CIsOptedInEmalMagazine__c%2CEmailMagazineUnsubscribedDate__c%2CEmailMagazineSubscribedDate__c%2CBeautyCatalogSendType__c%2CHealthCatalogSendType__c%2CApparelCatalogSendType__c%2CMedicineCatalogSendType__c%2CPetCatalogSendType__c%2CFaxPurchaseOrderSendType__c%2CLastNameKana__c%2CFirstNameKana__c%2CRank__c%2CSource__c%2CStatus__c%2CMemo__c%2CMemoForStore__c%2CIsDHCCreditCardOwner__c%2CAge__c%2CIsOptedInDM__c%2CIsOptedInCatalog__c%2CIsUnmailablePostway__c%2CIsUnmailablePost__c%2CIsStopOrder__c%2CIsOrderMonitoring__c%2CIsRequiredCaution__c%2CGuestOrderOnly__c%2CCustomerNumber__c%2CIsOptedInSurvey__c%2CMargedAccountId__c%2CRankExpiryDate__c%2CPreferredContactWay__c%2CLineMiniAppUserId__c%2CNameKana__c%2CBirthdate__c%2CEmailMagazineOptedOutUrl__c%2CPersonEmail__c%2COptInStoreEmailMagazineStatus__c%2CStoreEmailMagazineEmail__c%2CStoreEmailMagazineOptedOutKey__c%2CStoreEmailMagazineOptedOutUrl__c%2CTonariwaId__c%2CStoreEmailMagazineStoreCode__c%2CUnmailableReason__c%20FROM%20Account%20WHERE%20LastModifiedDate%3E%3D' & $extract_from  %}",
                  "api_request_method": "GET",
                  "records_key": "records",
                  "output_keys": "Id,IsDeleted,MasterRecordId,Name,LastName,FirstName,Salutation,Type,RecordTypeId,ParentId,PersonMailingStreet,PersonMailingCity,PersonMailingState,PersonMailingStateCode,PersonMailingPostalCode,PersonMailingCountry,PersonMailingLatitude,PersonMailingLongitude,PersonMailingGeocodeAccuracy,ShippingStreet,ShippingCity,ShippingState,ShippingPostalCode,ShippingCountry,ShippingLatitude,ShippingLongitude,ShippingGeocodeAccuracy,Phone,Fax,AccountNumber,Website,PhotoUrl,Sic,Industry,AnnualRevenue,NumberOfEmployees,Ownership,TickerSymbol,Description,Rating,Site,OwnerId,CreatedDate,CreatedById,LastModifiedDate,LastModifiedById,SystemModstamp,LastActivityDate,LastViewedDate,LastReferencedDate,IsPersonAccount,BillingStreet,BillingCity,BillingState,BillingPostalCode,BillingCountry,BillingLatitude,BillingLongitude,BillingGeocodeAccuracy,PersonOtherStreet,PersonOtherCity,PersonOtherState,PersonOtherPostalCode,PersonOtherCountry,PersonOtherLatitude,PersonOtherLongitude,PersonOtherGeocodeAccuracy,PersonMobilePhone,PersonOtherPhone,PersonAssistantPhone,PersonEmail,PersonTitle,PersonDepartment,PersonAssistantName,PersonLeadSource,PersonBirthdate,PersonHasOptedOutOfEmail,PersonHasOptedOutOfFax,PersonDoNotCall,PersonLastCURequestDate,PersonLastCUUpdateDate,PersonEmailBouncedReason,PersonEmailBouncedDate,PersonIndividualId,PersonPronouns,PersonGenderIdentity,Jigsaw,JigsawCompanyId,AccountSource,SicDesc,Gender__c,Number__c,PreferredShipmentService__c,AccountClosedDate__c,AccountClosedReason__c,ShopCardBarcode__c,PersonMailingAddress__c,IsEmployee__c,IsOptedInEmalMagazine__c,EmailMagazineUnsubscribedDate__c,EmailMagazineSubscribedDate__c,BeautyCatalogSendType__c,HealthCatalogSendType__c,ApparelCatalogSendType__c,MedicineCatalogSendType__c,PetCatalogSendType__c,FaxPurchaseOrderSendType__c,LastNameKana__c,FirstNameKana__c,Rank__c,Source__c,Status__c,Memo__c,MemoForStore__c,IsDHCCreditCardOwner__c,Age__c,IsOptedInDM__c,IsOptedInCatalog__c,IsUnmailablePostway__c,IsUnmailablePost__c,IsStopOrder__c,IsOrderMonitoring__c,IsRequiredCaution__c,GuestOrderOnly__c,CustomerNumber__c,IsOptedInSurvey__c,MargedAccountId__c,RankExpiryDate__c,PreferredContactWay__c,LineMiniAppUserId__c,NameKana__c,Birthdate__c,EmailMagazineOptedOutUrl__c,PersonEmail__c,OptInStoreEmailMagazineStatus__c,StoreEmailMagazineEmail__c,StoreEmailMagazineOptedOutKey__c,StoreEmailMagazineOptedOutUrl__c,TonariwaId__c,StoreEmailMagazineStoreCode__c,UnmailableReason__c"
                }
              },
              "Next": "2_job_internal_db_import"
            },
            "2_job_internal_db_import": {
              "Type": "Task",
              "QueryLanguage": "JSONata",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT csv, HEADER true)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "tmp/job_api_to_file_crm/JN_DW111-AF01_001/",
                  "input_file_name": "IF-CRM-ME-001.csv",
                  "import_table": "account_dwh_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/CRM-CS_IN/IF-DW111-AF01_' & $now_timestamp & '/' %}",
                  "jobnet_id": "JN_DW111-AF01_001",
                  "query_upsert": "sql_DW111-AF01_upsert_001"
                }
              },
              "Next": "3_job_db_to_file"
            },
            "3_job_db_to_file": {
              "Type": "Task",
              "QueryLanguage": "JSONata",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "sql_DW111-AF01_select_001",
                  "output_file_dir": "{% 'input-output/DLPF_DWH/IF-CRM-ME-001_' & $now_timestamp & '/' %}",
                  "file_setting": "{\"delimiter\" : \",\",\"enclosed text\" : \"\\\"\",\"new line code\" : \"LF\",\"header\" :true} ",
                  "file_name": "IF-CRM-ME-001.csv",
                  "file_type": "csv",
                  "jobnet_id": "JN_DW111-AF01_001",
                  "diff_base_timestamp_query": "sql_DWcommon_select_001",
                  "file_id": "IF-CRM-ME-001"
                }
              },
              "Next": "4_job_internal_db_clear"
            },
            "4_job_internal_db_clear": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_clear",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "query_delete": "sql_DW111-AF01_delete_001",
                  "truncate_table": "account",
                  "delete_start": "{% '' & $now_timestamp & '' %}",
                  "delete_end": "{% '' & $now_timestamp & '' %}",
                  "jobnet_id": "JN_DW111-AF01_001"
                }
              },
              "End": true
            }
          }
        }
  EventRule:
    Type: "AWS::Events::Rule"
    Properties:
      Name: "EB_DW111-FF01_001"
      ScheduleExpression: "cron(30 15 * * ? *)"
      State: "DISABLED"
      Targets:
        - Arn: !GetAtt GlueOrchestrationStateMachine.Arn
          Id: "StepFunctionsTarget"
          RoleArn: !Sub "arn:aws:iam::${AccountId}:role/MyEventBridgeRole"
