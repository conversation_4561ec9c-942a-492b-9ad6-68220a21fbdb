AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_IS003-DF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF-ST001-FF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_convert_format"
            },
            "1_job_convert_format": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_convert_format",
                "Arguments": {
                  "etl_yaml_file_id": "csv2tsv/etl_csv2tsv_IS003-DF01_001",
                  "input_file_dir": "ext-input-output/DLPF_WMS/JN_IS003-DF01_001/",
                  "input_file_name": "TINSTOCK_*.CSV",
                  "output_file_dir": "{% 'tmp/job_convert_format/JN_IS003-DF01_001_' & $full_date & '/' %}",
                  "output_file_name": "{% '0069_Arrival_' & $full_date & '.tsv' %}",
                  "backup_flag": "True",
                  "backup_file_dir": "ext-back-up/DLPF_WMS/JN_IS003-DF01_001/",
                  "jobnet_id": "JN_IS003-DF01_001",
                  "--TaskToken": "{% $states.context.Task.Token %}"
                }
              },
              "Catch": [
                {
                  "ErrorEquals": [
                    "FileNotFound"
                  ],
                  "Next": "file_not_found"
                }
              ],
              "Next": "2_job_convert_character_encoding"
            },
            "file_not_found": {
              "Type": "Pass",
              "End": true
            },
            "2_job_convert_character_encoding": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_convert_character_encoding",
                "Arguments": {
                  "input_character_encoding": "cp932",
                  "output_character_encoding": "UTF-8",
                  "input_file_dir": "{% 'tmp/job_convert_format/JN_IS003-DF01_001_' & $full_date & '/' %}",
                  "file_name": "{% '0069_Arrival_' & $full_date & '.tsv' %}",
                  "output_file_dir": "{% 'input-output/COMMONS_OUT/JN_IS003-DF01_001_' & $full_date & '/' %}",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/job_convert_format/JN_IS003-DF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_IS003-DF01_001"
                }
              },
              "Next": "3_job_send_file"
            },
            "3_job_send_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_send_file",
                "Arguments": {
                  "s3_full_path": "{% 'input-output/COMMONS_OUT/JN_IS003-DF01_001_' & $full_date & '/0069_Arrival_' & $full_date & '.tsv' %}",
                  "secret_name": "COMMONS_SFTP_INFO",
                  "external_system_destination_directory": "Interface/ToCommons/",
                  "external_system_transmission_file_name": "{% '0069_Arrival_' & $full_date & '.tsv' %}",
                  "backup_flag": "True",
                  "backup_s3_directory": "{% 'tmp/job_send_file/JN_IS003-DF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_IS003-DF01_001"
                }
              },
              "Next": "4_job_internal_db_import"
            },
            "4_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT CSV, DELIMITER E'\t', HEADER true)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "{% 'tmp/job_send_file/JN_IS003-DF01_001_' & $full_date %}",
                  "input_file_name": "{% '0069_Arrival_' & $full_date & '.tsv' %}",
                  "import_table": "arrival_achievements_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/job_send_file/JN_IS003-DF01_001_' & $full_date %}",
                  "jobnet_id": "JN_IS003-DF01_001",
                  "query_upsert": "sql_IS003-DF01_upsert_001"
                }
              },
              "End": true
            }
          }
        }
