AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_PR004-DD01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF_PR004-DD01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "parallel_1_to_7"
            },
            "parallel_1_to_7": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "1_job_db_to_file",
                  "States": {
                    "1_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_PR004-DD01_select_001",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'commodity_header_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "diff_base_timestamp_query": "sql_PR004-DD01_timestamp_001",
                          "file_id": "commodity_header",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_job_db_to_file",
                  "States": {
                    "2_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_PR004-DD01_select_002",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'commodity_detail_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "diff_base_timestamp_query": "sql_PR004-DD01_timestamp_002",
                          "file_id": "commodity_detail",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "3_job_db_to_file",
                  "States": {
                    "3_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_PR004-DD01_select_003",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'set_commodity_composition_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "diff_base_timestamp_query": "sql_PR004-DD01_timestamp_003",
                          "file_id": "set_commodity_composition",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "4_job_db_to_file",
                  "States": {
                    "4_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_PR004-DD01_select_004",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'regular_sale_commodity_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "diff_base_timestamp_query": "sql_PR004-DD01_timestamp_004",
                          "file_id": "regular_sale_commodity",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "5_job_db_to_file",
                  "States": {
                    "5_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_PR004-DD01_select_005",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'regular_sale_composition_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "diff_base_timestamp_query": "sql_PR004-DD01_timestamp_005",
                          "file_id": "regular_sale_composition",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "6_job_db_to_file",
                  "States": {
                    "6_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_PR004-DD01_select_006",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'regular_sale_base_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "diff_base_timestamp_query": "sql_PR004-DD01_timestamp_006",
                          "file_id": "regular_sale_base",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "7_job_db_to_file",
                  "States": {
                    "7_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_PR004-DD01_select_007",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'regular_sale_payment_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "diff_base_timestamp_query": "sql_PR004-DD01_timestamp_007",
                          "file_id": "regular_sale_payment",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "parallel_8_to_14"
            },
            "parallel_8_to_14": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "8_job_internal_db_import",
                  "States": {
                    "8_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'commodity_header_' & $full_date & '.csv' %}",
                          "import_table": "commodity_header_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "query_upsert": "sql_PR004-DD01_upsert_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "9_job_internal_db_import",
                  "States": {
                    "9_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'commodity_detail_' & $full_date & '.csv' %}",
                          "import_table": "commodity_detail_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "query_upsert": "sql_PR004-DD01_upsert_002"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "10_job_internal_db_import",
                  "States": {
                    "10_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'set_commodity_composition_' & $full_date & '.csv' %}",
                          "import_table": "set_commodity_composition_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "query_upsert": "sql_PR004-DD01_upsert_003"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "11_job_internal_db_import",
                  "States": {
                    "11_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'regular_sale_commodity_' & $full_date & '.csv' %}",
                          "import_table": "regular_sale_commodity_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "query_upsert": "sql_PR004-DD01_upsert_004"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "12_job_internal_db_import",
                  "States": {
                    "12_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'regular_sale_composition_' & $full_date & '.csv' %}",
                          "import_table": "regular_sale_composition_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "query_upsert": "sql_PR004-DD01_upsert_005"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "13_job_internal_db_import",
                  "States": {
                    "13_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'regular_sale_base_' & $full_date & '.csv' %}",
                          "import_table": "regular_sale_base_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "query_upsert": "sql_PR004-DD01_upsert_006"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "14_job_internal_db_import",
                  "States": {
                    "14_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'regular_sale_payment_' & $full_date & '.csv' %}",
                          "import_table": "regular_sale_payment_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR004-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_PR004-DD01_001",
                          "query_upsert": "sql_PR004-DD01_upsert_007"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "15_sql_execute"
            },
            "15_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_PR004-DD01_001",
                  "sql_info": "{\"sql_info\": [{\"query_id\": \"sql_common_update_003\"}]}"
                }
              },
              "End": true
            }
          }
        }
