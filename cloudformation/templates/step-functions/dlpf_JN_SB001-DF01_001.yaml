AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_SB001-DF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF-SB001-DF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}",
                "short_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01]') %}"
              },
              "Next": "1_job_db_to_file"
            },
            "1_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                    "secret_name": "OMS_DB_INFO",
                    "execute_query": "sql_SB001-DF01_select_001",
                    "output_file_dir": "{% 'tmp/job_db_to_file/JN_SB001-DF01_001_' & $full_date & '/' %}",
                    "file_setting": "{ \"header\":true, \"quote_char\": \"\", \"line_ending\": \"\\r\\n\" }",
                    "file_name": "{% 'teiki_kanyusha_data_' & $short_date & '.csv' %}",
                    "file_type": "csv",
                    "jobnet_id": "JN_SB001-DF01_001",
                    "diff_base_timestamp_query": "sql_SB001-DF01_timestamp_001",
                    "file_id": "teiki_kanyusha_data",
                    "sync_update_later_flg": "1"
                }
              },
              "Next": "2_job_convert_character_encoding"
            },
            "2_job_convert_character_encoding": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_convert_character_encoding",
                "Arguments": {
                    "input_character_encoding": "utf-8",
                    "output_character_encoding": "cp932",
                    "input_file_dir": "{% 'tmp/job_db_to_file/JN_SB001-DF01_001_' & $full_date & '/' %}",
                    "file_name": "{% 'teiki_kanyusha_data_' & $short_date & '.csv' %}",
                    "output_file_dir": "{% 'input-output/SAS_OUT/JN_SB001-DF01_001_' & $full_date & '/' %}",
                    "backup_flag": "True",
                    "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SB001-DF01_001_' & $full_date & '/' %}",
                    "jobnet_id": "JN_SB001-DF01_001"
                }
              },
              "Next": "3_job_send_file"
            },
            "3_job_send_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_send_file",
                "Arguments": {
                    "s3_full_path": "{% 'input-output/SAS_OUT/JN_SB001-DF01_001_' & $full_date & '/teiki_kanyusha_data_' & $short_date & '.csv' %}",
                    "secret_name": "SAS_SFTP_INFO",
                    "external_system_destination_directory": "SAS/teiki_kanyusha_data/",
                    "external_system_transmission_file_name": "{% 'teiki_kanyusha_data_' & $short_date & '.csv' %}",
                    "backup_flag": "True",
                    "backup_s3_directory": "{% 'back-up/SAS_OUT/JN_SB001-DF01_001_' & $full_date & '/' %}",
                    "jobnet_id": "JN_SB001-DF01_001"
                }
              },
              "Next": "4_sql_execute"
            },
            "4_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_SB001-DF01_001",
                  "sql_info": "{\"sql_info\": [{\"query_id\": \"sql_common_update_003\"}]}"
                }
              },
              "End": true
            }
          }
        }
