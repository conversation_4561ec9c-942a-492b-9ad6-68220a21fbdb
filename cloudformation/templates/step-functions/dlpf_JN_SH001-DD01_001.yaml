AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_SH001-DD01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF_SH001-DD01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "parallel_1_1_to_1_10"
            },
            "parallel_1_1_to_1_10": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "1_1_job_db_to_file",
                  "States": {
                    "1_1_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_001",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'order_header_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_001",
                          "file_id": "order_header",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_2_job_db_to_file",
                  "States": {
                    "1_2_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_002",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'order_detail_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_002",
                          "file_id": "order_detail",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_3_job_db_to_file",
                  "States": {
                    "1_3_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_003",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'regular_sale_cont_header_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_003",
                          "file_id": "regular_sale_cont_header",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_4_job_db_to_file",
                  "States": {
                    "1_4_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_004",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'regular_sale_cont_detail_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_004",
                          "file_id": "regular_sale_cont_detail",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_5_job_db_to_file",
                  "States": {
                    "1_5_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_005",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'shipping_header_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_005",
                          "file_id": "shipping_header",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_6_job_db_to_file",
                  "States": {
                    "1_6_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_006",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'user_account_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_006",
                          "file_id": "user_account",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_7_job_db_to_file",
                  "States": {
                    "1_7_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_007",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'order_campaign_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_007",
                          "file_id": "order_campaign",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_8_job_db_to_file",
                  "States": {
                    "1_8_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_008",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'order_campaign_use_amount_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_008",
                          "file_id": "order_campaign_use_amount",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_9_job_db_to_file",
                  "States": {
                    "1_9_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_009",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'order_campaign_history_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_009",
                          "file_id": "order_campaign_history",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "1_10_job_db_to_file",
                  "States": {
                    "1_10_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_SH001-DD01_select_010",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                          "file_name": "{% 'customer_address_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "diff_base_timestamp_query": "sql_SH001-DD01_timestamp_010",
                          "file_id": "customer_address",
                          "sync_update_later_flg": "1"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "parallel_2_1_to_2_10"
            },
            "parallel_2_1_to_2_10": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "2_1_job_internal_db_import",
                  "States": {
                    "2_1_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'order_header_' & $full_date & '.csv' %}",
                          "import_table": "order_header_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_2_job_internal_db_import",
                  "States": {
                    "2_2_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'order_detail_' & $full_date & '.csv' %}",
                          "import_table": "order_detail_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_002"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_3_job_internal_db_import",
                  "States": {
                    "2_3_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'regular_sale_cont_header_' & $full_date & '.csv' %}",
                          "import_table": "regular_sale_cont_header_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_003"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_4_job_internal_db_import",
                  "States": {
                    "2_4_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'regular_sale_cont_detail_' & $full_date & '.csv' %}",
                          "import_table": "regular_sale_cont_detail_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_004"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_5_job_internal_db_import",
                  "States": {
                    "2_5_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'shipping_header_' & $full_date & '.csv' %}",
                          "import_table": "shipping_header_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_005"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_6_job_internal_db_import",
                  "States": {
                    "2_6_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'user_account_' & $full_date & '.csv' %}",
                          "import_table": "user_account_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_006"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_7_job_internal_db_import",
                  "States": {
                    "2_7_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'order_campaign_' & $full_date & '.csv' %}",
                          "import_table": "order_campaign_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_007"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_8_job_internal_db_import",
                  "States": {
                    "2_8_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'order_campaign_use_amount_' & $full_date & '.csv' %}",
                          "import_table": "order_campaign_use_amount_view_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_008"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_9_job_internal_db_import",
                  "States": {
                    "2_9_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'order_campaign_history_' & $full_date & '.csv' %}",
                          "import_table": "order_campaign_history_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_009"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_10_job_internal_db_import",
                  "States": {
                    "2_10_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'customer_address_' & $full_date & '.csv' %}",
                          "import_table": "customer_address_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SH001-DD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH001-DD01_001",
                          "query_upsert": "sql_SH001-DD01_upsert_010"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "3_sql_execute"
            },
            "3_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_SH001-DD01_001",
                  "sql_info": "{\"sql_info\": [{\"query_id\": \"sql_common_update_003\"}]}"
                }
              },
              "End": true
            }
          }
        }
