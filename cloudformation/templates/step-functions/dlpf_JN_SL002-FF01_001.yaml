AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_SL002-FF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF_SL002-FF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "GetParallelNum"
            },
            "GetParallelNum": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "parallel_num": 2
              },
              "Next": "1_job_get_file"
            },
            "1_job_get_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_get_file",
                "Arguments": {
                  "source_secret_name": "COMMONS_SFTP_INFO",
                  "source_remote_file_full_path": "Commons/ToInterface/0069_TrStoreZaikoEc_*.tsv",
                  "s3_storage_file_full_path": "{% 'input-output/COMMONS_IN/JN_SL002-FF01_001_' & $full_date & '/0069_TrStoreZaikoEc_' & $full_date & '.tsv' %}",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/COMMONS_IN/JN_SL002-FF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_SL002-FF01_001",
                  "--TaskToken": "{% $states.context.Task.Token %}"
                }
              },
              "Catch": [
                {
                  "ErrorEquals": [
                    "FileNotFound"
                  ],
                  "Next": "file_not_found"
                }
              ],
              "Next": "2_job_internal_db_import"
            },
            "file_not_found": {
              "Type": "Pass",
              "End": true
            },
            "2_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT csv, DELIMITER E'\\t', HEADER true)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "{% 'input-output/COMMONS_IN/JN_SL002-FF01_001_' & $full_date & '/' %}",
                  "input_file_name": "{% '0069_TrStoreZaikoEc_' & $full_date & '.tsv' %}",
                  "import_table": "store_stock_alignment_diff_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/COMMONS_IN/JN_SL002-FF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_SL002-FF01_001",
                  "query_upsert": "sql_SL002-FF01_upsert_001"
                }
              },
              "Next": "3_sql_execute"
            },
            "3_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_SL002-FF01_001",
                  "file_id": "txStoreInventory",
                  "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_SL002-FF01_before_001\", \"params\": {\"split_num\":\"' & $parallel_num & '\", \"object_path\":\"/tmp/job_db_to_file/JN_SL002-FF01_001/JN_SL002-FF01_001_temp_csv.csv\"}}]}' %}"
                }
              },
              "Next": "4_DLPF_RETURN_PARALLEL_NUM_ARRAY"
            },
            "4_DLPF_RETURN_PARALLEL_NUM_ARRAY": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Next": "XML_Map",
              "QueryLanguage": "JSONata",
              "Arguments": {
                "FunctionName": "arn:aws:lambda:ap-northeast-1:${AccountId}:function:DLPF_RETURN_PARALLEL_NUM_ARRAY:$LATEST",
                "Payload": {
                  "parallel_num": "{% $parallel_num %}"
                }
              },
              "Assign": {
                "parallel_num_array": "{% $states.result.Payload %}"
              }
            },
            "XML_Map": {
              "Type": "Map",
              "ItemProcessor": {
                "ProcessorConfig": {
                  "Mode": "INLINE"
                },
                "StartAt": "5_job_db_to_file",
                "States": {
                  "5_job_db_to_file": {
                    "QueryLanguage": "JSONata",
                    "Type": "Task",
                    "Resource": "arn:aws:states:::glue:startJobRun.sync",
                    "Arguments": {
                      "JobName": "job_db_to_file",
                      "Arguments": {
                        "secret_name": "DLPF_DB_INFO",
                        "execute_query": "etl_SL002-FF01_001",
                        "batch_size": "1000",
                        "output_file_dir": "{% 'tmp/job_db_to_file/JN_SL002-FF01_001_' & $full_date & '/' %}",
                        "file_name": "{% 'txStoreInventory_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                        "file_type": "xml",
                        "jobnet_id": "JN_SL002-FF01_001",
                        "diff_base_timestamp_query": "sql_SL002-FF01_timestamp_001",
                        "file_id": "txStoreInventory",
                        "split_num": "{% $string($states.input.split_num) %}",
                        "tmp_csv_file": "{\"inventory_query\":\"/tmp/job_db_to_file/JN_SL002-FF01_001/JN_SL002-FF01_001_temp_csv.csv\"}",
                        "parent_column": "store_id",
                        "sync_update_skip_flg": "1"
                      }
                    },
                    "End": true
                  }
                }
              },
              "QueryLanguage": "JSONata",
              "Items": "{% $parallel_num_array %}",
              "ItemSelector": {
                "split_num": "{% $states.context.Map.Item.Value %}"
              },
              "Next": "ZIP_Map"
            },
            "ZIP_Map": {
              "Type": "Map",
              "ItemProcessor": {
                "ProcessorConfig": {
                  "Mode": "INLINE"
                },
                "StartAt": "6_job_file_compress",
                "States": {
                  "6_job_file_compress": {
                    "QueryLanguage": "JSONata",
                    "Type": "Task",
                    "Resource": "arn:aws:states:::glue:startJobRun.sync",
                    "Arguments": {
                      "JobName": "job_file_compress",
                      "Arguments": {
                        "input_file_dir": "{% 'tmp/job_db_to_file/JN_SL002-FF01_001_' & $full_date & '/' %}",
                        "file_name": "{% 'txStoreInventory_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                        "output_file_dir": "{% 'input-output/EC_OUT/JN_SL002-FF01_001_' & $full_date & '/' %}",
                        "output_file_name": "{% 'txStoreInventory_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.zip' %}",
                        "backup_flag": "True",
                        "backup_file_dir": "{% 'back-up/job_db_to_file/JN_SL002-FF01_001_' & $full_date & '/' %}",
                        "jobnet_id": "JN_SL002-FF01_001"
                      }
                    },
                    "End": true
                  }
                }
              },
              "QueryLanguage": "JSONata",
              "Items": "{% $parallel_num_array %}",
              "ItemSelector": {
                "split_num": "{% $states.context.Map.Item.Value %}"
              },
              "Next": "7_sql_execute"
            },
            "7_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_SL002-FF01_001",
                  "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_SL002-FF01_after_001\"}]}' %}"
                }
              },
              "End": true
            }
          }
        }
