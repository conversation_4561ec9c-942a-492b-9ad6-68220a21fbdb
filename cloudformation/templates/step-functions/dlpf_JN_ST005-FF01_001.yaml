AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_ST005-FF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF-ST005-FF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_get_file"
            },
            "1_job_get_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_get_file",
                "Arguments": {
                  "source_secret_name": "COMMONS_SFTP_INFO",
                  "source_remote_file_full_path": "Commons/ToInterface/0069_TrOmsZaikoEc_*.tsv",
                  "s3_storage_file_full_path": "{% 'input-output/DLPF_OMS/JN_ST005-FF01_001_' & $full_date & '/EICM0010.tsv' %}",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/DLPF_OMS/JN_ST005-FF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_ST005-FF01_001",
                  "--TaskToken": "{% $states.context.Task.Token %}"
                }
              },
              "Catch": [
                {
                  "ErrorEquals": [
                    "FileNotFound"
                  ],
                  "Next": "file_not_found"
                }
              ],
              "End": true
            },
            "file_not_found": {
              "Type": "Pass",
              "End": true
            }
          }
        }
