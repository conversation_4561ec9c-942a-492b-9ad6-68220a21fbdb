-- データベースの作成
CREATE DATABASE "dlpf-dev";

-- dlpf-devにDB切替

-- aws_s3&aws_commonをインストール
--DROP EXTENSION aws_s3;
--DROP EXTENSION aws_common;
CREATE EXTENSION aws_s3 CASCADE;

-- -- 管理者ロールの作成
-- CREATE ROLE admin WITH
--     LOGIN
--     SUPERUSER
--     CREATEDB
--     CREATEROLE
--     PASSWORD 'password';

-- DDLロールの削除
-- DROP ROLE IF EXISTS dlpf_ope;
-- DROP ROLE IF EXISTS dlpf_api;
-- DROP ROLE IF EXISTS dlpf_batch;
-- DROP ROLE IF EXISTS mdm;

-- DD<PERSON>ロールの作成
CREATE ROLE dlpf_ope WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- DMLロール1の作成
CREATE ROLE dlpf_api WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- DMLロール2の作成
CREATE ROLE dlpf_batch WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- MDMロールの作成
CREATE ROLE mdm WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- webshopロール（ReadReplica向け）の作成
CREATE ROLE webshop WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- wsappロール（ReadReplica向け）の作成
CREATE ROLE wsapp WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- デフォルトの検索パスの設定
ALTER ROLE dlpf_ope SET search_path TO dlpf;
ALTER ROLE dlpf_api SET search_path TO dlpf;
ALTER ROLE dlpf_batch SET search_path TO dlpf;
ALTER ROLE mdm SET search_path TO mdm;
ALTER ROLE webshop SET search_path TO oms_readreplica;
ALTER ROLE wsapp SET search_path TO oms_readreplica;

-- extensionに対する権限の付与
GRANT USAGE ON SCHEMA aws_s3 TO dlpf_batch;
GRANT USAGE ON SCHEMA aws_commons TO dlpf_batch;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_s3 TO dlpf_batch;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_commons TO dlpf_batch;

GRANT CREATE ON DATABASE "dlpf-dev" TO "dlpf_ope";

-- dlpf_opeユーザに切替

-- スキーマの作成
CREATE SCHEMA dlpf;
CREATE SCHEMA mdm;
CREATE SCHEMA oms_readreplica;
CREATE SCHEMA deldata;

-- スキーマに対する権限の付与
GRANT USAGE ON SCHEMA dlpf TO dlpf_ope;
GRANT USAGE ON SCHEMA dlpf TO dlpf_api;
GRANT USAGE ON SCHEMA dlpf TO dlpf_batch;
GRANT USAGE ON SCHEMA mdm TO dlpf_batch;
GRANT USAGE ON SCHEMA mdm TO dlpf_ope;
GRANT USAGE ON SCHEMA mdm TO mdm;
GRANT USAGE ON SCHEMA mdm TO dlpf_api;
GRANT USAGE ON SCHEMA oms_readreplica TO dlpf_batch;
GRANT USAGE ON SCHEMA oms_readreplica TO dlpf_ope;
GRANT USAGE ON SCHEMA oms_readreplica TO webshop;
GRANT USAGE ON SCHEMA oms_readreplica TO wsapp;
GRANT USAGE ON SCHEMA oms_readreplica TO mdm;
GRANT USAGE ON SCHEMA deldata TO webshop;
GRANT USAGE ON SCHEMA deldata TO wsapp;

-- DDLロールに対する権限の付与
GRANT CREATE ON SCHEMA dlpf TO dlpf_ope;

-- 将来作成されるテーブルに対するDML権限の事前付与
ALTER DEFAULT PRIVILEGES IN SCHEMA dlpf
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO dlpf_api;

ALTER DEFAULT PRIVILEGES IN SCHEMA dlpf
    GRANT SELECT, INSERT, UPDATE, DELETE, TRUNCATE ON TABLES TO dlpf_batch;

ALTER DEFAULT PRIVILEGES IN SCHEMA mdm
    GRANT SELECT ON TABLES TO dlpf_api;

GRANT SELECT ON ALL TABLES IN SCHEMA mdm TO dlpf_ope;
GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA dlpf TO dlpf_api;
GRANT ALL ON ALL SEQUENCES IN SCHEMA dlpf TO dlpf_api;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON ALL TABLES IN SCHEMA dlpf TO dlpf_batch;
GRANT ALL ON ALL SEQUENCES IN SCHEMA dlpf TO dlpf_batch;
GRANT SELECT ON ALL TABLES IN SCHEMA mdm TO dlpf_batch;
GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA mdm TO mdm;
GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA oms_readreplica TO webshop;
GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA deldata TO webshop;
GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO wsapp;
GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO mdm;
GRANT SELECT ON ALL TABLES IN SCHEMA mdm TO dlpf_api;
GRANT ALL ON ALL SEQUENCES IN SCHEMA mdm TO dlpf_api;

GRANT SELECT ON ALL TABLES IN SCHEMA deldata TO wsapp;
ALTER DEFAULT PRIVILEGES IN SCHEMA deldata
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO webshop;
GRANT CREATE ON SCHEMA deldata TO webshop;
ALTER DEFAULT PRIVILEGES IN SCHEMA deldata
    GRANT SELECT ON TABLES TO wsapp;

-- dlpfmasterユーザに切替

GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO dlpf_batch;
GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO dlpf_ope;

ALTER DEFAULT PRIVILEGES IN SCHEMA mdm
    GRANT SELECT ON TABLES TO dlpf_ope, dlpf_batch;
ALTER DEFAULT PRIVILEGES IN SCHEMA mdm
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO mdm;

ALTER DEFAULT PRIVILEGES IN SCHEMA oms_readreplica
    GRANT SELECT ON TABLES TO dlpf_ope;
ALTER DEFAULT PRIVILEGES IN SCHEMA oms_readreplica
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO webshop;
ALTER DEFAULT PRIVILEGES IN SCHEMA oms_readreplica
    GRANT SELECT ON TABLES TO wsapp;
ALTER DEFAULT PRIVILEGES IN SCHEMA oms_readreplica
    GRANT SELECT ON TABLES TO mdm;
