-- データベースの作成
CREATE DATABASE "dlpf-stg" ENCODING='UTF8' LOCALE='ja_JP.UTF-8' TEMPLATE='template0';

-- dlpf-stgにDB切替

-- aws_s3&aws_commonをインストール
--DROP EXTENSION aws_s3;
--DROP EXTENSION aws_common;
CREATE EXTENSION aws_s3 CASCADE;

-- -- 管理者ロールの作成
-- CREATE ROLE admin WITH
--     LOGIN
--     SUPERUSER
--     CREATEDB
--     CREATEROLE
--     PASSWORD 'password';

-- DDLロールの削除
-- DROP ROLE IF EXISTS dlpf_ope;
-- DROP ROLE IF EXISTS dlpf_api;
-- DROP ROLE IF EXISTS dlpf_batch;
-- DROP ROLE IF EXISTS mdm;

-- DDLロールの作成
CREATE ROLE dlpf_ope WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'vS$HH#)bnkQ=+MoQ3f/q1_A42RUm';

-- DMLロール1の作成
CREATE ROLE dlpf_api WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'l05abq0HJzsMQ47/BdGOFalB19LD';

-- DMLロール2の作成
CREATE ROLE dlpf_batch WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'OT6EFy)T)9kx+k93Raj#Pv5w1I$*';

-- MDMロールの作成
CREATE ROLE mdm WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'dki5HRV#v#H*klrGJP/h*K$R)724';

-- -- webshopロール（ReadReplica向け）の作成
-- CREATE ROLE webshop WITH
--     LOGIN
--     NOSUPERUSER
--     NOCREATEDB
--     NOCREATEROLE
--     PASSWORD 'password';

-- -- wsappロール（ReadReplica向け）の作成
-- CREATE ROLE wsapp WITH
--     LOGIN
--     NOSUPERUSER
--     NOCREATEDB
--     NOCREATEROLE
--     PASSWORD 'password';

-- デフォルトの検索パスの設定
ALTER ROLE dlpf_ope SET search_path TO dlpf;
ALTER ROLE dlpf_api SET search_path TO dlpf;
ALTER ROLE dlpf_batch SET search_path TO dlpf;
ALTER ROLE mdm SET search_path TO mdm;

-- ALTER ROLE webshop SET search_path TO oms_readreplica;
-- ALTER ROLE wsapp SET search_path TO oms_readreplica;

-- extensionに対する権限の付与
GRANT USAGE ON SCHEMA aws_s3 TO dlpf_batch;
GRANT USAGE ON SCHEMA aws_commons TO dlpf_batch;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_s3 TO dlpf_batch;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_commons TO dlpf_batch;
GRANT USAGE ON SCHEMA mdm TO dlpf_api;


GRANT CREATE ON DATABASE "dlpf-stg" TO "dlpf_ope";

-- dlpf_opeユーザに切替

-- スキーマの作成
CREATE SCHEMA dlpf;
CREATE SCHEMA mdm;
-- CREATE SCHEMA oms_readreplica;

-- スキーマに対する権限の付与
GRANT USAGE ON SCHEMA dlpf TO dlpf_ope;
GRANT USAGE ON SCHEMA dlpf TO dlpf_api;
GRANT USAGE ON SCHEMA dlpf TO dlpf_batch;
GRANT USAGE ON SCHEMA mdm TO dlpf_batch;
GRANT USAGE ON SCHEMA mdm TO dlpf_ope;
GRANT USAGE ON SCHEMA mdm TO mdm;
-- GRANT USAGE ON SCHEMA oms_readreplica TO dlpf_batch;
-- GRANT USAGE ON SCHEMA oms_readreplica TO dlpf_ope;
-- GRANT USAGE ON SCHEMA oms_readreplica TO webshop;
-- GRANT USAGE ON SCHEMA oms_readreplica TO wsapp;
-- GRANT USAGE ON SCHEMA oms_readreplica TO mdm;

-- DDLロールに対する権限の付与
GRANT CREATE ON SCHEMA dlpf TO dlpf_ope;

-- 将来作成されるテーブルに対するDML権限の事前付与
ALTER DEFAULT PRIVILEGES IN SCHEMA dlpf
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO dlpf_api;

ALTER DEFAULT PRIVILEGES IN SCHEMA dlpf
    GRANT SELECT, INSERT, UPDATE, DELETE, TRUNCATE ON TABLES TO dlpf_batch;

GRANT SELECT ON ALL TABLES IN SCHEMA mdm TO dlpf_ope;
GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA dlpf TO dlpf_api;
GRANT ALL ON ALL SEQUENCES IN SCHEMA dlpf TO dlpf_api;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON ALL TABLES IN SCHEMA dlpf TO dlpf_batch;
GRANT ALL ON ALL SEQUENCES IN SCHEMA dlpf TO dlpf_batch;
GRANT SELECT ON ALL TABLES IN SCHEMA mdm TO dlpf_batch;
GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA mdm TO mdm;
-- GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA oms_readreplica TO webshop;
-- GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO wsapp;
-- GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO mdm;

-- dlpfmasterユーザに切替

-- GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO dlpf_batch;
-- GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO dlpf_ope;

ALTER DEFAULT PRIVILEGES IN SCHEMA mdm
    GRANT SELECT ON TABLES TO dlpf_ope, dlpf_batch;
ALTER DEFAULT PRIVILEGES IN SCHEMA mdm
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO mdm;
ALTER DEFAULT PRIVILEGES IN SCHEMA mdm
    GRANT SELECT ON TABLES TO dlpf_api;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA oms_readreplica
--     GRANT SELECT ON TABLES TO dlpf_ope;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA oms_readreplica
--     GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO webshop;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA oms_readreplica
--     GRANT SELECT ON TABLES TO wsapp;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA oms_readreplica
--     GRANT SELECT ON TABLES TO mdm;

-- 以下FDW構築
CREATE SERVER my_fdw_target Foreign Data Wrapper postgres_fdw OPTIONS (DBNAME 'webshopdb', HOST 'oms.cluster-ro-c9oguq08u76t.ap-northeast-1.rds.amazonaws.com', SSLMODE 'require');
CREATE USER MAPPING FOR mdm SERVER my_fdw_target OPTIONS (user 'dlpf', password '?7f7}YyGt<G5$!nfFPHERef)');
CREATE FOREIGN TABLE oms_readreplica.regular_sale_base(
    shop_code                      varchar(16) not null,
    regular_sale_code              varchar(16) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    regular_cycle_kind_list        varchar(100),
    regular_cycle_days_list        varchar(100),
    regular_cycle_months_list      varchar(100),
    regular_sale_stop_from         numeric(5,0),
    regular_sale_stop_to           numeric(5,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null
 ) server my_fdw_target OPTIONS( schema_name 'public',TABLE_NAME 'regular_sale_base');
CREATE FOREIGN TABLE oms_readreplica.regular_sale_commodity(
    shop_code                      varchar(16) not null,
    regular_sale_code              varchar(16) not null,
    regular_sale_composition_no    varchar(8) not null,
    sku_code                       varchar(24) not null,
    commodity_code                 varchar(16) not null,
    display_order                  numeric(8,0) not null,
    regular_sale_commodity_type    varchar(1),
    regular_sale_commodity_point   numeric(3,0),
    difference_price               numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null
 ) server my_fdw_target OPTIONS( schema_name 'public', TABLE_NAME 'regular_sale_commodity');
CREATE FOREIGN TABLE oms_readreplica.regular_sale_composition(
    shop_code                      varchar(16) not null,
    regular_sale_code              varchar(16) not null,
    regular_sale_composition_no    varchar(8) not null,
    regular_sale_composition_name  varchar(50),
    regular_order_count_min_limit  numeric(5,0) not null,
    regular_order_count_max_limit  numeric(5,0),
    regular_order_count_interval   numeric(5,0) not null,
    retail_price                   numeric(8,0) not null,
    regular_sale_commodity_point   numeric(3,0),
    display_order                  numeric(8,0),
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null
 ) server my_fdw_target OPTIONS( schema_name 'public', TABLE_NAME 'regular_sale_composition');
CREATE FOREIGN TABLE oms_readreplica.set_commodity_composition(
    shop_code                      varchar(16) not null,
    commodity_code                 varchar(16) not null,
    child_commodity_code           varchar(16) not null,
    composition_quantity           numeric(2,0) not null,
    composition_order              numeric(2,0) not null,
    orm_rowid                      numeric(38,0) not null,
    created_user                   varchar(100) not null,
    created_datetime               timestamp(3) not null,
    updated_user                   varchar(100) not null,
    updated_datetime               timestamp(3) not null
 ) server my_fdw_target OPTIONS( schema_name 'public', TABLE_NAME 'set_commodity_composition');
GRANT USAGE ON SCHEMA oms_readreplica TO mdm;
GRANT SELECT ON ALL TABLES IN SCHEMA oms_readreplica TO mdm;
GRANT SELECT ON ALL TABLES IN SCHEMA mdm TO dlpf_api;
GRANT ALL ON ALL SEQUENCES IN SCHEMA mdm TO dlpf_api;
