CREATE TABLE campaign_combi_limit_view (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_combi_limit_code VARCHAR(16) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (campaign_instructions_code, campaign_combi_limit_code)
);
COMMENT ON TABLE campaign_combi_limit_view IS 'キャンペーン併用不可';
COMMENT ON COLUMN campaign_combi_limit_view.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_combi_limit_view.campaign_combi_limit_code IS '併用不可キャンペーン設定コード';
COMMENT ON COLUMN campaign_combi_limit_view.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_combi_limit_view.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_combi_limit_view.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_combi_limit_view.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_combi_limit_view.updated_datetime IS '更新日時';
COMMENT ON COLUMN campaign_combi_limit_view.delete_flg IS '削除フラグ';
COMMENT ON COLUMN campaign_combi_limit_view.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN campaign_combi_limit_view.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN campaign_combi_limit_view.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN campaign_combi_limit_view.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN campaign_combi_limit_view.d_version IS 'デ連バージョン';

