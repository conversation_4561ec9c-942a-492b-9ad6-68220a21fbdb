CREATE TABLE campaign_customer_view_work (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    customer_code VARCHAR(16) NOT NULL,
    joken_type VARCHAR(1) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    PRIMARY KEY (campaign_instructions_code, customer_code)
);
COMMENT ON TABLE campaign_customer_view_work IS 'キャンペーン設定顧客ワーク';
COMMENT ON COLUMN campaign_customer_view_work.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_customer_view_work.customer_code IS '顧客コード';
COMMENT ON COLUMN campaign_customer_view_work.joken_type IS '条件分類';
COMMENT ON COLUMN campaign_customer_view_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_customer_view_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_customer_view_work.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_customer_view_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_customer_view_work.updated_datetime IS '更新日時';
COMMENT ON COLUMN campaign_customer_view_work.delete_flg IS '削除フラグ';

