CREATE TABLE campaign_order_group_view_work (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_group_no NUMERIC(8,0) NOT NULL,
    campaign_joken_disp VARCHAR(50),
    exclude_joken_disp VARCHAR(50),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    PRIMARY KEY (campaign_instructions_code, campaign_group_no)
);
COMMENT ON TABLE campaign_order_group_view_work IS 'キャンペーン設定条件グループワーク';
COMMENT ON COLUMN campaign_order_group_view_work.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_order_group_view_work.campaign_group_no IS 'キャンペーン設定グループ番号';
COMMENT ON COLUMN campaign_order_group_view_work.campaign_joken_disp IS 'キャンペーン条件（一覧表示用）';
COMMENT ON COLUMN campaign_order_group_view_work.exclude_joken_disp IS 'キャンペーン除外条件（一覧表示用）';
COMMENT ON COLUMN campaign_order_group_view_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_order_group_view_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_order_group_view_work.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_order_group_view_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_order_group_view_work.updated_datetime IS '更新日時';
COMMENT ON COLUMN campaign_order_group_view_work.delete_flg IS '削除フラグ';

