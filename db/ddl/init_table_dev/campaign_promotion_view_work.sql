CREATE TABLE campaign_promotion_view_work (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    promotion_no NUMERIC(9) NOT NULL,
    promotion_type VARCHAR(2) NOT NULL,
    shop_code VARCHAR(16),
    commodity_code VARCHAR(16),
    commodity_name VARCHAR(100),
    present_qt NUMERIC(3,0),
    discount_rate NUMERIC(3,0),
    discount_amount NUMERIC(8,0),
    discount_retail_price NUMERIC(8,0),
    shipping_charge NUMERIC(8,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    PRIMARY KEY (campaign_instructions_code, promotion_no)
);
COMMENT ON TABLE campaign_promotion_view_work IS 'キャンペーン設定プロモーションワーク';
COMMENT ON COLUMN campaign_promotion_view_work.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_promotion_view_work.promotion_no IS 'プロモーション番号';
COMMENT ON COLUMN campaign_promotion_view_work.promotion_type IS 'プロモーション種別';
COMMENT ON COLUMN campaign_promotion_view_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN campaign_promotion_view_work.commodity_code IS '商品コード';
COMMENT ON COLUMN campaign_promotion_view_work.commodity_name IS '商品名称';
COMMENT ON COLUMN campaign_promotion_view_work.present_qt IS 'プレゼント数量';
COMMENT ON COLUMN campaign_promotion_view_work.discount_rate IS '値引率';
COMMENT ON COLUMN campaign_promotion_view_work.discount_amount IS '値引額';
COMMENT ON COLUMN campaign_promotion_view_work.discount_retail_price IS '値引後販売価格';
COMMENT ON COLUMN campaign_promotion_view_work.shipping_charge IS '送料';
COMMENT ON COLUMN campaign_promotion_view_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_promotion_view_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_promotion_view_work.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_promotion_view_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_promotion_view_work.updated_datetime IS '更新日時';
COMMENT ON COLUMN campaign_promotion_view_work.delete_flg IS '削除フラグ';

