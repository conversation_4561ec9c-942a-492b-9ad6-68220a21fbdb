CREATE TABLE dhc_card_mst_work (
    record_gb  VARCHAR(1) NOT NULL,
    smcc_gb  VARCHAR(2),
    pass1 VARCHAR(1),
    card_no  VARCHAR(16),
    admission_date  VARCHAR(8),
    subscription_date  VARCHAR(8),
    cust_name  VARCHAR(20),
    pass2 VARCHAR(1),
    valid_date  VARCHAR(4),
    trust_withdrawal_yn  VARCHAR(1),
    trust_accident_yn  VARCHAR(1),
    trust_attention_yn  VARCHAR(1),
    pass3 VARCHAR(1),
    shop_code  VARCHAR(5),
    pass4 VARCHAR(1),
    subscription_gb  VARCHAR(1),
    staff_code  VARCHAR(19),
    pass5 VARCHAR(1),
    memb_no  VARCHAR(20),
    cust_no  VARCHAR(20),
    modify_cust_name_yn  VARCHAR(1),
    pass6 VARCHAR(1),
    modify_valid_date_yn  VARCHAR(1),
    modify_cooperation_yn  VARCHAR(1),
    modify_trust_yn  VARCHAR(1),
    pass7 VARCHAR(1)
);
COMMENT ON TABLE dhc_card_mst_work IS 'DHCカードマスタワーク';
COMMENT ON COLUMN dhc_card_mst_work.record_gb  IS 'レコード区分';
COMMENT ON COLUMN dhc_card_mst_work.smcc_gb  IS 'SMCC区分';
COMMENT ON COLUMN dhc_card_mst_work.pass1 IS 'フォーマット対応用1';
COMMENT ON COLUMN dhc_card_mst_work.card_no  IS 'カード番号';
COMMENT ON COLUMN dhc_card_mst_work.admission_date  IS '承認日';
COMMENT ON COLUMN dhc_card_mst_work.subscription_date  IS '申込日';
COMMENT ON COLUMN dhc_card_mst_work.cust_name  IS '顧客名';
COMMENT ON COLUMN dhc_card_mst_work.pass2 IS 'フォーマット対応用2';
COMMENT ON COLUMN dhc_card_mst_work.valid_date  IS '有効期限';
COMMENT ON COLUMN dhc_card_mst_work.trust_withdrawal_yn  IS '解約';
COMMENT ON COLUMN dhc_card_mst_work.trust_accident_yn  IS '信頼事故';
COMMENT ON COLUMN dhc_card_mst_work.trust_attention_yn  IS '要注意';
COMMENT ON COLUMN dhc_card_mst_work.pass3 IS 'フォーマット対応用3';
COMMENT ON COLUMN dhc_card_mst_work.shop_code  IS 'ショップコード';
COMMENT ON COLUMN dhc_card_mst_work.pass4 IS 'フォーマット対応用4';
COMMENT ON COLUMN dhc_card_mst_work.subscription_gb  IS '申込区分';
COMMENT ON COLUMN dhc_card_mst_work.staff_code  IS 'スタッフコード';
COMMENT ON COLUMN dhc_card_mst_work.pass5 IS 'フォーマット対応用5';
COMMENT ON COLUMN dhc_card_mst_work.memb_no  IS '会員番号';
COMMENT ON COLUMN dhc_card_mst_work.cust_no  IS '顧客番号';
COMMENT ON COLUMN dhc_card_mst_work.modify_cust_name_yn  IS '氏名変更';
COMMENT ON COLUMN dhc_card_mst_work.pass6 IS 'フォーマット対応用6';
COMMENT ON COLUMN dhc_card_mst_work.modify_valid_date_yn  IS '有効期限変更';
COMMENT ON COLUMN dhc_card_mst_work.modify_cooperation_yn  IS '協力変更';
COMMENT ON COLUMN dhc_card_mst_work.modify_trust_yn  IS '信頼変更';
COMMENT ON COLUMN dhc_card_mst_work.pass7 IS 'フォーマット対応用7';

