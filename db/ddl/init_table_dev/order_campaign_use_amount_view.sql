CREATE TABLE order_campaign_use_amount_view (
    order_no VARCHAR(16) NOT NULL,
    tax_group_code VA<PERSON>HAR(8) NOT NULL,
    tax_no NUMERIC(3,0) NOT NULL,
    use_code_type VARCHAR(1) NOT NULL,
    use_code VARCHAR(16) NOT NULL,
    use_amount NUMERIC(8,0) NOT NULL,
    tax_rate NUMERIC(3,0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (order_no, tax_group_code, tax_no, use_code_type, use_code)
);
COMMENT ON TABLE order_campaign_use_amount_view IS '受注キャンペーンクーポン利用額';
COMMENT ON COLUMN order_campaign_use_amount_view.order_no IS '受注番号';
COMMENT ON COLUMN order_campaign_use_amount_view.tax_group_code IS '消費税グループコード';
COMMENT ON COLUMN order_campaign_use_amount_view.tax_no IS '消費税番号';
COMMENT ON COLUMN order_campaign_use_amount_view.use_code_type IS '利用コード種別';
COMMENT ON COLUMN order_campaign_use_amount_view.use_code IS '利用コード';
COMMENT ON COLUMN order_campaign_use_amount_view.use_amount IS '利用額';
COMMENT ON COLUMN order_campaign_use_amount_view.tax_rate IS '消費税率';
COMMENT ON COLUMN order_campaign_use_amount_view.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN order_campaign_use_amount_view.created_user IS '作成ユーザ';
COMMENT ON COLUMN order_campaign_use_amount_view.created_datetime IS '作成日時';
COMMENT ON COLUMN order_campaign_use_amount_view.updated_user IS '更新ユーザ';
COMMENT ON COLUMN order_campaign_use_amount_view.updated_datetime IS '更新日時';
COMMENT ON COLUMN order_campaign_use_amount_view.delete_flg IS '削除フラグ';
COMMENT ON COLUMN order_campaign_use_amount_view.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN order_campaign_use_amount_view.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN order_campaign_use_amount_view.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN order_campaign_use_amount_view.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN order_campaign_use_amount_view.d_version IS 'デ連バージョン';

