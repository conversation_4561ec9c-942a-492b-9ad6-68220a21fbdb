CREATE TABLE order_campaign_view (
    order_no VARCHAR(16) NOT NULL,
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_instructions_name VARCHAR(50) NOT NULL,
    campaign_description VARCHAR(100),
    campaign_end_date TIMESTAMP(0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (order_no, campaign_instructions_code)
);
COMMENT ON TABLE order_campaign_view IS '受注キャンペーン適用';
COMMENT ON COLUMN order_campaign_view.order_no IS '受注番号';
COMMENT ON COLUMN order_campaign_view.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN order_campaign_view.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN order_campaign_view.campaign_description IS 'キャンペーン内容';
COMMENT ON COLUMN order_campaign_view.campaign_end_date IS 'キャンペーン適用終了日';
COMMENT ON COLUMN order_campaign_view.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN order_campaign_view.created_user IS '作成ユーザ';
COMMENT ON COLUMN order_campaign_view.created_datetime IS '作成日時';
COMMENT ON COLUMN order_campaign_view.updated_user IS '更新ユーザ';
COMMENT ON COLUMN order_campaign_view.updated_datetime IS '更新日時';
COMMENT ON COLUMN order_campaign_view.delete_flg IS '削除フラグ';
COMMENT ON COLUMN order_campaign_view.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN order_campaign_view.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN order_campaign_view.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN order_campaign_view.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN order_campaign_view.d_version IS 'デ連バージョン';

