CREATE TABLE out_achievements_mail_order_issue_work (
    wh_code VARCHAR(3) NOT NULL,
    accept_no VARCHAR(22) NOT NULL,
    logimane_slip_no VARCHAR(13) NOT NULL,
    goods_code VARCHAR(10) NOT NULL,
    out_qty NUMERIC(9) NOT NULL,
    close_date VARCHAR(8) NOT NULL,
    PRIMARY KEY (accept_no, goods_code)
);
COMMENT ON TABLE out_achievements_mail_order_issue_work IS '出荷実績(通販)_出庫ワーク';
COMMENT ON COLUMN out_achievements_mail_order_issue_work.wh_code IS '倉庫コード';
COMMENT ON COLUMN out_achievements_mail_order_issue_work.accept_no IS '基幹出荷指示番号';
COMMENT ON COLUMN out_achievements_mail_order_issue_work.logimane_slip_no IS '入出庫伝票番号';
COMMENT ON COLUMN out_achievements_mail_order_issue_work.goods_code IS '商品番号';
COMMENT ON COLUMN out_achievements_mail_order_issue_work.out_qty IS '出庫数';
COMMENT ON COLUMN out_achievements_mail_order_issue_work.close_date IS '出庫確定日';

