CREATE TABLE out_indicate_header_work (
    accept_no VARCHAR(22) NOT NULL,
    cust_no VARCHAR(10) NOT NULL,
    record_no VARCHAR(6),
    cust_name VARCHAR(100),
    post_no VARCHAR(8),
    addr1 VARCHAR(50),
    addr2 VARCHAR(50),
    addr3 VARCHAR(50),
    tel_no VARCHAR(15),
    prefecture_code NUMERIC(2,0),
    cust_flg NUMERIC(1,0),
    order_date TIMESTAMP,
    pay_kb VARCHAR(2),
    total_price NUMERIC(10,0),
    delive_cust_name VARCHAR(100),
    delive_post_no VARCHAR(8),
    delive_addr1 VARCHAR(50),
    delive_addr2 VARCHAR(50),
    delive_addr3 VARCHAR(50),
    delive_tel_no VARCHAR(15),
    gift_flg VARCHAR(1),
    kibou_ymd TIMESTAMP,
    night_flg VARCHAR(2),
    cosme_price NUMERIC(10,0),
    health_price NUMERIC(10,0),
    inner_price NUMERIC(10,0),
    update_date TIMESTAMP,
    chit_print_date TIMESTAMP,
    yamato_bar_code VARCHAR(14),
    gyosha_flg VARCHAR(1),
    status_flg VARCHAR(1),
    slip_ono VARCHAR(20),
    order_no VARCHAR(14),
    clinic_name VARCHAR(100),
    shipment_date TIMESTAMP,
    shipment_plan_date TIMESTAMP,
    pack_cnt NUMERIC(10),
    store_code VARCHAR(8),
    period_flg VARCHAR(2),
    delivery_box_gb VARCHAR(2),
    air_delivery_yn VARCHAR(1),
    tax_amt NUMERIC(10,0),
    conveni_yn VARCHAR(1),
    pudo_yn VARCHAR(1),
    inplan_yn VARCHAR(1),
    over_stock_yn VARCHAR(1),
    reserve_order_yn VARCHAR(1),
    gift_rapping_yn VARCHAR(1),
    kanshi_yn VARCHAR(1),
    fusoku_yn VARCHAR(1),
    airplane_yn VARCHAR(1),
    satofuru_yn VARCHAR(1),
    tokusha_yn VARCHAR(1),
    rakugaki_yn VARCHAR(1),
    multi_sample_yn VARCHAR(1),
    slip_size_code VARCHAR(1) NOT NULL,
    wh_code VARCHAR(3) NOT NULL,
    agent_cd VARCHAR(13) NOT NULL,
    import_yn VARCHAR(1),
    import_date TIMESTAMP,
    PRIMARY KEY (accept_no)
);
COMMENT ON TABLE out_indicate_header_work IS '出荷指示ヘッダワーク';
COMMENT ON COLUMN out_indicate_header_work.accept_no IS '受付番号';
COMMENT ON COLUMN out_indicate_header_work.cust_no IS '顧客番号';
COMMENT ON COLUMN out_indicate_header_work.record_no IS 'レコード番号';
COMMENT ON COLUMN out_indicate_header_work.cust_name IS '顧客名';
COMMENT ON COLUMN out_indicate_header_work.post_no IS '郵便番号';
COMMENT ON COLUMN out_indicate_header_work.addr1 IS '顧客住所１';
COMMENT ON COLUMN out_indicate_header_work.addr2 IS '顧客住所２';
COMMENT ON COLUMN out_indicate_header_work.addr3 IS '顧客住所３';
COMMENT ON COLUMN out_indicate_header_work.tel_no IS '電話番号';
COMMENT ON COLUMN out_indicate_header_work.prefecture_code IS '都道府県コード';
COMMENT ON COLUMN out_indicate_header_work.cust_flg IS '顧客フラグ';
COMMENT ON COLUMN out_indicate_header_work.order_date IS '注文日';
COMMENT ON COLUMN out_indicate_header_work.pay_kb IS '支払区分';
COMMENT ON COLUMN out_indicate_header_work.total_price IS '合計金額';
COMMENT ON COLUMN out_indicate_header_work.delive_cust_name IS '配達先顧客名';
COMMENT ON COLUMN out_indicate_header_work.delive_post_no IS '配達先郵便番号';
COMMENT ON COLUMN out_indicate_header_work.delive_addr1 IS '配達先住所１';
COMMENT ON COLUMN out_indicate_header_work.delive_addr2 IS '配達先住所２';
COMMENT ON COLUMN out_indicate_header_work.delive_addr3 IS '配達先住所３';
COMMENT ON COLUMN out_indicate_header_work.delive_tel_no IS '配達先電話番号';
COMMENT ON COLUMN out_indicate_header_work.gift_flg IS 'ギフトフラグ';
COMMENT ON COLUMN out_indicate_header_work.kibou_ymd IS '配達希望日';
COMMENT ON COLUMN out_indicate_header_work.night_flg IS '夜間可否';
COMMENT ON COLUMN out_indicate_header_work.cosme_price IS '化粧品金額';
COMMENT ON COLUMN out_indicate_header_work.health_price IS '健康食品金額';
COMMENT ON COLUMN out_indicate_header_work.inner_price IS 'インナーウェア金額';
COMMENT ON COLUMN out_indicate_header_work.update_date IS '更新日時';
COMMENT ON COLUMN out_indicate_header_work.chit_print_date IS '伝票出力日';
COMMENT ON COLUMN out_indicate_header_work.yamato_bar_code IS 'バーコード';
COMMENT ON COLUMN out_indicate_header_work.gyosha_flg IS '業者フラグ';
COMMENT ON COLUMN out_indicate_header_work.status_flg IS 'ステータスフラグ';
COMMENT ON COLUMN out_indicate_header_work.slip_ono IS '出庫伝票番号';
COMMENT ON COLUMN out_indicate_header_work.order_no IS '注文番号';
COMMENT ON COLUMN out_indicate_header_work.clinic_name IS '病院名';
COMMENT ON COLUMN out_indicate_header_work.shipment_date IS '出荷日';
COMMENT ON COLUMN out_indicate_header_work.shipment_plan_date IS '出荷予定日';
COMMENT ON COLUMN out_indicate_header_work.pack_cnt IS '包装数';
COMMENT ON COLUMN out_indicate_header_work.store_code IS '着店コード';
COMMENT ON COLUMN out_indicate_header_work.period_flg IS '定期フラグ';
COMMENT ON COLUMN out_indicate_header_work.delivery_box_gb IS '配達BOX区分';
COMMENT ON COLUMN out_indicate_header_work.air_delivery_yn IS '航空輸送可否';
COMMENT ON COLUMN out_indicate_header_work.tax_amt IS '消費税';
COMMENT ON COLUMN out_indicate_header_work.conveni_yn IS 'コンビニ受取可否';
COMMENT ON COLUMN out_indicate_header_work.pudo_yn IS 'PUDO利用可否';
COMMENT ON COLUMN out_indicate_header_work.inplan_yn IS '入庫予定可否';
COMMENT ON COLUMN out_indicate_header_work.over_stock_yn IS '過受注可否';
COMMENT ON COLUMN out_indicate_header_work.reserve_order_yn IS '予約受注可否';
COMMENT ON COLUMN out_indicate_header_work.gift_rapping_yn IS 'ギフトラッピング可否';
COMMENT ON COLUMN out_indicate_header_work.kanshi_yn IS '注文監視可否';
COMMENT ON COLUMN out_indicate_header_work.fusoku_yn IS '不足可否';
COMMENT ON COLUMN out_indicate_header_work.airplane_yn IS '航空便不可可否';
COMMENT ON COLUMN out_indicate_header_work.satofuru_yn IS 'さとふる可否';
COMMENT ON COLUMN out_indicate_header_work.tokusha_yn IS '特別社販可否';
COMMENT ON COLUMN out_indicate_header_work.rakugaki_yn IS 'らくがき板可否';
COMMENT ON COLUMN out_indicate_header_work.multi_sample_yn IS '複数サンプル可否';
COMMENT ON COLUMN out_indicate_header_work.slip_size_code IS '伝票サイズコード';
COMMENT ON COLUMN out_indicate_header_work.wh_code IS '倉庫コード';
COMMENT ON COLUMN out_indicate_header_work.agent_cd IS '取扱店ＣＤ';
COMMENT ON COLUMN out_indicate_header_work.import_yn IS '取込可否';
COMMENT ON COLUMN out_indicate_header_work.import_date IS '取込日';

