CREATE TABLE regular_sale_base_view (
    shop_code VARCHAR(16) NOT NULL,
    regular_sale_code VA<PERSON>HAR(16) NOT NULL,
    sku_code VARCHAR(24) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    regular_cycle_kind_list VARCHAR(100),
    regular_cycle_days_list VARCHAR(100),
    regular_cycle_months_list VARCHAR(100),
    regular_sale_stop_from NUMERIC(5,0),
    regular_sale_stop_to NUMERIC(5,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_code, regular_sale_code)
);
COMMENT ON TABLE regular_sale_base_view IS '定期便基本情報';
COMMENT ON COLUMN regular_sale_base_view.shop_code IS 'ショップコード';
COMMENT ON COLUMN regular_sale_base_view.regular_sale_code IS '定期便コード';
COMMENT ON COLUMN regular_sale_base_view.sku_code IS 'SKUコード';
COMMENT ON COLUMN regular_sale_base_view.commodity_code IS '商品コード';
COMMENT ON COLUMN regular_sale_base_view.regular_cycle_kind_list IS '定期サイクル種別指定';
COMMENT ON COLUMN regular_sale_base_view.regular_cycle_days_list IS '定期サイクル日付間隔指定';
COMMENT ON COLUMN regular_sale_base_view.regular_cycle_months_list IS '定期サイクル○ヶ月間隔指定';
COMMENT ON COLUMN regular_sale_base_view.regular_sale_stop_from IS '定期休止回次FROM';
COMMENT ON COLUMN regular_sale_base_view.regular_sale_stop_to IS '定期休止回次TO';
COMMENT ON COLUMN regular_sale_base_view.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN regular_sale_base_view.created_user IS '作成ユーザ';
COMMENT ON COLUMN regular_sale_base_view.created_datetime IS '作成日時';
COMMENT ON COLUMN regular_sale_base_view.updated_user IS '更新ユーザ';
COMMENT ON COLUMN regular_sale_base_view.updated_datetime IS '更新日時';
COMMENT ON COLUMN regular_sale_base_view.delete_flg IS '削除フラグ';
COMMENT ON COLUMN regular_sale_base_view.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN regular_sale_base_view.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN regular_sale_base_view.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN regular_sale_base_view.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN regular_sale_base_view.d_version IS 'デ連バージョン';

