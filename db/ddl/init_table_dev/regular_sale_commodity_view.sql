CREATE TABLE regular_sale_commodity_view (
    shop_code VARCHAR(16) NOT NULL,
    regular_sale_code VARCHAR(16) NOT NULL,
    regular_sale_composition_no VARCHAR(8) NOT NULL,
    sku_code VARCHAR(24) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    display_order NUMERIC(8,0) NOT NULL,
    regular_sale_commodity_type VARCHAR(1),
    regular_sale_commodity_point NUMERIC(3,0),
    difference_price NUMERIC(8,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_code, regular_sale_code, regular_sale_composition_no, sku_code)
);
COMMENT ON TABLE regular_sale_commodity_view IS '定期便商品構成';
COMMENT ON COLUMN regular_sale_commodity_view.shop_code IS 'ショップコード';
COMMENT ON COLUMN regular_sale_commodity_view.regular_sale_code IS '定期便コード';
COMMENT ON COLUMN regular_sale_commodity_view.regular_sale_composition_no IS '定期便構成グループコード';
COMMENT ON COLUMN regular_sale_commodity_view.sku_code IS 'SKUコード';
COMMENT ON COLUMN regular_sale_commodity_view.commodity_code IS '商品コード';
COMMENT ON COLUMN regular_sale_commodity_view.display_order IS '表示順';
COMMENT ON COLUMN regular_sale_commodity_view.regular_sale_commodity_type IS '定期便商品構成区分';
COMMENT ON COLUMN regular_sale_commodity_view.regular_sale_commodity_point IS '定期便商品構成ポイント';
COMMENT ON COLUMN regular_sale_commodity_view.difference_price IS '増減金額';
COMMENT ON COLUMN regular_sale_commodity_view.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN regular_sale_commodity_view.created_user IS '作成ユーザ';
COMMENT ON COLUMN regular_sale_commodity_view.created_datetime IS '作成日時';
COMMENT ON COLUMN regular_sale_commodity_view.updated_user IS '更新ユーザ';
COMMENT ON COLUMN regular_sale_commodity_view.updated_datetime IS '更新日時';
COMMENT ON COLUMN regular_sale_commodity_view.delete_flg IS '削除フラグ';
COMMENT ON COLUMN regular_sale_commodity_view.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN regular_sale_commodity_view.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN regular_sale_commodity_view.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN regular_sale_commodity_view.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN regular_sale_commodity_view.d_version IS 'デ連バージョン';

