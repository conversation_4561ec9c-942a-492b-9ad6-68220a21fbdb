CREATE TABLE regular_sale_composition_view_work (
    shop_code VARCHAR(16) NOT NULL,
    regular_sale_code VARCHAR(16) NOT NULL,
    regular_sale_composition_no VARCHAR(8) NOT NULL,
    regular_sale_composition_name VARCHAR(50),
    regular_order_count_min_limit NUMERIC(5,0) NOT NULL,
    regular_order_count_max_limit NUMERIC(5,0),
    regular_order_count_interval NUMERIC(5,0) NOT NULL,
    retail_price NUMERIC(8,0) NOT NULL,
    regular_sale_commodity_point NUMERIC(3,0),
    display_order NUMERIC(8,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    PRIMARY KEY (shop_code, regular_sale_code, regular_sale_composition_no)
);
COMMENT ON TABLE regular_sale_composition_view_work IS '定期便構成情報ワーク';
COMMENT ON COLUMN regular_sale_composition_view_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN regular_sale_composition_view_work.regular_sale_code IS '定期便コード';
COMMENT ON COLUMN regular_sale_composition_view_work.regular_sale_composition_no IS '定期便構成グループコード';
COMMENT ON COLUMN regular_sale_composition_view_work.regular_sale_composition_name IS '定期便構成名称';
COMMENT ON COLUMN regular_sale_composition_view_work.regular_order_count_min_limit IS '定期回次下限';
COMMENT ON COLUMN regular_sale_composition_view_work.regular_order_count_max_limit IS '定期回次上限';
COMMENT ON COLUMN regular_sale_composition_view_work.regular_order_count_interval IS '定期回次間隔';
COMMENT ON COLUMN regular_sale_composition_view_work.retail_price IS '販売価格';
COMMENT ON COLUMN regular_sale_composition_view_work.regular_sale_commodity_point IS '定期便商品構成ポイント';
COMMENT ON COLUMN regular_sale_composition_view_work.display_order IS '表示順';
COMMENT ON COLUMN regular_sale_composition_view_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN regular_sale_composition_view_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN regular_sale_composition_view_work.created_datetime IS '作成日時';
COMMENT ON COLUMN regular_sale_composition_view_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN regular_sale_composition_view_work.updated_datetime IS '更新日時';
COMMENT ON COLUMN regular_sale_composition_view_work.delete_flg IS '削除フラグ';

