CREATE TABLE regular_sale_cont_detail_view_work (
    regular_contract_no VARCHAR(14) NOT NULL,
    regular_contract_detail_no NUMERIC(3,0) NOT NULL,
    shop_code VARCHAR(16) NOT NULL,
    sku_code VARCHAR(24) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    contract_amount NUMERIC(8,0) NOT NULL,
    commodity_name VARCHAR(100) NOT NULL,
    commodity_subcategory_code VARCHAR(16),
    commodity_subcategory_code_name VARCHAR(50),
    baitai_code VARCHAR(10) NOT NULL,
    regular_cycle_delivery_kbn VARCHAR(1) NOT NULL,
    regular_cycle_kijun_date TIMESTAMP(0),
    regular_kind VARCHAR(1) NOT NULL,
    regular_cycle_day_int VARCHAR(2),
    regular_cycle_day NUMERIC(2,0),
    regular_cycle_mon_interval NUMERIC(2,0),
    regular_cycle_mon_interval_day NUMERIC(2,0),
    regular_cycle_week_num NUMERIC(2,0),
    regular_cycle_week_kbn VARCHAR(1),
    regular_cycle_week_mon VARCHAR(1),
    regular_cycle_week_tue VARCHAR(1),
    regular_cycle_week_wed VARCHAR(1),
    regular_cycle_week_thu VARCHAR(1),
    regular_cycle_week_fri VARCHAR(1),
    regular_cycle_week_sat VARCHAR(1),
    regular_cycle_week_sun VARCHAR(1),
    regular_cycle_week_hol VARCHAR(1),
    cycle_disp_name VARCHAR(1000),
    next_shipping_plan_date TIMESTAMP(0),
    next_shipping_date TIMESTAMP(0) NOT NULL,
    next_delivery_plan_date TIMESTAMP(0),
    next_delivery_date TIMESTAMP(0),
    lastest_delivery_date TIMESTAMP(0),
    regular_kaiji NUMERIC(5,0) NOT NULL,
    shipped_regular_count NUMERIC(3,0) NOT NULL,
    regular_sale_stop_from NUMERIC(5,0),
    regular_sale_stop_to NUMERIC(5,0),
    hasso_souko_cd VARCHAR(6),
    shipping_area VARCHAR(2),
    regular_check_memo VARCHAR(1000),
    regular_memo_hold_flg NUMERIC(1,0) NOT NULL,
    souko_shiji VARCHAR(40),
    next_regular_sale_stop_status VARCHAR(1) NOT NULL,
    regular_stop_reason_kbn VARCHAR(2),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    PRIMARY KEY (regular_contract_no, regular_contract_detail_no)
);
COMMENT ON TABLE regular_sale_cont_detail_view_work IS '定期契約明細ワーク';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_contract_no IS '定期契約番号';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_contract_detail_no IS '定期契約明細番号';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.sku_code IS 'SKUコード';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.commodity_code IS '商品コード';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.contract_amount IS '契約商品数';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.commodity_name IS '商品名称';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.commodity_subcategory_code IS '商品中分類';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.commodity_subcategory_code_name IS '商品中分類名称';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.baitai_code IS '媒体コード';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_delivery_kbn IS '定期サイクル発送日指定区分';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_kijun_date IS '定期サイクル基準日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_kind IS '定期サイクル種別';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_day_int IS '定期サイクル日付間隔';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_day IS '定期サイクル日付';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_mon_interval IS '定期サイクル月間隔';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_mon_interval_day IS '定期サイクル月間隔日付';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_num IS '定期サイクル第○週';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_kbn IS '定期サイクル○曜日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_mon IS '定期サイクル届指定月';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_tue IS '定期サイクル届指定火';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_wed IS '定期サイクル届指定水';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_thu IS '定期サイクル届指定木';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_fri IS '定期サイクル届指定金';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_sat IS '定期サイクル届指定土';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_sun IS '定期サイクル届指定日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_cycle_week_hol IS '定期サイクル届指定祝日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.cycle_disp_name IS 'サイクル表示名';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.next_shipping_plan_date IS '次回発送予定日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.next_shipping_date IS '次回発送日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.next_delivery_plan_date IS '次回お届け予定日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.next_delivery_date IS '次回お届け日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.lastest_delivery_date IS '最新お届け日';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_kaiji IS '定期回次';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.shipped_regular_count IS '発送済定期回数';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_sale_stop_from IS '定期休止回次FROM';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_sale_stop_to IS '定期休止回次TO';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.hasso_souko_cd IS '発送倉庫コード';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.shipping_area IS '発送場所';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_check_memo IS '定期チェックメモ';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_memo_hold_flg IS '定期メモ保留フラグ';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.souko_shiji IS '倉庫指示';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.next_regular_sale_stop_status IS '次回定期休止ステータス';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.regular_stop_reason_kbn IS '定期休止理由区分';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.created_datetime IS '作成日時';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.updated_datetime IS '更新日時';
COMMENT ON COLUMN regular_sale_cont_detail_view_work.delete_flg IS '削除フラグ';

