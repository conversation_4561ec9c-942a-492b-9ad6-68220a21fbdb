CREATE TABLE set_commodity_composition_view (
    shop_code VARCHAR(16) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    child_commodity_code VARCHAR(16) NOT NULL,
    composition_quantity NUMERIC(2,0) NOT NULL,
    composition_order NUMERIC(2,0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_code, commodity_code, child_commodity_code, composition_order)
);
COMMENT ON TABLE set_commodity_composition_view IS 'セット商品構成';
COMMENT ON COLUMN set_commodity_composition_view.shop_code IS 'ショップコード';
COMMENT ON COLUMN set_commodity_composition_view.commodity_code IS '商品コード';
COMMENT ON COLUMN set_commodity_composition_view.child_commodity_code IS '子商品コード';
COMMENT ON COLUMN set_commodity_composition_view.composition_quantity IS '構成数量';
COMMENT ON COLUMN set_commodity_composition_view.composition_order IS '構成順序';
COMMENT ON COLUMN set_commodity_composition_view.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN set_commodity_composition_view.created_user IS '作成ユーザ';
COMMENT ON COLUMN set_commodity_composition_view.created_datetime IS '作成日時';
COMMENT ON COLUMN set_commodity_composition_view.updated_user IS '更新ユーザ';
COMMENT ON COLUMN set_commodity_composition_view.updated_datetime IS '更新日時';
COMMENT ON COLUMN set_commodity_composition_view.delete_flg IS '削除フラグ';
COMMENT ON COLUMN set_commodity_composition_view.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN set_commodity_composition_view.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN set_commodity_composition_view.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN set_commodity_composition_view.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN set_commodity_composition_view.d_version IS 'デ連バージョン';

