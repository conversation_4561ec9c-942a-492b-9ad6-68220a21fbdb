CREATE TABLE set_commodity_composition_view_work (
    shop_code VARCHAR(16) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    child_commodity_code VARCHAR(16) NOT NULL,
    composition_quantity NUMERIC(2,0) NOT NULL,
    composition_order NUMERIC(2,0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    delete_flg NUMERIC(1) NOT NULL Default 0,
    PRIMARY KEY (shop_code, commodity_code, child_commodity_code, composition_order)
);
COMMENT ON TABLE set_commodity_composition_view_work IS 'セット商品構成ワーク';
COMMENT ON COLUMN set_commodity_composition_view_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN set_commodity_composition_view_work.commodity_code IS '商品コード';
COMMENT ON COLUMN set_commodity_composition_view_work.child_commodity_code IS '子商品コード';
COMMENT ON COLUMN set_commodity_composition_view_work.composition_quantity IS '構成数量';
COMMENT ON COLUMN set_commodity_composition_view_work.composition_order IS '構成順序';
COMMENT ON COLUMN set_commodity_composition_view_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN set_commodity_composition_view_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN set_commodity_composition_view_work.created_datetime IS '作成日時';
COMMENT ON COLUMN set_commodity_composition_view_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN set_commodity_composition_view_work.updated_datetime IS '更新日時';
COMMENT ON COLUMN set_commodity_composition_view_work.delete_flg IS '削除フラグ';

