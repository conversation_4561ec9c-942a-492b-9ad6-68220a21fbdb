CREATE TABLE sync_timestamp (
    job_schedule_id VARCHAR(50) NOT NULL,
    file_name VARCHAR(200) NOT NULL,
    sync_datetime TIMESTAMP NOT NULL,
    sync_datetime_temp TIMESTAMP,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (job_schedule_id, file_name)
);
COMMENT ON TABLE sync_timestamp IS '同期済タイムスタンプ（差分データ管理用）';
COMMENT ON COLUMN sync_timestamp.job_schedule_id IS 'ジョブスケジュールID';
COMMENT ON COLUMN sync_timestamp.file_name IS 'ファイル名';
COMMENT ON COLUMN sync_timestamp.sync_datetime IS '前回同期済タイムスタンプ';
COMMENT ON COLUMN sync_timestamp.sync_datetime_temp IS '前回同期済タイムスタンプ（一時格納用）';
COMMENT ON COLUMN sync_timestamp.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN sync_timestamp.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN sync_timestamp.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN sync_timestamp.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN sync_timestamp.d_version IS 'デ連バージョン';

