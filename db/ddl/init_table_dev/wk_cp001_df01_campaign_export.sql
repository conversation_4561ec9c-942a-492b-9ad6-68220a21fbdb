CREATE TABLE wk_cp001_df01_campaign_export (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_exist_flg VARCHAR(1) NOT NULL,
    campaign_customer_flg VARCHAR(1) NOT NULL,
    PRIMARY KEY (campaign_instructions_code)
);
COMMENT ON TABLE wk_cp001_df01_campaign_export IS 'キャンペーン連携対象抽出ワーク';
COMMENT ON COLUMN wk_cp001_df01_campaign_export.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_campaign_export.campaign_exist_flg IS 'キャンペーン存在フラグ';
COMMENT ON COLUMN wk_cp001_df01_campaign_export.campaign_customer_flg IS 'キャンペーン顧客フラグ';

