CREATE TABLE wk_cp001_df01_campaign_promotion_assignment_main (
    promotion_id VARCHAR(26) NOT NULL,
    campaign_instructions_code VARCHAR(16) NOT NULL,
    split_num NUMERIC(2,0),
    PRIMARY KEY (promotion_id, campaign_instructions_code)
);
COMMENT ON TABLE wk_cp001_df01_campaign_promotion_assignment_main IS 'プロモーション割り当て出力ワーク';
COMMENT ON COLUMN wk_cp001_df01_campaign_promotion_assignment_main.promotion_id IS 'プロモーション番号';
COMMENT ON COLUMN wk_cp001_df01_campaign_promotion_assignment_main.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_campaign_promotion_assignment_main.split_num IS '分割単位';

