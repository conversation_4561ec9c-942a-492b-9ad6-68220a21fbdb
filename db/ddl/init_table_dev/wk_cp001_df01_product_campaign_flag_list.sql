CREATE TABLE wk_cp001_df01_product_campaign_flag_list (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    preferential_product_flg VARCHAR(1),
    single_product_flg VARCHAR(1),
    PRIMARY KEY (campaign_instructions_code)
);
COMMENT ON TABLE wk_cp001_df01_product_campaign_flag_list IS 'キャンペーン連携対象商品属性抽出ワーク';
COMMENT ON COLUMN wk_cp001_df01_product_campaign_flag_list.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_product_campaign_flag_list.preferential_product_flg IS '優待商品フラグ';
COMMENT ON COLUMN wk_cp001_df01_product_campaign_flag_list.single_product_flg IS '単商品フラグ';

