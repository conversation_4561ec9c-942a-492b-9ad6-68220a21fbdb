CREATE TABLE wk_cp001_df01_static_customer_groups_main (
    group_id VARCHAR(25) NOT NULL,
    customer_no VARCHAR(12) NOT NULL,
    split_num NUMERIC(2,0),
    PRIMARY KEY (group_id, customer_no)
);
COMMENT ON TABLE wk_cp001_df01_static_customer_groups_main IS '静的顧客グループ出力ワーク';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_main.group_id IS '顧客グループID';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_main.customer_no IS '顧客番号';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_main.split_num IS '分割単位';

