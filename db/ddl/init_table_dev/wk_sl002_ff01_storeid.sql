CREATE TABLE wk_sl002_ff01_storeid (
    store_id VARCHAR(16) NOT NULL,
    allocation_timestamp VARCHAR(29) NOT NULL,
    split_num NUMERIC(2,0),
    PRIMARY KEY (store_id)
);
COMMENT ON TABLE wk_sl002_ff01_storeid IS '店舗在庫(差分)出力ワーク';
COMMENT ON COLUMN wk_sl002_ff01_storeid.store_id IS '店舗コード';
COMMENT ON COLUMN wk_sl002_ff01_storeid.allocation_timestamp IS '連携時の時刻';
COMMENT ON COLUMN wk_sl002_ff01_storeid.split_num IS '分割単位';

