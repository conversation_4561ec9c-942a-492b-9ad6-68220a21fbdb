CREATE TABLE wk_st002_df01_inventory (
    commodity_code VARCHAR(16) NOT NULL,
    stock_quantity NUMERIC(8,0) NOT NULL,
    stock_arrival_date VARCHAR(10),
    arrival_quantity NUMERIC(8,0) NOT NULL,
    allocation NUMERIC(9,0) NOT NULL,
    allocation_timestamp VARCHAR(29) NOT NULL,
    split_num NUMERIC(2,0),
    PRIMARY KEY (commodity_code)
);
COMMENT ON TABLE wk_st002_df01_inventory IS '通販在庫(差分)出力ワーク';
COMMENT ON COLUMN wk_st002_df01_inventory.commodity_code IS '通販商品番号';
COMMENT ON COLUMN wk_st002_df01_inventory.stock_quantity IS '在庫数量';
COMMENT ON COLUMN wk_st002_df01_inventory.stock_arrival_date IS '入荷日';
COMMENT ON COLUMN wk_st002_df01_inventory.arrival_quantity IS '予約引当可能数';
COMMENT ON COLUMN wk_st002_df01_inventory.allocation IS '在庫数量+予約引当可能数';
COMMENT ON COLUMN wk_st002_df01_inventory.allocation_timestamp IS '連携時の時刻';
COMMENT ON COLUMN wk_st002_df01_inventory.split_num IS '分割単位';

