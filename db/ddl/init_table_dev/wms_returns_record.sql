CREATE TABLE wms_returns_record (
    KANRI_NO VARCHAR(13) NOT NULL,
    NYUKO_YMD VARCHAR(10),
    JUCHU_NO VARCHAR(15),
    HINBAN_CD VARCHAR(25),
    HENPIN_QT NUMERIC(15),
    LETTER_FLG VARCHAR(1),
    HENPIN_REASON_KBN VARCHAR(2),
    RYOHIN_KBN VARCHAR(2),
    HENPIN_BIKO VARCHAR(50),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL
);
COMMENT ON TABLE wms_returns_record IS 'WMS返品実績';
COMMENT ON COLUMN wms_returns_record.KANRI_NO IS '管理番号';
COMMENT ON COLUMN wms_returns_record.NYUKO_YMD IS '入庫日';
COMMENT ON COLUMN wms_returns_record.JUCHU_NO IS '受注番号';
COMMENT ON COLUMN wms_returns_record.HINBAN_CD IS '品番';
COMMENT ON COLUMN wms_returns_record.HENPIN_QT IS '数量';
COMMENT ON COLUMN wms_returns_record.LETTER_FLG IS '手紙有無';
COMMENT ON COLUMN wms_returns_record.HENPIN_REASON_KBN IS '返品理由';
COMMENT ON COLUMN wms_returns_record.RYOHIN_KBN IS '良品区分';
COMMENT ON COLUMN wms_returns_record.HENPIN_BIKO IS '備考欄';
COMMENT ON COLUMN wms_returns_record.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN wms_returns_record.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN wms_returns_record.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN wms_returns_record.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN wms_returns_record.d_version IS 'デ連バージョン';

