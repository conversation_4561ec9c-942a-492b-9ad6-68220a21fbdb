# AWS Glue用パッケージ作成手順

このドキュメントでは、AWS Glue向けのWheelファイル（Pythonホイール）と依存パッケージの作成・デプロイ手順について説明します。

## 概要

AWS Glueジョブ用のパッケージング方法として、このプロジェクトでは以下のアプローチを採用しています：

1. **メインパッケージ（WHL）**: プロジェクトのメインコードをPythonホイール（.whl）ファイルにパッケージ化
   - 依存関係処理モジュール（glue_deps_handler.py）も含まれています
2. **依存ライブラリ（deps.zip）**: サードパーティの依存ライブラリを単一のZIPファイルにパッケージ化
3. **Glueジョブメインスクリプト**: 各Glueジョブの実行ファイル（glue_job_*.py）をS3にデプロイ

## パッケージ作成・デプロイ手順

### 0. 環境の前提条件

パッケージ作成・デプロイスクリプトを実行するには、以下が必要です：

- **Docker**: ビルドおよびパッケージング処理にDockerを使用します
  - Dockerがインストールされていない場合は、「ローカル開発環境構築手順_API&バッチ.xlsx」の「13.Dockerのインストール手順」シートを参照してください
  - WSL環境でのDockerインストール方法については、上記ドキュメントに記載のシートを参照してください

**Docker使用に関する注意点:**
- 現在の実装では、毎回新しいDockerイメージ（`glue3-builder`）をビルドします
- これにより一貫性は保たれますが、ビルド時間が長くなる場合があります
- 依存関係に変更がない場合は `--skip-deps-build` オプションの使用を推奨します

### 1. 環境の準備

まず、環境を整えるために必要なパッケージをインストールし、依存パッケージを同期します：

```bash
# job ディレクトリに移動
cd job

# Python buildパッケージをインストール（WHLファイル作成に必要）
uv pip install build

# 依存パッケージを同期（不具合防止のため重要）
uv sync --all-groups --reinstall
```

### 2. パッケージ作成・デプロイ

パッケージ作成・デプロイ前に、必ず適切なブランチ（開発環境ならdevelop、検証環境ならrelease）をチェックアウトし、リモートリポジトリとの差分がないことを確認してください。

#### 2.1 基本的な使用方法

メインパッケージ（WHL）、依存ライブラリ（deps.zip）およびGlueジョブメインスクリプト（glue_job_*.py）を自動的に作成してS3にアップロードするには、以下のスクリプトを使用します：

```bash
# job ディレクトリに移動
cd job

# 開発環境用
./build_and_deploy_dev.sh

# または検証環境用
./build_and_deploy_stg.sh
```

#### 2.2 詳細オプション（build_and_deploy.sh）

環境別スクリプトは内部で共通の `build_and_deploy.sh` を呼び出します。直接実行する場合は以下のオプションが利用できます：

##### **必須パラメータ**
```bash
./build_and_deploy.sh --bucket BUCKET_NAME --prefix PREFIX_PATH
```

##### **主要オプション**

| オプション | 説明 | デフォルト |
|-----------|------|-----------|
| `--clean` | ビルド前にキャッシュと一時ファイルを削除 | false |
| `--skip-deps-build` | deps.zipのビルドをスキップ（既存ファイル使用） | false |
| `--non-interactive` | 非対話モード（確認プロンプトをスキップ） | false |
| `--dry-run` | 実際のS3アップロードを行わず内容確認のみ | false |

##### **デプロイ制御オプション**

| オプション | 説明 | デフォルト |
|-----------|------|-----------|
| `--deploy-deps true/false` | deps.zipをデプロイするかどうか | true |
| `--deploy-source true/false` | WHLファイルをデプロイするかどうか | true |
| `--deploy-scripts true/false` | Glueジョブスクリプトをデプロイするかどうか | true |

##### **使用例**

```bash
# 依存関係のビルドをスキップして、WHLファイルのみデプロイ
./build_and_deploy.sh --bucket my-bucket --prefix scripts \
  --skip-deps-build --deploy-deps false --deploy-source true --deploy-scripts false

# ドライランモードで内容確認（S3アップロードなし）
./build_and_deploy.sh --bucket my-bucket --prefix scripts --dry-run --non-interactive

# 非対話モードで全てデプロイ
./build_and_deploy.sh --bucket my-bucket --prefix scripts --non-interactive

# クリーンビルドで全て再作成
./build_and_deploy.sh --bucket my-bucket --prefix scripts --clean
```

#### 2.3 処理内容

スクリプトは以下の処理を順次実行します：

1. **前提条件チェック**: Docker環境、Git状態、ブランチ確認
2. **アプリケーションWHLの作成**: プロジェクトコードをWheelファイルにビルド
3. **deps.zipの作成**: 必要な依存ライブラリを単一のZIPファイルにまとめる（manylinux2014互換）
   - `--skip-deps-build`指定時は既存ファイルを使用またはS3からダウンロード
4. **S3へのアップロード**: 以下のファイルをS3にアップロード
   - WHLファイル（アプリケーションコード）
   - deps.zip（依存ライブラリ、ビルドされた場合のみ）
   - glue_job_*.py（各Glueジョブのメイン起動スクリプト）

#### 2.4 実行環境による挙動の違い

- **ローカル環境 (WSLなど)**: 環境変数 (`http_proxy`, `https_proxy`, `no_proxy`) で設定されたプロキシ情報がDockerビルド・実行時に使用されます
- **CodeBuild環境**: プロキシ設定は自動的にスキップされ、非対話モードが自動有効化されます
- **非対話モード**: 確認プロンプトをスキップし、デプロイ制御オプションに従って自動実行されます

#### 2.5 効率的な使用方法

**開発時の推奨パターン:**
```bash
# 初回または依存関係変更時
./build_and_deploy_dev.sh

# コード変更のみの場合（deps.zipスキップで高速化）
./build_and_deploy.sh --bucket aws-glue-assets-886436956581-ap-northeast-1 --prefix scripts \
  --skip-deps-build --deploy-deps false --non-interactive
```

**本番デプロイ時の推奨パターン:**
```bash
# 事前確認（ドライラン）
./build_and_deploy_stg.sh --dry-run --non-interactive

# 実際のデプロイ
./build_and_deploy_stg.sh --non-interactive
```

### 3. AWS Glueでの使用方法

CloudFormationでGlueジョブを定義する際、以下のパラメータを設定します：

開発環境の例）
```yaml
DefaultArguments:
  "--extra-py-files": "s3://aws-glue-assets-886436956581-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-886436956581-ap-northeast-1/scripts/deps.zip"
```

各Glueジョブスクリプトには、先頭に以下のコードを追加する必要があります：

```python
# 依存関係のセットアップ（スクリプト先頭で実行）
from source.glue_deps_handler import setup_glue_dependencies
setup_glue_dependencies()

# 以降、通常通りモジュールをインポート
import boto3
# ...残りのコード
```

注意点：
- build_and_deploy.sh実行後に表示される`--extra-py-files`パラメータ文字列をそのまま使用できます
- 各ジョブスクリプトの先頭で依存関係セットアップコードを追加することが重要です
- メインスクリプト（glue_job_*.py）は自動的にS3の`/scripts/source/`ディレクトリにデプロイされます

### 4. deps.zip方式のメリット

旧方式（個別WHLファイル）と比較した、deps.zip方式の主なメリットは以下の通りです：

1. **パラメータの簡素化**: 全依存ライブラリを単一のパスで指定できる
2. **バイナリ互換性の向上**: manylinux2014形式を使用しGlue環境との互換性を確保
3. **データファイルアクセスの改善**: botocoreなどのデータファイルに依存するパッケージが正常に動作
4. **実行環境の安定化**: deps.zipを展開するため、ZIPファイル内部へのアクセス問題を解消

## 5. トラブルシューティング

### 5.1 よくある問題と解決方法

#### Docker関連の問題

**問題**: `docker: command not found`
```bash
解決方法: Dockerをインストールしてください
# WSL環境の場合
sudo apt update && sudo apt install docker.io
sudo service docker start
```

**問題**: `Docker Daemon is not running`
```bash
解決方法: Docker Daemonを起動してください
# WSL環境の場合
sudo service docker start

# または
sudo systemctl start docker
```

**問題**: Dockerビルドが遅い
```bash
解決方法: deps.zipのビルドをスキップしてください
./build_and_deploy.sh --bucket BUCKET --prefix PREFIX --skip-deps-build
```

#### ビルド関連の問題

**問題**: `python-build module not found`
```bash
解決方法: 必要なパッケージをインストールしてください
uv pip install build wheel setuptools
```

**問題**: `deps.zip not found`
```bash
解決方法1: 依存関係をビルドしてください
./build_and_deploy.sh --bucket BUCKET --prefix PREFIX

解決方法2: S3から既存のdeps.zipをダウンロードしてください
# --skip-deps-buildオプション使用時に自動実行されます
```

#### 権限関連の問題

**問題**: S3アップロードに失敗
```bash
解決方法: AWS認証情報を確認してください
aws configure list
aws sts get-caller-identity
```

### 5.2 デバッグ方法

#### ドライランモードでの確認
```bash
# 実際のアップロードを行わずに処理内容を確認
./build_and_deploy.sh --bucket BUCKET --prefix PREFIX --dry-run --non-interactive
```

#### 段階的な実行
```bash
# 1. WHLファイルのみビルド・デプロイ
./build_and_deploy.sh --bucket BUCKET --prefix PREFIX \
  --skip-deps-build --deploy-deps false --deploy-scripts false

# 2. deps.zipのみビルド・デプロイ
./build_and_deploy.sh --bucket BUCKET --prefix PREFIX \
  --deploy-source false --deploy-scripts false

# 3. スクリプトのみデプロイ
./build_and_deploy.sh --bucket BUCKET --prefix PREFIX \
  --skip-deps-build --deploy-deps false --deploy-source false
```

## 6. 今後の予定

- CI/CDパイプラインとの統合
- 本番環境用対応
- Dockerイメージキャッシュ機能の追加検討

Todo: 本番環境への対応方法については、今後検討を進める予定です。
