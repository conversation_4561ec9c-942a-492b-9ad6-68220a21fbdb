common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "promotions"
  indent: 2

etl:
  parameters:
    sync_timestamp:
      type: "datetime"
      description: "同期基準日時"
      required: true
    diff_base_timestamp:
      type: "datetime"
      description: "差分基準時刻"
      required: true
    split_num:
      type: "string"
      description: "分割単位"
      required: true

  # 削除されていないキャンペーンのメインクエリ
  campaign_exist_query: |
    SELECT
      campaign_instructions_code
      ,campaign_instructions_name
      ,enabled_flag
      ,campaign_start_date
      ,campaign_end_date
      ,customer_groups_group_id
      ,campaign_applied_scope
    FROM wk_cp001_df01_campaign_main
    WHERE split_num = :split_num
      AND campaign_exist_flg = '1'
    ORDER BY campaign_instructions_code

  # 削除されたキャンペーンのメインクエリ
  campaign_deleted_query: |
    SELECT
      campaign_instructions_code
    FROM wk_cp001_df01_campaign_main
    WHERE split_num = :split_num
      AND campaign_exist_flg = '0'
    ORDER BY campaign_instructions_code

  # 削除されていないプロモーションのメインクエリ
  promotion_exist_query: |
    SELECT
      campaign_instructions_code
      ,promotion_id
      ,name
      ,promotion_type
      ,present_product_code
      ,first_order_only
      ,discount_rate
      ,oneshot_order_limit
      ,campaign_quantity_limit
      ,campaign_end_date
      ,preferential_product_flg
      ,baitai_code
      ,campaign_priority
      ,discount_amount
      ,discount_retail_price
      ,single_product_flg
      ,discount_amount_amount
      ,discount_rate_amount
      ,pricebook_id
    FROM wk_cp001_df01_promotion_main
    WHERE split_num = :split_num
      AND campaign_exist_flg = '1'
    ORDER BY campaign_instructions_code, promotion_id

  # 削除されたプロモーションのメインクエリ
  promotion_deleted_query: |
    SELECT
      campaign_instructions_code
      ,promotion_id
    FROM wk_cp001_df01_promotion_main
    WHERE split_num = :split_num
      AND campaign_exist_flg = '0'
    ORDER BY campaign_instructions_code, promotion_id

  # キャンペーン設定条件
  campaign_order_query: |
    --CSV出力によって取得

  # 値引きプロモーションルール(単商品)
  promotion_rule_single_product_query: |
    --CSV出力によって取得

  # 値引きプロモーションルール(複数商品)
  promotion_rule_multiple_product_query: |
    --CSV出力によって取得

  # プロモーション割り当て情報クエリ
  promotion_assignment_query: |
    SELECT 
      promotion_id
      ,campaign_instructions_code
    FROM wk_cp001_df01_campaign_promotion_assignment_main
    WHERE split_num = :split_num
    ORDER BY campaign_instructions_code, promotion_id

  mappings:
    # ルート要素：promotions
    - target_element: "promotions"
      attributes:
        xmlns: "http://www.demandware.com/xml/impex/promotion/2008-01-31"
      child_elements:
        # 1-1.削除されたキャンペーン要素
        - target_element: "campaign"
          query: "campaign_deleted_query"
          attributes:
            campaign-id: "{campaign_instructions_code}"
            mode: "delete"
        # 1-2. 削除されていないキャンペーン要素
        - target_element: "campaign"
          query: "campaign_exist_query"
          attributes:
            campaign-id: "{campaign_instructions_code}"
          child_elements:
            - target_element: "description"
              source_column: "campaign_instructions_name"
              type: "string"
            - target_element: "enabled-flag"
              source_column: "enabled_flag"
              type: "string"
            - target_element: "campaign-scope"
              child_elements:
                - target_element: "applicable-online"
            - target_element: "start-date"
              source_column: "campaign_start_date"
              type: "datetime"
              format: "%Y-%m-%dT%H:%M:%S.000+09:00"
            - target_element: "end-date"
              source_column: "campaign_end_date"
              type: "datetime"
              format: "%Y-%m-%dT%H:%M:%S.000+09:00"
            - target_element: "customer-groups"
              attributes:
                match-mode: "any"
              child_elements:
                - target_element: "customer-group"
                  attributes:
                    group-id: "{customer_groups_group_id}"
            # キャンペーン適用範囲をカスタム属性に追加
            - target_element: "custom-attributes"
              child_elements:
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txCampaignScopeApplication"
                  source_column: "campaign_applied_scope"
                  type: "string"

        # 2-1. プロモーション要素
        - target_element: "promotion"
          query: "promotion_deleted_query"
          attributes:
            promotion-id: "{promotion_id}"
            mode: "delete"
        # 2-2. プロモーション要素
        - target_element: "promotion"
          query: "promotion_exist_query"
          attributes:
            promotion-id: "{promotion_id}"
          child_elements:
            - target_element: "enabled-flag"
              value: "true"
              type: "boolean"
            - target_element: "archived-flag"
              value: "false"
              type: "boolean"
            - target_element: "searchable-flag"
              value: "false"
              type: "boolean"
            - target_element: "refinable-flag"
              value: "false"
              type: "boolean"
            - target_element: "prevent-requalifying-flag"
              value: "false"
              type: "boolean"
            - target_element: "prorate-across-eligible-items-flag"
              value: "false"
              type: "boolean"
            - target_element: "exclusivity"
              value: "no"
              type: "string"
            - target_element: "name"
              source_column: "name"
              type: "string"

            # カスタム属性
            - target_element: "custom-attributes"
              child_elements:
                # プロモーション種別
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txPromotionType"
                  source_column: "promotion_type"
                  type: "string"

                # プレゼント_商品コード
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txPresentProductCode"
                  source_column: "present_product_code"
                  type: "string"

                # キャンペーン設定条件
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txCampaignConditionGroup"
                  child_elements:
                    - target_element: "value"
                      sub_query: "campaign_order_query"
                      source_column: "campaign_condition_group"
                      type: "string"

                # 定期初回値引プロモーションフラグ
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txIsFirstOrderOnlySubscription"
                  source_column: "first_order_only"
                  type: "string"

                # OFF率
                - target_element: "custom-attribute"
                  condition: "promotion_type == '03'" # 割引の場合のみ
                  attributes:
                    attribute-id: "txDiscountRateForScreenDisplay"
                  source_column: "discount_rate"
                  type: "number"

                # 上限数量（1注文）
                - target_element: "custom-attribute"
                  condition: "oneshot_order_limit is not None" # 値がある場合のみ表示
                  attributes:
                    attribute-id: "txMaximumQuantityPerOrder"
                  source_column: "oneshot_order_limit"
                  type: "number"

                # 上限数量（同一CP累積）
                - target_element: "custom-attribute"
                  condition: "campaign_quantity_limit is not None" # 値がある場合のみ表示
                  attributes:
                    attribute-id: "txUpperLimitQuantityPerSameCampaignAccumulation"
                  source_column: "campaign_quantity_limit"
                  type: "number"

                # キャンペーン終了日
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txCampaignEndDateForScreenDisplay"
                  source_column: "campaign_end_date"
                  type: "datetime"
                  format: "%Y-%m-%dT%H:%M:%S.000+0900"

                # 優待プロモーションフラグ
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txPreferentialPromotionFlag"
                  source_column: "preferential_product_flg"
                  type: "string"

                # 媒体コード
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txMediaCode"
                  source_column: "baitai_code"
                  type: "string"

                # 優先順位
                - target_element: "custom-attribute"
                  attributes:
                    attribute-id: "txPriority"
                  source_column: "campaign_priority"
                  type: "number"

            # 商品プロモーションルール
            - target_element: "product-promotion-rule"
              condition: "promotion_type == '02' and discount_amount is not None"
              child_elements:
                - target_element: "discounted-products"
                  condition: "True"
                  child_elements:
                    - target_element: "included-products"
                      condition: "True"
                      child_elements:
                        - target_element: "condition-group"
                          condition: "True"
                          child_elements:
                            - target_element: "product-id-condition" # 商品ID条件
                              condition: "True"
                              attributes:
                                operator: "is equal"
                              child_elements:
                                - target_element: "product-id"
                                  condition: "single_product_flg == '1'"
                                  sub_query: "promotion_rule_single_product_query"
                                  source_column: "joken"
                                - target_element: "product-id"
                                  condition: "single_product_flg is None or single_product_flg== '0'"
                                  sub_query: "promotion_rule_multiple_product_query"
                                  source_column: "commodity_code"
                - target_element: "simple-discount"
                  condition: "True"
                  child_elements:
                    - target_element: "amount" # 値引額
                      source_column: "discount_amount_amount"
                      type: "string"

            - target_element: "product-promotion-rule"
              condition: "promotion_type == '03' and discount_retail_price is None and discount_rate is not None"
              child_elements:
                - target_element: "discounted-products"
                  condition: "True"
                  child_elements:
                    - target_element: "included-products"
                      condition: "True"
                      child_elements:
                        - target_element: "condition-group"
                          condition: "True"
                          child_elements:
                            - target_element: "product-id-condition" # 商品ID条件
                              condition: "True"
                              attributes:
                                operator: "is equal"
                              child_elements:
                                - target_element: "product-id"
                                  condition: "single_product_flg == '1'"
                                  sub_query: "promotion_rule_single_product_query"
                                  source_column: "joken"
                                - target_element: "product-id"
                                  condition: "single_product_flg is None or single_product_flg== '0'"
                                  sub_query: "promotion_rule_multiple_product_query"
                                  source_column: "commodity_code"
                - target_element: "simple-discount"
                  condition: "True"
                  child_elements:
                    - target_element: "percentage" # 割引率
                      source_column: "discount_rate_amount"
                      type: "string"

            - target_element: "product-promotion-rule"
              condition: "promotion_type == '03' and discount_retail_price is not None"
              child_elements:
                - target_element: "discounted-products"
                  condition: "True"
                  child_elements:
                    - target_element: "included-products"
                      condition: "True"
                      child_elements:
                        - target_element: "condition-group"
                          condition: "True"
                          child_elements:
                            - target_element: "product-id-condition" # 商品ID条件
                              condition: "True"
                              attributes:
                                operator: "is equal"
                              child_elements:
                                - target_element: "product-id"
                                  condition: "single_product_flg == '1'"
                                  sub_query: "promotion_rule_single_product_query"
                                  source_column: "joken"
                                - target_element: "product-id"
                                  condition: "single_product_flg is None or single_product_flg== '0'"
                                  sub_query: "promotion_rule_multiple_product_query"
                                  source_column: "commodity_code"
                - target_element: "simple-discount"
                  condition: "True"
                  child_elements:
                    - target_element: "price-book-price"
                      condition: "True"
                      child_elements:
                        - target_element: "pricebook-id"
                          source_column: "pricebook_id"
                          type: "string"

        # 3. プロモーション割り当て情報
        - target_element: "promotion-campaign-assignment"
          query: "promotion_assignment_query"
          attributes:
            promotion-id: "{promotion_id}"
            campaign-id: "{campaign_instructions_code}"
          child_elements:
            - target_element: "qualifiers"
              attributes:
                match-mode: "any"
              child_elements:
                - target_element: "customer-groups"
                - target_element: "source-codes"
                - target_element: "coupons"

  transformations:
    # 日時型変換（UTC形式に変更）
    - column: "campaign_start_date"
      type: "datetime_convert"
      format: "%Y-%m-%dT%H:%M:%S.000+09:00"
      query: campaign_exist_query

    - column: "campaign_end_date"
      type: "datetime_convert"
      format: "%Y-%m-%dT%H:%M:%S.000+09:00"
      query: campaign_exist_query

    - column: "campaign_end_date"
      type: "datetime_convert"
      format: "%Y-%m-%dT%H:%M:%S.000+0900"
      query: promotion_exist_query
