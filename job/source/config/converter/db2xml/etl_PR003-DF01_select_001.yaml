common:
  format: db2xml
  encoding: utf-8

output:
  root_element: catalog
  record_element: product
  indent: 2

etl:
  parameters:
    sync_timestamp:
      type: datetime
      description: 前回同期済時刻
      required: true
    diff_base_timestamp:
      type: datetime
      description: 差分基準時刻
      required: true
    split_num:
      type: "string"
      description: "分割単位"
      required: true

  # メインクエリ: 商品基本情報の取得
  product_query: |
    --ワークテーブルのデータを抽出
    SELECT
      MAIL_ORDER_PRODUCT_CD,
      WEB_PRODUCT_NAME,
      tax_class_id,
      REPRESENTATIVE_PRODUCT_CD,
      REGISTRATION_NAME,
      CONTENTS,
      CORE_DEPARTMENT,
      DEP,
      JAN,
      PRODUCT_SEGMENT,
      BUSINESS_SEGMENT,
      PRODUCT_CAT,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>UP,
      PRODUCT_TYPE,
      WEB,
      ORDER_PER_ORDER_MAX,
      WEIGHT,
      COLOR_NAME,
      COLOR_CD,
      SIZE_NAME,
      SIZE_CD,
      MAIL_DELIVERY_FLG,
      NEKOPOSU_VOLUME_RATE,
      OUTSIDE_HOME_RECEIVE_SERVICE_FLG,
      OUTSIDE_HOME_VOLUME_RATE,
      COMPANY_SALES_BUY_FLG,
      CAST(CASE
          WHEN SET_COMPOSITION_FLG = '1' THEN 'true'
          ELSE 'false'
        END AS VARCHAR) AS SET_COMPOSITION_FLG,
      BEFORE_RENEWAL_PRODUCT_NO,
      SHAPE_NAME,
      SHAPE_CD,
      SEASON,
      USE_POINT_CNT,
      SALE_START_DATE,
      SALE_END_DATE,
      PRODUCT_SERIES,
      CAST(CASE
          WHEN SET_PRODUCT_FLG = '1' THEN 'true'
          ELSE 'false'
        END AS VARCHAR) AS SET_PRODUCT_FLG,
      CAST(CASE
          WHEN PREFERENTIAL_PRODUCT_FLG = '1' THEN 'true'
          ELSE 'false'
        END AS VARCHAR) AS PREFERENTIAL_PRODUCT_FLG,
      txInventoryProductID,
      BUTTOBI_SUBSC_BUNDLE_YN,
      CORE_PRODUCT_NAME
    FROM
      wk_pr003_df01_product_linkage_work
    WHERE split_num = :split_num
    ORDER BY MAIL_ORDER_PRODUCT_CD

  # セット商品構成情報を取得するサブクエリ
  bundled_products_query: |
    SELECT
      child_commodity_code AS product_id,
      SUM(composition_quantity) AS quantity,
      MAX(composition_order) AS composition_order
    FROM
      set_commodity_composition
    WHERE
      commodity_code = :mail_order_product_cd
    GROUP BY commodity_code, child_commodity_code
    ORDER BY composition_order

  # 定期商品構成情報を取得するサブクエリ
  regular_products_query: |
    SELECT
      regular_sale_commodity.sku_code AS product_id
    FROM
      regular_sale_base
      INNER JOIN regular_sale_commodity
      ON regular_sale_base.shop_code = regular_sale_commodity.shop_code
        AND regular_sale_base.regular_sale_code = regular_sale_commodity.regular_sale_code
    WHERE
      regular_sale_base.sku_code = :mail_order_product_cd

  mappings:
    - target_element: catalog
      attributes:
        xmlns: "http://www.demandware.com/xml/impex/catalog/2006-10-31"
        catalog-id: "dhc_catalog-ms"
      child_elements:
        - target_element: header
          child_elements:
            - target_element: image-settings
              child_elements:
                - target_element: internal-location
                  attributes:
                    base-path: "/"
                - target_element: view-types
                  child_elements:
                    - target_element: view-type
                      value: "large"
                    - target_element: view-type
                      value: "medium"
                    - target_element: view-type
                      value: "small"
                    - target_element: view-type
                      value: "swatch"
                    - target_element: view-type
                      value: "forPLP"
                - target_element: alt-pattern
                  value: "${productname}"
                - target_element: title-pattern
                  value: "${productname}"
        - target_element: product
          query: product_query
          attributes:
            product-id: "{mail_order_product_cd}"
          child_elements:
            - target_element: min-order-quantity
              value: "1"
            - target_element: step-quantity
              value: "1"
            - target_element: display-name
              attributes:
                xml:lang: x-default
              source_column: web_product_name
            - target_element: tax-class-id
              source_column: tax_class_id
              type: string
            - target_element: custom-attributes
              child_elements:
                - target_element: custom-attribute
                  source_column: representative_product_cd
                  attributes:
                    attribute-id: txRepresentativeProductID
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txEntryName
                  source_column: registration_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txCapacity
                  source_column: contents
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txCoreDept
                  source_column: core_department
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txPepartmentInCharge
                  source_column: dep
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txJanCode
                  source_column: jan
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSegment
                  source_column: product_segment
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txBussinessSegment
                  source_column: business_segment
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductClassification
                  source_column: product_cat
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductCategory
                  source_column: lgroup
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductClass
                  source_column: mgroup
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductSubclass
                  source_column: sgroup
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductDivision
                  source_column: dgroup
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductComp
                  source_column: product_type
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txWebSite
                  source_column: web
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txOrderLimit
                  source_column: order_per_order_max
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txWeight
                  source_column: weight
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txColorName
                  source_column: color_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txColor
                  source_column: color_cd
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSizeName
                  source_column: size_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSize
                  source_column: size_cd
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txMailDeliveryFlag
                  source_column: mail_delivery_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txVolume
                  source_column: nekoposu_volume_rate
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txTakeStorFlag
                  source_column: outside_home_receive_service_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txEntrustVolume
                  source_column: outside_home_volume_rate
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txEmployeeFlag
                  source_column: company_sales_buy_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSetConfigrationFlag
                  source_column: set_composition_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txOther
                  source_column: before_renewal_product_no
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSharpName
                  source_column: shape_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSharpCode
                  source_column: shape_cd
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSeason
                  source_column: season
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txPoint
                  source_column: use_point_cnt
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductSeries
                  source_column: product_series
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txWebOrderStartDate
                  source_column: sale_start_date
                  type: "datetime"
                  format: "%Y-%m-%dT%H:%M:%S.000+0900"
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txWebOrderFinishDate
                  source_column: sale_end_date
                  type: "datetime"
                  format: "%Y-%m-%dT%H:%M:%S.000+0900"
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSetProductFlag
                  source_column: set_product_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txPreferentialFlag
                  source_column: preferential_product_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txInventoryProduct
                  source_column: txinventoryproductid
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txMixpackFlag
                  source_column: buttobi_subsc_bundle_yn
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txFlagshipName
                  source_column: core_product_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductId
                  source_column: mail_order_product_cd

            # バンドル情報（セット商品または定期商品の場合）
            - target_element: bundled-products
              condition: "set_product_flg == 'true' or product_type == '11'"
              child_elements:
                # セット商品の場合の構成品
                - target_element: bundled-product
                  condition: "set_product_flg == 'true'"
                  sub_query: bundled_products_query
                  attributes:
                    product-id: "{product_id}"
                  child_elements:
                    - target_element: quantity
                      source_column: quantity
                      type: number
                # 定期商品の場合の構成品
                - target_element: bundled-product
                  condition: "product_type == '11'"
                  sub_query: regular_products_query
                  attributes:
                    product-id: "{product_id}"
                  child_elements:
                    - target_element: quantity
                      value: "1"

  transformations:
    - column: "sale_start_date"
      type: "datetime_convert"
      format: "%Y-%m-%dT%H:%M:%S.000+0900"

    - column: "sale_end_date"
      type: "datetime_convert"
      format: "%Y-%m-%dT%H:%M:%S.000+0900"
