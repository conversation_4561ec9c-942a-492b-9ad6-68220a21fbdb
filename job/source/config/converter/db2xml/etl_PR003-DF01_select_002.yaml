common:
  format: db2xml
  encoding: utf-8

output:
  root_element: pricebooks
  record_element: price-table
  indent: 2

etl:
  parameters:
    sync_timestamp:
      type: datetime
      description: 前回同期済時刻
      required: true
    diff_base_timestamp:
      type: datetime
      description: 差分基準時刻
      required: true
    split_num:
      type: "string"
      description: "分割単位"
      required: true

  # メインクエリ: 商品価格情報の取得
  pricelist_query: |
    SELECT
      product_id,
      amount,
      oms_delete_flg
    FROM wk_PR003_DF01_product_price_list
    WHERE amount IS NOT NULL
    AND split_num = :split_num
    AND oms_delete_flg = 0
    ORDER BY product_id

  # メインクエリ: 商品価格情報の取得(削除)
  pricelist_query_deleted: |
    SELECT
      product_id,
      amount,
      oms_delete_flg
    FROM wk_PR003_DF01_product_price_list
    WHERE amount IS NOT NULL
    AND split_num = :split_num
    AND oms_delete_flg != 0
    ORDER BY product_id

  mappings:
    # ルート要素：pricebooks
    - target_element: "pricebooks"
      attributes:
        xmlns: "http://www.demandware.com/xml/impex/pricebook/2006-10-31"
      child_elements:
        # pricebook要素
        - target_element: "pricebook"
          child_elements:
            # header要素
            - target_element: "header"
              attributes:
                pricebook-id: "dhc_pricelist-jpy"
              child_elements:
                - target_element: "currency"
                  value: "JPY"
                  type: "string"
            # price-tables要素
            - target_element: "price-tables"
              child_elements:
                - target_element: "price-table"
                  query: "pricelist_query_deleted"
                  attributes:
                    product-id: "{product_id}"
                    mode: "delete"
                - target_element: "price-table"
                  query: "pricelist_query"
                  attributes:
                    product-id: "{product_id}"
                  child_elements:
                    - target_element: "amount"
                      source_column: "amount"
                      type: "number"
                      attributes:
                        quantity: "1"
