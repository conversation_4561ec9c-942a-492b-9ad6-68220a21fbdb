#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime
import xml.etree.ElementTree as ET
from typing import Optional, Dict, List, Any
import pandas as pd
import json
import os

from source.converter_base import DBSourceConverter
from source.db_connector import DbConnector

from .xml_builder import XMLBuilderMixin
from .data_transformer import DataTransformerMixin
from .query_executor import QueryExecutorMixin
from .config_validator import ConfigValidatorMixin


class DB2XMLConverter(
    XMLBuilderMixin,
    DataTransformerMixin,
    QueryExecutorMixin,
    ConfigValidatorMixin,
    DBSourceConverter,
):
    """DBデータからXMLへの変換クラス"""

    def __init__(
        self,
        jobnet_id: str,
        etl_config: str,
        db_connection: DbConnector,
        last_sync_timestamp: Optional[datetime] = None,
        diff_base_timestamp: Optional[datetime] = None,
        split_num: Optional[str] = None,
        tmp_csv_file: Optional[str] = None,
        parent_column: Optional[str] = None,
        sub_tmp_csv_file: Optional[str] = None,
        sub_parent_column: Optional[str] = None
    ):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
            db_connection: DBコネクション
            last_sync_timestamp: 前回同期タイムスタンプ（オプション）
            diff_base_timestamp: 差分基準時刻（オプション）
        """
        # YAMLで定義されているパラメータ名に合わせて設定
        self.sync_timestamp = last_sync_timestamp
        self.diff_base_timestamp = diff_base_timestamp
        self.split_num = split_num

        self.converter_type = "db2xml"
        self._processed_parents = set()

        self.xml_counter = {}

        super().__init__(
            jobnet_id,
            etl_config,
            db_connection,
            last_sync_timestamp,
            diff_base_timestamp,
        )

        self.logger.debug("一時csvファイル読み込み開始")

        if tmp_csv_file is not None:
            self.tmp_csv_file = json.loads(tmp_csv_file)
            self.parent_column = parent_column
            self.df_sub_querys = {}
            for key,value in self.tmp_csv_file.items():
                s3_tmp_csv_file = "s3://" + os.environ["S3_BUCKET_NAME"] + value
                csv_data_frame = pd.read_csv(s3_tmp_csv_file, dtype=pd.StringDtype())
                self.df_sub_querys[key] = csv_data_frame.groupby(parent_column.split(","))
            # サブクエリのサブクエリに対応
            if sub_tmp_csv_file is not None:
                self.sub_tmp_csv_file = json.loads(sub_tmp_csv_file)
                self.sub_parent_column = sub_parent_column
                self.df_sub_sub_querys = {}
                for key,value in self.sub_tmp_csv_file.items():
                    s3_tmp_csv_file = "s3://" + os.environ["S3_BUCKET_NAME"] + value
                    csv_data_frame = pd.read_csv(s3_tmp_csv_file, dtype=pd.StringDtype())
                    self.df_sub_sub_querys[key] = csv_data_frame.groupby(sub_parent_column.split(","))

        self.logger.debug("一時csvファイル読み込み終了")

        # 各種検証の実行
        self.logger.debug("XML設定の検証開始")
        self._validate_xml_config()
        self.logger.debug("マッピング定義とクエリ参照の検証開始")
        self._validate_query_references()
        self.logger.debug("変換ルールの検証開始")
        self._validate_transformation_rules()
        self.logger.debug("全検証完了")

    def execute_conversion(self, output_path: str) -> str:
        """
        データ取得と変換を実行
        Args:
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        try:
            self.logger.debug("変換処理開始")
            query_results = self._execute_queries()
            # self.logger.debug(f"全クエリの実行結果: {query_results}")
            result = self._convert_data(query_results, output_path)
            self.logger.debug("変換処理完了")
            return result
        except Exception as e:
            self.logger.error_common(f"変換処理でエラーが発生: {str(e)}")
            raise

    def convert_from_data(self, data: List[Dict], output_path: str) -> str:
        """
        既存データの変換
        Args:
            data: 変換元データ
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        try:
            self.logger.debug("既存データからの変換開始")
            query_name = next(
                (m["query"] for m in self.config["etl"]["mappings"] if "query" in m),
                "default_query",
            )
            query_results = {query_name: data}
            result = self._convert_data(query_results, output_path)
            self.logger.debug("既存データからの変換完了")
            return result
        except Exception as e:
            self.logger.error_common(f"既存データの変換でエラーが発生: {str(e)}")
            raise

    def _convert_data(
        self, query_results: Dict[str, List[Dict]], output_path: str
    ) -> str:
        """
        データをXML形式に変換
        Args:
            query_results: クエリ結果
            output_path: 出力パス
        Returns:
            str: XML形式データ
        """
        try:
            self.logger.debug("XML構築開始")
            self.logger.debug(f"データ変換開始: {len(query_results)} クエリ")

            # トランスフォーメーションの適用
            if "transformations" in self.config.get("etl", {}):
                query_results = self._apply_transformations(
                    self.config["etl"]["transformations"], query_results
                )

            # マッピング定義の取得
            mappings = self.config.get("etl", {}).get("mappings", [])
            if not mappings:
                raise ValueError("mappings定義が見つかりません")

            # 最初のマッピング定義からルート要素を構築
            root = self._build_xml_element(query_results, mappings[0], None)

            # XML出力件数のログ出力
            for key, value in self.xml_counter.items():
                self.logger.info(
                    "I_job_db_to_file_xml_count_001"
                    ,msg_values=(key, value)
                )

            # XMLの整形
            result = self._format_xml(root)
            # self.logger.debug(f"生成されたXML内容:\n{result}")
            return result

        except Exception as e:
            self.logger.error_common(f"XML変換エラー: {str(e)}")
            raise