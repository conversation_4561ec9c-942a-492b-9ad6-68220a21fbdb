#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timezone, timedelta
import html
from .utils import convert_to_dict


class DataTransformerMixin:
    """データ変換処理の責務を担当"""

    def _format_datetime(self, dt_value: datetime, format_str: str) -> str:
        """
        日時を指定されたフォーマットで文字列に変換する共通処理
        Args:
            dt_value: 変換対象の日時値
            format_str: 変換フォーマット
        Returns:
            str: フォーマットされた日時文字列
        """
        # タイムゾーン情報がない場合はJST（UTC+9）を設定
        if dt_value.tzinfo is None:
            dt_value = dt_value.replace(tzinfo=timezone(timedelta(hours=9)))

        # フォーマットの末尾がZの場合はUTCに変換
        if format_str.endswith('Z'):
            # JSTからUTCへ変換
            dt_value = dt_value.astimezone(timezone.utc)
            # 'Z'を含むフォーマット文字列の場合、%zの代わりにZを使用
            format_str = format_str[:-1] + 'Z'
            return dt_value.strftime(format_str.replace('Z', '')) + 'Z'
        else:
            # 通常の%z形式でタイムゾーン情報を含む出力
            return dt_value.strftime(format_str)

    def _convert_value(
        self, value: Any, field_type: str, field_format: Optional[str] = None
    ) -> str:
        """
        データ型に応じた値の変換
        Args:
            value: 変換対象の値
            field_type: 変換後のデータ型
            field_format: 変換フォーマット（オプション）
        Returns:
            str: 変換後の値
        """
        if value is None:
            return self.config.get("transformations", {}).get("null_value", "")

        try:
            # self.logger.debug(
            #     f"値変換開始: value={value}, type={field_type}, format={field_format}"
            # )

            if field_type == "number":
                try:
                    if isinstance(value, Decimal):
                        value = float(value)
                    elif isinstance(value, str):
                        value = float(value)

                    if isinstance(value, float) and value.is_integer():
                        value = int(value)

                    if not isinstance(value, (int, float)):
                        raise ValueError(f"数値型への変換エラー: {value}")

                    if isinstance(value, float):
                        result = f"{value:.2f}"
                    else:
                        result = str(value)

                    # self.logger.debug(f"数値変換結果: {result}")
                    return result

                except (ValueError, TypeError):
                    raise ValueError(f"数値型への変換エラー: {value}")

            elif field_type == "datetime":
                if isinstance(value, str):
                    try:
                        # 大文字小文字を吸収し、様々な形式に対応
                        normalized_value = (
                            value.upper().replace("T", "T").replace("t", "T")
                        )

                        # 複数の日付形式に対応
                        date_formats = [
                            "%Y-%m-%dT%H:%M:%S.%f%z",  # マイクロ秒を含む形式
                            "%Y-%m-%dT%H:%M:%S%z",  # マイクロ秒なし
                            "%Y-%m-%d %H:%M:%S%z",  # スペース区切り
                        ]

                        # 形式を順番に試す
                        for fmt in date_formats:
                            try:
                                value = datetime.strptime(normalized_value, fmt)
                                break
                            except ValueError:
                                continue
                        else:
                            raise ValueError(f"サポートされていない日時形式: {value}")

                    except ValueError as e:
                        self.logger.error_common(f"日時変換エラー: {str(e)}")
                        raise

                # 共通処理を呼び出し
                format_str = field_format or "%Y-%m-%dT%H:%M:%S.000%z"
                result = self._format_datetime(value, format_str)
                # self.logger.debug(f"日時変換結果: {result}")
                return result

            elif field_type == "boolean":
                result=self._boolean_transform(value)
                # self.logger.debug(f"真偽値変換結果: {result}")
                return result

            elif field_type == "html":
                result = html.escape(str(value))
                # self.logger.debug(f"HTML変換結果: {result}")
                return result

            result = str(value)
            # self.logger.debug(f"文字列変換結果: {result}")
            return result

        except Exception as e:
            self.logger.error_common(f"値変換エラー: {str(e)}")
            raise ValueError(f"値の変換に失敗しました: {str(e)}")

    def _apply_transformations(
        self,
        transformations: List[Dict[str, Any]],
        query_results: Dict[str, List[Dict]],
    ) -> Dict[str, List[Dict]]:
        """データ変換ルールを適用する"""
        try:
            # self.logger.debug("データ変換ルールの適用開始")
            for transform in transformations:
                transform_type = transform.get("type")
                column = transform.get("column")
                target_query = transform.get("query")

                if not column:
                    raise ValueError("変換ルールにcolumnが指定されていません")

                # self.logger.debug(
                #     f"変換処理: type={transform_type}, column={column}, query={target_query}"
                # )

                for query_name, records in query_results.items():
                    if target_query and query_name != target_query:
                        continue

                    converted_records = []
                    for record in records:
                        record_dict = convert_to_dict(record)

                        if column not in record_dict and not target_query:
                            raise ValueError(
                                f"変換対象のカラム '{column}' が存在しません。"
                                f"利用可能なカラム: {list(record_dict.keys())}"
                            )

                        # 変換タイプに応じた処理
                        if (
                            transform_type == "replace_null"
                            and record_dict.get(column) is None
                        ):
                            record_dict[column] = transform.get("value", "")
                        elif transform_type == "boolean_convert":
                            record_dict[column] = (
                                transform.get("true_value", "true")
                                if record_dict.get(column)
                                else transform.get("false_value", "false")
                            )
                        elif transform_type == "datetime_convert" and record_dict.get(
                            column
                        ):
                            try:
                                dt_value = record_dict[column]
                                if isinstance(dt_value, str):
                                    dt_value = datetime.strptime(
                                        dt_value, "%Y-%m-%d %H:%M:%S"
                                    )

                                # 共通処理を呼び出し
                                format_str = transform.get("format", "%Y-%m-%dT%H:%M:%S.000%z")
                                record_dict[column] = self._format_datetime(dt_value, format_str)
                            except Exception as e:
                                self.logger.warning(f"日時変換エラー: {str(e)}")

                        converted_records.append(record_dict)

                    query_results[query_name] = converted_records

            return query_results

        except Exception as e:
            self.logger.error_common(f"データ変換中にエラーが発生しました: {str(e)}")
            raise

    def _boolean_transform(self, value):
        value_str = str(value).lower()
        if value_str in ("1", "true"):
            return "true"
        else:
            return "false"
