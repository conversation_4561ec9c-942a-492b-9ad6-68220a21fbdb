#!/usr/bin/env python
# -*- coding: utf-8 -*-
# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

import os
import sys
import boto3
import json
import requests
import time
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime
from source.common import (
    initialize_env,
    get_job_params,
    retry_function,
    str_to_bool,
)
from source.common_util import load_sql_config
from source.glue_logger import GlueLogger


class GlueJobBulkApiRegister:
    """BulkAPI登録処理ジョブ"""

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)
        self.s3_client = boto3.client("s3")
        self.params = None
        self.retry_limit = 3
        self.retry_interval = 1.0
        self.retryable_status_codes = [500, 502, 503, 504]

    def get_s3_file(self, input_file_dir: str, file_name: str):
        """
        S3ファイル取得
        Args:
            input_file_dir: インプットファイルディレクトリ
            file_name: ファイル名
        Returns:
            str: ファイル内容
        """

        def _get():
            return self.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file_dir + file_name,
            )

        method_name = "S3ファイル取得"
        return retry_function(
            _get,
            self.logger,
            method_name,
            "I_job_bulk_api_register_003",
            "E_job_bulk_api_register_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def get_api_info(self, api_secret_name: str):
        # API接続情報格納secret_id
        self.api_secret_name = api_secret_name
        # secret_manager クライアント
        self.secrets_client = boto3.client("secretsmanager")

        response = self.secrets_client.get_secret_value(SecretId=self.api_secret_name)
        secret_string = response["SecretString"]
        return json.loads(secret_string)

    def requests_get_with_retry(self, process_name, url, headers=None):
        """
        リトライ機能付きでHTTP GETリクエストを実行する

        Args:
            process_name: プロセス名
            url: リクエスト先URL
            headers: リクエストヘッダ
        Returns:
            requests.Response: レスポンスオブジェクト
        """
        retry_counts = 0

        while retry_counts <= self.retry_limit:
            response = requests.get(url, headers=headers)
            status_code = response.status_code

            if status_code not in self.retryable_status_codes:
                return response

            retry_counts += 1
            if retry_counts <= self.retry_limit:
                self.logger.info(
                    "I_job_bulk_api_register_003",
                    msg_values=(process_name),
                )
                time.sleep(self.retry_interval)
            else:
                self.logger.error(
                    "E_job_bulk_api_register_002",
                    msg_values=[f"{process_name}リトライ上限"],
                )
                return response

        return response

    def requests_post_with_retry(self, process_name, url, headers=None, data=None):
        """
        リトライ機能付きでHTTP POSTリクエストを実行する

        Args:
            process_name: プロセス名
            url: リクエスト先URL
            headers: リクエストヘッダ
            data: POSTリクエストデータ
        Returns:
            requests.Response: レスポンスオブジェクト
        """
        retry_counts = 0

        while retry_counts <= self.retry_limit:
            response = requests.post(url, headers=headers, data=data)
            status_code = response.status_code

            # リトライ不要のステータスコードの場合は早期リターン
            if status_code not in self.retryable_status_codes:
                return response

            retry_counts += 1
            if retry_counts <= self.retry_limit:
                self.logger.info(
                    "I_job_bulk_api_register_003",
                    msg_values=(process_name),
                )
                time.sleep(self.retry_interval)
            else:
                self.logger.error(
                    "E_job_bulk_api_register_002",
                    msg_values=[f"{process_name}リトライ上限"],
                )
                return response

        return response

    def requests_put_with_retry(self, process_name, url, headers=None, data=None):
        """
        リトライ機能付きでHTTP PUTリクエストを実行する

        Args:
            process_name: プロセス名
            url: リクエスト先URL
            headers: リクエストヘッダ
            data: PUTリクエストデータ
        Returns:
            requests.Response: レスポンスオブジェクト
        """
        retry_counts = 0

        while retry_counts <= self.retry_limit:
            response = requests.put(url, headers=headers, data=data)
            status_code = response.status_code

            # リトライ不要のステータスコードの場合は早期リターン
            if status_code not in self.retryable_status_codes:
                return response

            retry_counts += 1
            if retry_counts <= self.retry_limit:
                self.logger.info(
                    "I_job_bulk_api_register_003",
                    msg_values=(process_name),
                )
                time.sleep(self.retry_interval)
            else:
                self.logger.error(
                    "E_job_bulk_api_register_002",
                    msg_values=[f"{process_name}リトライ上限"],
                )
                return response

        return response

    def requests_patch_with_retry(self, process_name, url, headers=None, data=None):
        """
        リトライ機能付きでHTTP PATCHリクエストを実行する

        Args:
            process_name: プロセス名
            url: リクエスト先URL
            headers: リクエストヘッダ
            data: PATCHリクエストデータ
        Returns:
            requests.Response: レスポンスオブジェクト
        """
        retry_counts = 0

        while retry_counts <= self.retry_limit:
            response = requests.patch(url, headers=headers, data=data)
            status_code = response.status_code

            # リトライ不要のステータスコードの場合は早期リターン
            if status_code not in self.retryable_status_codes:
                return response

            retry_counts += 1
            if retry_counts <= self.retry_limit:
                self.logger.info(
                    "I_job_bulk_api_register_003",
                    msg_values=(process_name),
                )
                time.sleep(self.retry_interval)
            else:
                self.logger.error(
                    "E_job_bulk_api_register_002",
                    msg_values=[f"{process_name}リトライ上限"],
                )
                return response

        return response

    def get_access_token(self, api_info: Dict[str, Any]):
        """
        アクセストークン取得
        Args:
            api_info: API接続情報
        Returns:
            str: アクセストークン
        """
        method_name = "アクセストークン取得"
        try:
            self.logger.info(
                "I_job_bulk_api_register_004",
                msg_values=(method_name, "認証情報"),
            )
            response = self.requests_get_with_retry(
                method_name,
                api_info["host"]
                + api_info["token_endpoint"]
                + "?grant_type=client_credentials&client_id="
                + api_info["client_id"]
                + "&client_secret="
                + api_info["client_secret"]
            )
            status_code = response.status_code
            self.logger.info(
                "I_job_bulk_api_register_005",
                msg_values=(method_name, status_code),
            )
            if status_code != 200:
                raise BaseException
            token_info = response.json()
            return token_info["access_token"]
        except BaseException as e:
            self.logger.error("E_job_bulk_api_register_002", msg_values=(method_name))
            self.logger.error_common(f"{method_name}エラー: {str(e)}")
            raise e

    def create_bulk_api_job(
        self, api_info: Dict[str, Any], token: str, bulk_api_setting: Dict[str, Any]
    ):
        """
        BulkAPIジョブ作成
        Args:
            api_info: API接続情報
            token: アクセストークン
            bulk_api_setting: BulkAPI設定
        Returns:
            str: ジョブID
        """
        method_name = "BulkAPIジョブ作成"
        try:
            request_param = json.dumps(bulk_api_setting["request"])
            self.logger.info(
                "I_job_bulk_api_register_004",
                msg_values=(method_name, request_param),
            )
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
            }
            response = self.requests_post_with_retry(
                method_name,
                api_info["host"] + api_info["bulk_api_path"],
                headers=headers,
                data=request_param,
            )
            status_code = response.status_code
            self.logger.info(
                "I_job_bulk_api_register_005",
                msg_values=(method_name, status_code),
            )
            info = response.json()
            if status_code != 200 or "id" not in info:
                raise BaseException
            return info.get("id")
        except BaseException as e:
            self.logger.error("E_job_bulk_api_register_002", msg_values=(method_name))
            self.logger.error_common(f"{method_name}エラー: {str(e)}")
            raise e

    def upload_bulk_api_csv(
        self, api_info: Dict[str, Any], token: str, job_id: str, csv_data: bytes
    ):
        """
        BulkAPI_CSVアップロード
        Args:
            api_info: API接続情報
            token: アクセストークン
            job_id: ジョブID
            csv_data: CSVファイルデータ(バイト配列)
        """
        method_name = "BulkAPI_CSVアップロード"
        try:
            self.logger.info(
                "I_job_bulk_api_register_004",
                msg_values=(method_name, self.file_name),
            )
            headers = {"Authorization": f"Bearer {token}", "Content-Type": "text/csv"}
            response = self.requests_put_with_retry(
                method_name,
                api_info["host"] + api_info["bulk_api_path"] + job_id + "/batches/",
                headers=headers,
                data=csv_data,
            )
            status_code = response.status_code
            self.logger.info(
                "I_job_bulk_api_register_005",
                msg_values=(method_name, status_code),
            )
            if status_code != 201:
                raise BaseException
        except BaseException as e:
            self.logger.error("E_job_bulk_api_register_002", msg_values=(method_name))
            self.logger.error_common(f"{method_name}エラー: {str(e)}")
            raise e

    def update_bulk_api_status(self, api_info: Dict[str, Any], token: str, job_id: str):
        """
        BulkAPIステータス更新
        Args:
            api_info: API接続情報
            token: アクセストークン
            job_id: ジョブID
        """
        method_name = "BulkAPIステータス更新"
        try:
            request_param_dict = {"state": "UploadComplete"}
            request_param = json.dumps(request_param_dict)
            self.logger.info(
                "I_job_bulk_api_register_004",
                msg_values=(method_name, request_param),
            )
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
            }
            response = self.requests_patch_with_retry(
                method_name,
                api_info["host"] + api_info["bulk_api_path"] + job_id + "/",
                headers=headers,
                data=request_param,
            )
            status_code = response.status_code
            self.logger.info(
                "I_job_bulk_api_register_005",
                msg_values=(method_name, status_code),
            )
            if status_code != 200:
                raise BaseException
        except BaseException as e:
            self.logger.error("E_job_bulk_api_register_002", msg_values=(method_name))
            self.logger.error_common(f"{method_name}エラー: {str(e)}")
            raise e

    def get_bulk_api_status(
        self,
        api_info: Dict[str, Any],
        token: str,
        bulk_api_setting: Dict[str, Any],
        job_id: str,
    ):
        """
        BulkAPIステータス確認
        Args:
            api_info: API接続情報
            token: アクセストークン
            bulk_api_setting: BulkAPI設定
            job_id: ジョブID
        Returns:
            str: BulkAPIステータス
        """
        method_name = "BulkAPIステータス確認"
        try:
            interval = int(bulk_api_setting["interval"])
            headers = {"Authorization": f"Bearer {token}"}
            url = api_info["host"] + api_info["bulk_api_path"] + job_id + "/"

            while True:
                time.sleep(interval)

                self.logger.info(
                    "I_job_bulk_api_register_004",
                    msg_values=(method_name, "なし"),
                )
                response = self.requests_get_with_retry(method_name, url, headers=headers)
                status_code = response.status_code
                self.logger.info(
                    "I_job_bulk_api_register_005",
                    msg_values=(method_name, status_code),
                )
                if status_code == 200:
                    info = response.json()
                    state = info.get("state")
                    if (
                        state == "JobComplete"
                        or state == "Failed"
                        or state == "Aborted"
                    ):
                        return state
                    elif state == "UploadComplete" or state == "InProgress":
                        continue
                    else:
                        raise BaseException
                else:
                    raise BaseException
        except BaseException as e:
            self.logger.error("E_job_bulk_api_register_002", msg_values=(method_name))
            self.logger.error_common(f"{method_name}エラー: {str(e)}")
            raise e

    def get_bulk_api_unprocessed_records(
        self, api_info: Dict[str, Any], token: str, job_id: str
    ):
        """
        BulkAPI未処理レコード取得
        Args:
            api_info: API接続情報
            token: アクセストークン
            job_id: ジョブID
        Returns:
            bytes: CSVファイルデータ(バイトデータ)
        """
        method_name = "BulkAPI未処理レコード取得"
        try:
            self.logger.info(
                "I_job_bulk_api_register_004",
                msg_values=(method_name, "なし"),
            )
            headers = {"Authorization": f"Bearer {token}"}
            response = self.requests_get_with_retry(
                method_name,
                api_info["host"]
                + api_info["bulk_api_path"]
                + job_id
                + "/unprocessedrecords/",
                headers=headers,
            )
            status_code = response.status_code
            self.logger.info(
                "I_job_bulk_api_register_005",
                msg_values=(method_name, status_code),
            )
            if status_code != 200:
                raise BaseException
            return response.content
        except BaseException as e:
            self.logger.error("E_job_bulk_api_register_002", msg_values=(method_name))
            self.logger.error_common(f"{method_name}エラー: {str(e)}")
            raise e

    def put_s3_unprocessed_records(self, backup_file_dir, file_name, csv_data: bytes):
        """
        S3未処理レコード配置
        Args:
            backup_file_dir: バックアップファイルディレクトリ
            file_name: ファイル名
            csv_data: ファイル内容(バイトデータ)
        """

        def _put():
            self.s3_client.put_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=backup_file_dir + "unprocessed_" + file_name,
                Body=csv_data,
            )

        method_name = "S3未処理レコード配置"
        retry_function(
            _put,
            self.logger,
            method_name,
            "I_job_bulk_api_register_003",
            "E_job_bulk_api_register_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def get_bulk_api_failed_records(
        self, api_info: Dict[str, Any], token: str, job_id: str
    ):
        """
        BulkAPI失敗レコード取得
        Args:
            api_info: API接続情報
            token: アクセストークン
            job_id: ジョブID
        Returns:
            bytes: CSVファイルデータ(バイトデータ)
        """
        method_name = "BulkAPI失敗レコード取得"
        try:
            self.logger.info(
                "I_job_bulk_api_register_004",
                msg_values=(method_name, "なし"),
            )
            headers = {"Authorization": f"Bearer {token}"}
            response = self.requests_get_with_retry(
                method_name,
                api_info["host"]
                + api_info["bulk_api_path"]
                + job_id
                + "/failedResults/",
                headers=headers,
            )
            status_code = response.status_code
            self.logger.info(
                "I_job_bulk_api_register_005",
                msg_values=(method_name, status_code),
            )
            if status_code != 200:
                raise BaseException
            return response.content
        except BaseException as e:
            self.logger.error("E_job_bulk_api_register_002", msg_values=(method_name))
            self.logger.error_common(f"{method_name}エラー: {str(e)}")
            raise e

    def put_s3_failed_records(self, backup_file_dir, file_name, csv_data: bytes):
        """
        S3失敗レコード配置
        Args:
            backup_file_dir: バックアップファイルディレクトリ
            file_name: ファイル名
            csv_data: ファイル内容(バイトデータ)
        """

        def _put():
            self.s3_client.put_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=backup_file_dir + "failed_" + file_name,
                Body=csv_data,
            )

        method_name = "S3失敗レコード配置"
        retry_function(
            _put,
            self.logger,
            method_name,
            "I_job_bulk_api_register_003",
            "E_job_bulk_api_register_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def backup_input_file(
        self, input_file_dir: str, backup_file_dir: str, file_name: str
    ):
        """
        インプットファイルバックアップ
        Args:
            input_file_dir: インプットファイルディレクトリ
            backup_file_dir: バックアップファイルディレクトリ
            file_name: ファイル名
        """

        def _copy():
            bucket_name = os.environ.get("S3_BUCKET_NAME")
            self.s3_client.copy_object(
                Bucket=bucket_name,
                Key=backup_file_dir + file_name,
                CopySource={"Bucket": bucket_name, "Key": input_file_dir + file_name},
            )

        method_name = "インプットファイルバックアップ"
        retry_function(
            _copy,
            self.logger,
            method_name,
            "I_job_bulk_api_register_003",
            "E_job_bulk_api_register_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def delete_input_file(self, input_file_dir: str, file_name: str):
        """
        インプットファイル削除
        Args:
            input_file_dir: インプットファイルディレクトリ
            file_name: ファイル名
        """

        def _delete():
            self.s3_client.delete_object(
                Bucket=os.environ.get("S3_BUCKET_NAME"), Key=input_file_dir + file_name
            )

        method_name = "インプットファイル削除"
        retry_function(
            _delete,
            self.logger,
            method_name,
            "I_job_bulk_api_register_003",
            "E_job_bulk_api_register_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
        """
        self.params = params
        self.file_name = params["file_name"]
        try:
            self.input_file_dir = params["input_file_dir"]
            self.backup_flag = params["backup_flag"]
            self.backup_file_dir = params["backup_file_dir"]

            # 開始処理
            self.logger.info(
                "I_job_bulk_api_register_001",
                (self.file_name),
            )

            # 取得対象のファイルをログ出力
            target_file_path = os.path.join(
                self.input_file_dir, self.file_name
            )

            self.logger.info("I_job_bulk_api_register_006", msg_values=(target_file_path))


            # S3ファイル取得
            response = self.get_s3_file(self.input_file_dir, self.file_name)

            if response["ContentLength"] != 0:
                # 0バイトではない場合

                # アクセストークン取得
                api_info = self.get_api_info(self.params["api_secret_name"])
                token = self.get_access_token(api_info)

                # BulkAPIジョブ作成
                bulk_api_setting = json.loads(params["bulk_api_setting"])
                job_id = self.create_bulk_api_job(api_info, token, bulk_api_setting)

                # BulkAPI CSVアップロード
                self.upload_bulk_api_csv(
                    api_info, token, job_id, response["Body"].read()
                )

                # BulkAPIステータス更新
                self.update_bulk_api_status(api_info, token, job_id)

                # BulkAPIステータス確認
                state = self.get_bulk_api_status(
                    api_info, token, bulk_api_setting, job_id
                )

                if state == "Failed" or state == "Aborted":
                    # レスポンスの"state"が"Failed","Aborted"の場合

                    # BulkAPI未処理レコード取得
                    csv_data = self.get_bulk_api_unprocessed_records(
                        api_info, token, job_id
                    )

                    # S3未処理レコード配置
                    self.put_s3_unprocessed_records(
                        self.backup_file_dir, self.file_name, csv_data
                    )

                    # BulkAPI失敗レコード取得
                    csv_data = self.get_bulk_api_failed_records(api_info, token, job_id)

                    # S3失敗レコード配置（ここで正常処理時も例外送出して異常終了）
                    self.put_s3_failed_records(
                        self.backup_file_dir, self.file_name, csv_data
                    )
                    raise BaseException
            else:
                # 0バイトの場合
                self.logger.info("I_job_bulk_api_register_007", msg_values=(target_file_path))

            # インプットファイルバックアップ
            if self.backup_flag:
                self.backup_input_file(
                    self.input_file_dir, params["backup_file_dir"], self.file_name
                )

            # インプットファイル削除
            self.delete_input_file(self.input_file_dir, self.file_name)

            # 終了処理
            self.logger.info(
                "I_job_bulk_api_register_002",
                (self.file_name),
            )

        except BaseException as e:
            # 例外処理
            self.logger.error("E_job_bulk_api_register_003", (str(e),))
            self.logger.error(
                "E_job_bulk_api_register_001",
                (self.file_name),
            )
            raise


def get_params():
    # パラメータの取得
    params = get_job_params()

    # 必須パラメータのチェック
    required_params = [
        "api_secret_name",  # API　Secrets Managerシークレット名
        "bulk_api_setting",  # BulkAPI設定
        "input_character_encoding",  # インプット文字コード
        "file_name",  # ファイル名
        "input_file_dir",  # インプットファイルディレクトリ
        "backup_file_dir",  # バックアップファイルディレクトリ
        "jobnet_id",  # ジョブネットID
    ]
    for param in required_params:
        if param not in params:
            raise ValueError(f"Required parameter '{param}' is missing")

    # 任意パラメータのデフォルト値設定
    params["backup_flag"] = str_to_bool(params.get("backup_flag", "False"))  # バックアップフラグ

    return params


def main():
    """メイン関数"""
    try:
        # 環境変数の初期化
        initialize_env()

        # パラメータ取得
        params = get_params()

        # ジョブの実行
        job = GlueJobBulkApiRegister(params["jobnet_id"])
        job.execute(params)
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        raise e


if __name__ == "__main__":
    main()
