#!/usr/bin/env python
# -*- coding: utf-8 -*-
# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

import os
import io
import zipfile
import boto3
from typing import Dict, Any

from source.common import initialize_env, get_job_params, retry_function, find_s3_prefix, str_to_bool
from source.glue_logger import GlueLogger


class GlueJobFileCompress:
    """ファイル圧縮ジョブ"""

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)
        self.s3_client = boto3.client("s3")

    def get_s3_file(self, input_file_dir: str, file_name: str):
        """
        S3ファイル取得
        Args:
            input_file_dir: インプットファイルディレクトリ
            file_name: ファイル名
        Returns:
            str: ファイル内容
        """

        def _get():
            actual_path = os.path.join(input_file_dir, file_name)

            return self.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=actual_path,
            )

        method_name = "S3ファイル取得"
        return retry_function(
            _get,
            self.logger,
            method_name,
            "I_job_file_compress_003",
            "E_job_file_compress_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def file_compress(
        self,
        file_name: str,
        file_data: Any,
    ):
        """
        ファイル圧縮
        Args:
            file_name: 圧縮するファイル名
            file_data: ファイル内容（ファイル圧縮前）
        Returns:
            str: ファイル内容（ファイル圧縮後）
        """
        try:
            ext = os.path.splitext(file_name)[1]
            if ext.upper() == ".zip".upper():
                return file_data
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.writestr(file_name, file_data)
            zip_buffer.seek(0)
            return zip_buffer.getvalue()
        except Exception as e:
            self.logger.error(
                "E_job_file_compress_003",
                msg_values=(str(e)),
            )
            self.logger.error(
                "E_job_file_compress_002",
                msg_values=("ファイル圧縮"),
            )
            raise

    def put_s3_file(self, output_file_dir, output_file_name, input_data_encode):
        """
        S3ファイル配置
        Args:
            output_file_dir: アウトプットファイルディレクトリ
            output_file_name: 圧縮後ファイル名
            input_data_encode: ファイル内容（ファイル圧縮後）
        """

        def _put():

            actual_path = os.path.join(output_file_dir, output_file_name)
            self.s3_client.put_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=actual_path,
                Body=input_data_encode,
            )

        method_name = "S3ファイル配置"
        retry_function(
            _put,
            self.logger,
            method_name,
            "I_job_file_compress_003",
            "E_job_file_compress_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def backup_input_file(
        self, input_file_dir: str, backup_file_dir: str, file_name: str
    ):
        """
        インプットファイルバックアップ
        Args:
            input_file_dir: インプットファイルディレクトリ
            backup_file_dir: バックアップファイルディレクトリ
            file_name: ファイル名
        """

        def _copy():
            bucket_name = os.environ.get("S3_BUCKET_NAME")
            self.s3_client.copy_object(
                Bucket=bucket_name,
                Key=backup_file_dir + file_name,
                CopySource={"Bucket": bucket_name, "Key": input_file_dir + file_name},
            )

        method_name = "インプットファイルバックアップ"
        retry_function(
            _copy,
            self.logger,
            method_name,
            "I_job_file_compress_003",
            "E_job_file_compress_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def delete_input_file(self, input_file_dir: str, file_name: str):
        """
        インプットファイル削除
        Args:
            input_file_dir: インプットファイルディレクトリ
            file_name: ファイル名
        """

        def _delete():
            self.s3_client.delete_object(
                Bucket=os.environ.get("S3_BUCKET_NAME"), Key=input_file_dir + file_name
            )

        method_name = "インプットファイル削除"
        retry_function(
            _delete,
            self.logger,
            method_name,
            "I_job_file_compress_003",
            "E_job_file_compress_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
                - input_file_dir: インプットファイルディレクトリ
                - file_name: ファイル名
                - output_file_dir: アウトプットファイルディレクトリ
                - output_file_name: 圧縮後ファイル名
                - backup_flag: バックアップフラグ
                - backup_file_dir: バックアップファイルディレクトリ
                - jobnet_id: ジョブネットID
        """
        try:
            self.params = params
            input_file_dir_origin = params["input_file_dir"]
            # 入力のinput_file_dirにタイムスタンプがついてない場合、s3上一番古いタイムスタンプをついて変数に入れ替える
            input_file_dir = find_s3_prefix(input_file_dir_origin, os.environ["S3_BUCKET_NAME"])[0]
            output_file_dir = params["output_file_dir"]
            file_name = params["file_name"]
            output_file_name =  params["output_file_name"]

            # 2.4.2 開始ログ出力
            self.logger.info(
                "I_job_file_compress_001",
                msg_values=(file_name),
            )

            # 入力ファイル・出力ファイルをログに出力
            input_file_path = os.path.join(input_file_dir, file_name)
            output_file_path = os.path.join(output_file_dir, output_file_name)
            self.logger.info("I_job_file_compress_004", (input_file_path, output_file_path))

            # 2.4.3 S3ファイル取得
            file_data = self.get_s3_file(input_file_dir, file_name)

            # 2.4.4 ファイル圧縮
            input_data_encode = self.file_compress(
                file_name,
                file_data["Body"].read(),
            )

            # 2.4.5 S3ファイル配置
            self.put_s3_file(output_file_dir, output_file_name, input_data_encode)

            # 2.4.8 終了処理
            self.logger.info(
                "I_job_file_compress_002",
                msg_values=(file_name),
            )
        except Exception as e:
            # 2.4.9 例外処理
            self.logger.error(
                "E_job_file_compress_003",
                msg_values=(str(e)),
            )
            self.logger.error(
                "E_job_file_compress_001",
                msg_values=(params["file_name"]),
            )
            raise e
        finally:
            try:
                # 2.4.6 インプットファイルバックアップ
                if params["backup_flag"]:
                    backup_file_dir = params["backup_file_dir"]
                    self.backup_input_file(input_file_dir, backup_file_dir, file_name)

                # 2.4.7 インプットファイル削除
                self.delete_input_file(input_file_dir, file_name)
            except Exception as e:
                # 2.4.9 例外処理
                self.logger.error(
                    "E_job_file_compress_003",
                    msg_values=(str(e)),
                )
                self.logger.error(
                    "E_job_file_compress_001",
                    msg_values=(params["file_name"]),
                )
                raise e




def get_params():
    # 2.4.1 パラメータ取得
    params = get_job_params()

    # 必須パラメータのチェック
    required_params = [
        "input_file_dir",  # インプットファイルディレクトリ
        "file_name",  # ファイル名
        "output_file_dir",  # アウトプットファイルディレクトリ
        "output_file_name",  # 圧縮後ファイル名
        "jobnet_id",  # ジョブネットID
    ]
    for param in required_params:
        if param not in params:
            raise ValueError(f"Required parameter '{param}' is missing")

    # 任意パラメータのデフォルト値設定
    params["backup_flag"] = str_to_bool(params.get("backup_flag", "False"))  # バックアップフラグ
    params.setdefault("backup_file_dir", "")  # バックアップファイルディレクトリ

    if params.get("backup_flag") and not params.get("backup_file_dir"):
        raise ValueError(f"Required parameter 'backup_file_dir' is missing")

    return params


# メイン処理
def main():
    """メイン関数"""
    try:
        # 環境変数の初期化
        initialize_env()

        # 2.4.1 パラメータ取得
        params = get_params()

        # ジョブの実行
        job = GlueJobFileCompress(params["jobnet_id"])
        job.execute(params)

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        raise e

if __name__ == "__main__":
    main()
