# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

import os
import sys
import boto3
import re
from pandas.errors import EmptyDataError
from typing import Dict, Any

from source.db_connector import DbConnector
from source.glue_logger import <PERSON><PERSON><PERSON>ogger
from source.common import (
    initialize_env,
    get_job_params,
    retry_function,
    find_s3_prefix,
    find_s3_file,
    str_to_bool,
)
from source.common_util import load_sql_config
from source.common_exception import FileNotFoundException


class GlueJobInternalDbImport:

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)
        self.s3_client = boto3.client("s3")

    def backup_to_s3(self, in_path: str, bk_path: str):
        """
        S3にファイルをアップロード（リトライ処理付き）
        Args:
            in_path: ファイル内容
            bk_path: 出力パス(ファイル名含むフルパス)
        """

        # ファイルバックアップ
        def backup():
            self.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=bk_path,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": in_path,
                },
            )

        # リトライ共通関数呼び出し
        try:
            retry_function(
                func=backup,
                info_msg_id="I_job_internal_db_import_003",
                error_msg_id="E_job_internal_db_import_002",
                retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
                retry_interval=float(os.environ.get("S3_RETRY_INTERVAL")),
                logger=self.logger,
                process_name="S3ファイルバックアップ",
                # args=None,
            )

        except Exception as e:
            self.logger.error(
                msg_id="E_job_internal_db_import_002",
                msg_values=("S3ファイル配置"),
            )
            # 例外オブジェクトログ出力
            self.logger.error_common(f"S3ファイル配置: {str(e)}")
            raise

    def connect_db(self, secret_name: str):
        """
        データベースに接続
        Args:
            secret_name: 接続情報シークレット名
        """
        try:
            self.db_connector = DbConnector(self.logger, secret_id=secret_name)
            self.db_connector.connect()
            self.logger.info(
                msg_id="I_job_internal_db_import_003", msg_values=("DB接続成功")
            )
        except Exception as e:
            self.logger.error(
                msg_id="E_job_internal_db_import_002", msg_values=("DB接続")
            )
            # 例外オブジェクトログ出力
            self.logger.error_common(f"DB接続: {str(e)}")
            raise

    def set_default_format_options(self):
        # オプション有無の判定
        # FORCE_NULLまたはFORCE_NOT_NULLオプションが存在しない場合、FORCE NULLをデフォルトで付与
        options = self.params["input_format_options"]

        add_options = []

        # FORMAT csv
        if not (re.search(r"(,| |\()FORMAT.*(,|\))", options.upper())):
            add_options.append("FORMAT csv")

        # HEADER true
        if not (re.search(r"(,| |\()HEADER.*(,|\))", options.upper())):
            add_options.append("HEADER true")

        # QUOTE '"'
        if not (re.search(r"(,| |\()QUOTE.*(,|\))", options.upper())):
            add_options.append("QUOTE '\"'")

        # FORCE_NULL(*)
        if not (re.search(r"(,| |\()(FORCE_NULL|FORCE_NOT_NULL) *\(.+\) *(,|\))", options.upper())):
            # FORCE_NULL対象として、Not null制約がついていないカラムを取得する
            query_select_force_cols = load_sql_config("sql_common_select_002.sql")
            force_cols = self.db_connector.exec(
                sql_template=query_select_force_cols,
                values={"table_name": self.params["import_table"]}
            )
            force_col_rows = force_cols.fetchall()
            force_col_names = [row[0] for row in force_col_rows]

            # カラムが1件以上ある場合のみFORCE_NULL句を追加
            if force_col_names:
                force_null = "FORCE_NULL(" + ",".join(force_col_names) + ")"
                add_options.append(force_null)

        # NULL ''
        if not (re.search(r"(,| |\()NULL.*(,|\))", options.upper())):
            add_options.append("NULL ''")

        # デフォルトオプションを結合(必須パラメータのため指定されていない場合は考慮しない)
        options_splitted = options.rsplit(")", 1)  # 終端の)で1回分割
        options = options_splitted[0]  # 終端の)を除く文字列を取得
        for option in add_options:
            options += ", " + option  # デフォルトオプションをカンマ区切りで結合
        options += ")"  # 終端の)を補完

        self.params["input_format_options"] = options
        self.logger.debug("INPUT_FORMAT_OPTIONS：" + options)

    def execute_import(self, input_file_name):
        """
        クエリを実行してデータを取得
        Args:
            input_file_name: インポートに使用するファイル名
        """
        try:
            # TRUNCATE
            # truncateクエリの取得
            query_truncate = load_sql_config("sql_common_truncate_001.sql")
            query_truncate = query_truncate.replace(
                ":table_name", self.params["import_table"]
            )
            self.db_connector.exec(sql_template=query_truncate)

            # インプットファイルDBインポート
            # URI取得
            # importクエリの取得
            query_create_uri = load_sql_config("aws_commons.create_s3_uri.sql")

            actual_path = os.path.join(self.params["input_file_dir"], input_file_name)

            #インポートファイルパスをログ出力
            self.logger.info("I_job_internal_db_import_005",(actual_path) )

            s3_info = self.db_connector.exec(
                sql_template=query_create_uri,
                values={
                    "bucket": os.environ["S3_BUCKET_NAME"],
                    "file_path": actual_path,
                    "region": os.environ["AWS_DEFAULT_REGION"],
                },
            )
            s3_info = s3_info.fetchall()[0][0]

            # データが存在しない場合はライブラリの仕様によりエラーとなるためImportさせない
            exist_data = False

            exist_header = (
                "HEADER FALSE" not in self.params["input_format_options"].upper() and
                "HEADER" in self.params["input_format_options"].upper()
            )
            # shift_jisは予備で設定
            for enc in ["utf-8", "cp932", "shift_jis"]:
                try:
                    # S3からオブジェクトを取得
                    response = self.s3_client.get_object(
                        Bucket=os.environ["S3_BUCKET_NAME"], Key=actual_path
                    )
                    lines = len(response["Body"].read().decode(enc).splitlines())
                    # ヘッダー有無を判別しデータの有無を判別
                    if exist_header:
                        # ヘッダー行分を削除
                        lines -= 1
                    # データ数が1行以上存在するか
                    if lines > 0:
                        exist_data = True

                    is_decode_error = False
                    break

                except UnicodeDecodeError:
                    # continue
                    self.logger.debug(
                        msg="文字コード[" + enc + "]のファイル読み込み失敗"
                    )
                    is_decode_error = True
                except EmptyDataError:
                    # ※データなし
                    break

            if is_decode_error:
                self.logger.error(
                    msg_id="E_job_internal_db_import_004",
                    msg_values=(input_file_name),
                )
                raise Exception

            # データ有の場合のみインポート処理実行

            if exist_data:

                # インポート
                # importクエリの取得
                query_import_table = load_sql_config("aws_s3.table_import_from_s3.sql")
                self.db_connector.exec(
                    sql_template=query_import_table,
                    values={
                        "table_name": self.params["import_table"],
                        # "column_list": None,
                        "options": self.params["input_format_options"],
                        "s3_info": s3_info,
                    },
                )
            else:
                # データが存在しないためインポートが行われませんでした。
                self.logger.info(
                    msg_id="I_job_internal_db_import_004",
                    msg_values=(input_file_name),
                )

        except Exception as e:
            self.logger.error(
                msg_id="E_job_internal_db_import_002",
                msg_values=("インポート実行"),
            )
            # 例外オブジェクトログ出力
            self.logger.error_common(f"インポート実行: {str(e)}")
            raise

    def execute_upsert(self, query_id: str):
        """
        クエリを実行してデータを取得
        Args:
            query_id: クエリID
        """
        try:
            # upsertクエリの取得
            query_upsert = load_sql_config(f"{query_id}.sql")
            params = {
                "user": self.db_connector.username,
            }

            # UPSERT実行
            self.db_connector.exec(sql_template=query_upsert, values=params)

        except Exception as e:
            self.logger.error(
                msg_id="E_job_internal_db_import_002", msg_values=("クエリ実行")
            )
            # 例外オブジェクトログ出力
            self.logger.error_common(f"クエリ実行: {str(e)}")
            raise

    def delete_file(self, delete_file: str):
        """
        インプットファイルを削除
        Args:
            delete_file: 削除ファイルパス
        """

        # ファイル削除
        def delete():
            self.s3_client.delete_object(
                Bucket=os.environ.get("S3_BUCKET_NAME"),  # Backet
                Key=delete_file,  # Key
            )

        # リトライ共通関数呼び出し
        retry_function(
            func=delete,
            process_name="インポートファイル削除",
            info_msg_id="I_job_internal_db_import_003",
            error_msg_id="E_job_internal_db_import_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT", 3)),
            retry_interval=float(os.environ.get("S3_RETRY_INTERVAL", 1.0)),
            logger=self.logger,
        )

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 起動パラメータ
        """

        db_connector = None
        input_file_dir = None
        input_file_name = None
        input_path = None

        try:

            self.params = params
            # 開始処理
            self.logger.info(
                msg_id="I_job_internal_db_import_001",
                msg_values=(params["input_file_name"]),
            )

            # 入力のinput_file_dirにタイムスタンプがついてない場合、s3上一番古いタイムスタンプをついて変数に入れ替える
            input_file_dir = find_s3_prefix(self.params["input_file_dir"], os.environ.get("S3_BUCKET_NAME"))[0]
            self.params["input_file_dir"] = input_file_dir

            if "*" in self.params["input_file_name"]:
                input_file_name = find_s3_file(
                        input_file_dir,
                        self.params["input_file_name"],
                        os.environ.get("S3_BUCKET_NAME")
                    )
            else:
                input_file_name = self.params["input_file_name"]

            input_path = os.path.join(
                input_file_dir, input_file_name
            )    

            # DB接続
            self.connect_db(params["secret_name"])

            # トランザクション
            self.db_connector.begin()

            # フォーマットオプションのデフォルト値設定
            self.set_default_format_options()
            # DBデータテーブルインポート
            self.execute_import(input_file_name)
            # UPSERT
            self.execute_upsert(query_id=self.params["query_upsert"])

            self.db_connector.commit()

            # 終了処理
            # 正常終了ログ
            self.logger.info(
                msg_id="I_job_internal_db_import_002",
                msg_values=input_file_name,
            )

        # 例外処理
        except FileNotFoundException as e:
            if self.params.get("backup_flag"):
                self.params["backup_flag"] = False
            self.logger.info("I_file_not_found_001", (params.get("input_file_name", "Unknown"),))
            raise
        except Exception as e:
            # スタックトレース
            self.logger.error(
                msg_id="E_job_internal_db_import_003",
                msg_values=(str(e)),
            )

            # 異常終了ログ
            self.logger.error(
                msg_id="E_job_internal_db_import_001",
                msg_values=input_file_name,
            )
            # 例外オブジェクトログ出力
            self.logger.error_common(f"メイン処理: {str(e)}")
            raise

        finally:
            
            try:
                if input_file_name is not None:
                    # ファイルバックアップ
                    bk_path = os.path.join(params["backup_file_dir"], input_file_name)
                    if params["backup_flag"]:
                        self.backup_to_s3(in_path=input_path, bk_path=bk_path)

                    # インプットファイル削除
                    self.delete_file(delete_file=input_path)

            except Exception as e:
                # スタックトレース
                self.logger.error(
                    msg_id="E_job_internal_db_import_003",
                    msg_values=(str(e)),
                )

                # 異常終了ログ
                self.logger.error(
                    msg_id="E_job_internal_db_import_001",
                    msg_values=input_file_name,
                )
                # 例外オブジェクトログ出力
                self.logger.error_common(f"インプットファイルのバックアップと削除: {str(e)}")
                raise

            finally:
                # DBコネクションクローズ
                if db_connector:
                    db_connector.close()

def get_params():
    """
    パラメータ受取り
    Returns:
        Dict[str, Any]: 取得したパラメータ
    """
    # 必須パラメータのチェック
    params = get_job_params()

    required_params = [
        "input_format_options",  # フォーマットオプション
        "secret_name",  # Secrets Managerシークレット名
        "input_file_dir",  # インプットファイルディレクトリ
        "input_file_name",  # インプットファイル名
        "import_table",  # インポートテーブル名
        "jobnet_id",  # ジョブネットID
        "query_upsert",  # UPSERTクエリ
    ]
    for param in required_params:
        if param not in params:
            raise ValueError(f"Required parameter '{param}' is missing")

    # 任意パラメータのデフォルト値設定
    params["backup_flag"] = str_to_bool(params.get("backup_flag", "False"))

    # 入力チェック
    # バックアップフラグTrueの場合、バックアップファイルディレクトリがない場合はエラーとする
    if params.get("backup_flag") and not params.get("backup_file_dir"):
        raise ValueError(f"Required parameter 'backup_file_dir' is missing")

    # インポートテーブル名がワークテーブル出ない場合はエラーとする
    if not str(params["import_table"]).endswith("_work"):
        raise ValueError("インポートテーブル名はワークテーブルである必要があります。")

    return params


def main():
    """メイン関数"""
    try:
        # 環境変数の初期化
        initialize_env()

        # パラメータの取得
        params = get_params()

        # ジョブの実行
        job = GlueJobInternalDbImport(params["jobnet_id"])
        job.execute(params)

    except BaseException as e:
        print(f"Error occurred: {str(e)}")
        raise e


if __name__ == "__main__":
    main()
