SELECT
    input_no,
    input_sys_type,
    corp_cd,
    vote_employee_cd,
    vote_dept_cd,
    approval_employee_cd,
    TO_CHAR(approval_date, 'YYYYMMDD') AS approval_date,
    approval_status_type,
    journal_type,
    TO_CHAR(slip_date, 'YYYYMMDD') AS slip_date,
    slip_no,
    slip_ope_ban_type,
    journal_reference_type,
    input_unit_no,
    xml_db_seq_key,
    transfer_link_key,
    sys_reserve1,
    fixes_reason_code,
    business_code,
    form_code,
    order_top_char,
    order_item,
    order_other,
    wh_code,
    join_segment_code_1,
    join_segment_code_2,
    join_segment_code_3,
    counter_corp_cd,
    counter_corp_join_segment_code_1,
    counter_corp_join_segment_code_2,
    counter_corp_join_segment_code_3,
    TO_CHAR(slip_user_open_date_1, 'YYYYMMDD') AS slip_user_open_date_1,
    slip_user_open_code_1,
    slip_user_open_code_2,
    sys_reserve2,
    slip_remarks,
    approval_remarks,
    slip_user_open_area,
    slip_user_open_area_2,
    line_num,
    slip_detail_lending_type,
    account_code,
    accounting_dept_code,
    details_type,
    details_code,
    items_type,
    items_code,
    count_ext_code_1,
    count_ext_code_1_type,
    count_ext_code_2,
    count_ext_code_2_type,
    count_ext_code_3,
    count_ext_code_3_type,
    count_ext_code_4,
    count_ext_code_4_type,
    count_ext_code_5,
    count_ext_code_5_type,
    search_ext_code_1,
    search_ext_code_1_type,
    search_ext_code_2,
    search_ext_code_2_type,
    search_ext_code_3,
    search_ext_code_3_type,
    search_ext_code_4,
    search_ext_code_4_type,
    search_ext_code_5,
    search_ext_code_5_type,
    business_partner_code,
    segment_code,
    cost_burden_center_code,
    bill_cash_code,
    business_segment_code,
    region_segment_code,
    customer_segment_code,
    user_open_segment_code_1,
    user_open_segment_code_2,
    match_key,
    tran_currency_code,
    tran_currency_exchange_rate_type,
    tran_currency_rate,
    view_currency_exchange_rate_type_1,
    view_currency_rate_1,
    view_currency_exchange_rate_type_2,
    view_currency_rate_2,
    view_currency_exchange_rate_type_3,
    view_currency_rate_3,
    funding_code,
    tax_type_code,
    sys_reserve3,
    tax_rate_type,
    function_currency_amout,
    tran_currency_amout,
    reference_tax,
    user_open_num_1,
    tax_type,
    history_property_code,
    counter_account_code,
    sys_reserve4,
    sys_reserve5,
    sys_reserve6,
    sys_reserve7,
    sys_reserve8,
    sys_reserve9,
    sys_reserve10,
    sys_reserve11,
    sys_reserve12,
    sys_reserve13,
    sys_reserve14,
    sys_reserve15,
    sys_reserve16,
    sys_reserve17,
    sys_reserve18,
    sys_reserve19,
    sys_reserve20,
    sys_reserve21,
    sys_reserve22,
    sys_reserve23,
    sys_reserve24,
    sys_reserve25,
    sys_reserve26,
    sys_reserve27,
    sys_reserve28,
    quantity,
    unit_cd,
    quantity_sub,
    unit_cd_sub,
    function_currency_price,
    tran_currency_price,
    ext_num_1,
    ext_num_2,
    ext_num_3,
    TO_CHAR(user_open_date_1, 'YYYYMMDD') AS user_open_date_1,
    user_open_code_1,
    user_open_code_2,
    user_open_code_3,
    user_open_code_4,
    user_open_code_5,
    user_open_code_6,
    user_open_code_7,
    user_open_area_1,
    sys_reserve29,
    sys_reserve30,
    user_open_area_2,
    user_open_area_3,
    user_open_code_8,
    user_open_area_5,
    user_open_area_6,
    user_open_area_7,
    user_open_area_8,
    sys_reserve31,
    TO_CHAR(user_open_date_2, 'YYYYMMDD') AS user_open_date_2,
    text_description_bill_remarks,
    detail_user_open_area,
    detail_user_open_area_2,
    individual_application_key,
    recovery_payment_dept_code,
    contract_no,
    invoice_no,
    TO_CHAR(recovery_payment_schedule_date, 'YYYYMMDD') AS recovery_payment_schedule_date,
    TO_CHAR(bill_cash_closing_date, 'YYYYMMDD') AS bill_cash_closing_date,
    upd_sub_sys_type,
    property_control_number,
    bill_no,
    bill_kind_type,
    bill_type,
    transition_type,
    TO_CHAR(bill_cash_settlement_date, 'YYYYMMDD') AS bill_cash_settlement_date,
    bill_split_type_sys_reserve,
    TO_CHAR(effort_payment_advice_date, 'YYYYMMDD') AS effort_payment_advice_date,
    TO_CHAR(cash_schedule_date, 'YYYYMMDD') AS cash_schedule_date,
    bill_site,
    sys_reserve32,
    sys_reserve33,
    bank_account_holder,
    payment_place_counter_bank_code,
    payment_place,
    bill_effort_company_bank_code,
    bill_discount_fee,
    telegraph_document_transfer_type,
    fee_burden_type,
    fb_transfer_process_type,
    company_bank_account_type,
    company_bank_account_no,
    counter_bank_account_type,
    counter_bank_account_no,
    sys_reserve34,
    sys_reserve35
FROM
    accounting_data_mail_order
WHERE
    d_updated_datetime > :sync_datetime
    AND d_updated_datetime <= :diff_base_timestamp;
