-- sync_timestampのtempに基準日時を入れる
INSERT INTO sync_timestamp
  (job_schedule_id, file_name, sync_datetime, sync_datetime_temp, d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
VALUES
  ('JN_CP001-DF01_001', 'txCampaignPricebooks.xml', '1900-01-01 0:00:00', NOW(), 'JN_CP001-DF01_001', NOW(), 'JN_CP001-DF01_001', NOW(), 0)
  ,('JN_CP001-DF01_001', 'txCampaignPromotions.xml', '1900-01-01 0:00:00', NOW(), 'JN_CP001-DF01_001', NOW(), 'JN_CP001-DF01_001', NOW(), 0)
  ,('JN_CP001-DF01_001', 'txDynamicCustomerGroups.xml', '1900-01-01 0:00:00', NOW(), 'JN_CP001-DF01_001', NOW(), 'JN_CP001-DF01_001', NOW(), 0)
  ,('JN_CP001-DF01_001', 'txStaticCustomerGroups.xml', '1900-01-01 0:00:00', NOW(), 'JN_CP001-DF01_001', NOW(), 'JN_CP001-DF01_001', NOW(), 0)
ON CONFLICT (job_schedule_id, file_name)
DO UPDATE SET
  sync_datetime_temp = NOW()
  ,d_updated_user = 'JN_CP001-DF01_001'
  ,d_updated_datetime = NOW();
--ワークテーブルのTRUNCATE
TRUNCATE TABLE wk_CP001_DF01_updated_campaign_promotion_list;
TRUNCATE TABLE wk_CP001_DF01_campaign_export;
TRUNCATE TABLE wk_CP001_DF01_product_campaign_flag_list;
TRUNCATE TABLE wk_cp001_df01_campaign_main;
TRUNCATE TABLE wk_cp001_df01_promotion_main;
TRUNCATE TABLE wk_cp001_df01_campaign_promotion_assignment_main;
TRUNCATE TABLE wk_cp001_df01_updated_campaign;
TRUNCATE TABLE wk_cp001_df01_updated_promotion;
-- 最初にキャンペーンとプロモーションの出力テーブルに更新されたキャンペーンコードを削除扱いで入れる
-- 連携対象のものはあとからUPDATEする
-- キャンペーン
INSERT INTO wk_cp001_df01_updated_campaign (campaign_instructions_code)
SELECT
  campaign_instructions_code
FROM campaign_instructions --キャンペーン設定
WHERE
  d_updated_datetime > :sync_datetime
UNION ALL 
SELECT
  campaign_instructions_code
FROM campaign_order_view --キャンペーン設定条件
WHERE
  d_updated_datetime > :sync_datetime
--最後にUNIONで重複削除
UNION
SELECT
  campaign_instructions_code
FROM campaign_order_group_view --キャンペーン設定条件グループ
WHERE
  d_updated_datetime > :sync_datetime;
-- プロモーション
INSERT INTO wk_cp001_df01_updated_promotion (campaign_instructions_code)
SELECT
  campaign_instructions_code
FROM campaign_instructions --キャンペーン設定
WHERE
  d_updated_datetime > :sync_datetime
UNION ALL 
SELECT
  campaign_instructions_code
FROM campaign_promotion_view --キャンペーンプロモーション
WHERE
  d_updated_datetime > :sync_datetime
UNION ALL 
SELECT
  campaign_instructions_code
FROM campaign_order_view --キャンペーン設定条件
WHERE
  d_updated_datetime > :sync_datetime
UNION ALL 
SELECT
  campaign_instructions_code
FROM campaign_order_group_view --キャンペーン設定条件グループ
WHERE
  d_updated_datetime > :sync_datetime
UNION ALL
SELECT
  campaign_instructions_code
FROM campaign_customer_view --顧客グループ
WHERE
  d_updated_datetime > :sync_datetime
--最後にUNIONで重複削除
UNION
SELECT
  campaign_instructions_code
FROM campaign_instructions_commodity_view --キャンペーン設定商品
WHERE
  d_updated_datetime > :sync_datetime;
INSERT INTO wk_cp001_df01_campaign_main (campaign_instructions_code, campaign_instructions_name, enabled_flag, campaign_start_date, campaign_end_date, customer_groups_group_id, campaign_applied_scope, campaign_exist_flg)
SELECT 
  campaign_instructions_code
  ,'deleted_campaign'
  ,'false'
  ,'2000-01-01 00:00:00'
  ,'2000-01-01 00:00:00'
  ,'Everyone'
  ,'00'
  ,'0'
FROM wk_cp001_df01_updated_campaign;
-- プロモーション
INSERT INTO wk_cp001_df01_promotion_main (campaign_instructions_code, promotion_id, name, promotion_type, first_order_only, campaign_end_date, preferential_product_flg, discount_amount_amount, discount_rate_amount, pricebook_id, campaign_exist_flg)
SELECT 
  up.campaign_instructions_code
  ,up.campaign_instructions_code || '_' || cp.promotion_no
  ,'deleted_campaign'
  ,'00'
  ,'false'
  ,'2000-01-01 00:00:00'
  ,'false'
  ,'0'
  ,'0'
  ,'delete'
  ,'0'
FROM wk_cp001_df01_updated_promotion AS up
INNER JOIN campaign_promotion_view AS cp ON
  up.campaign_instructions_code = cp.campaign_instructions_code;
-- 更新されたキャンペーンがEC向けもしくは全体向けか確認
WITH target_promotion_work AS (
  SELECT
    ci.campaign_instructions_code
    ,CASE
      WHEN MAX(CASE WHEN co.joken_kind2 = '304' AND co.joken = '21' THEN 1 ELSE 0 END) = 1 THEN 1 --1ならEC向け
      WHEN MAX(CASE WHEN co.joken_kind2 = '304' AND (co.delete_flg = 0 AND cog.delete_flg = 0) THEN 1 ELSE 0 END) = 0 THEN 1 --1なら全体向け
      ELSE 0
    END AS ec_or_all_flg
    ,CASE
      WHEN MAX(CASE WHEN co.joken_kind2 = '304' AND co.joken = '21' AND (co.delete_flg = 0 AND cog.delete_flg = 0) THEN 1 ELSE 0 END) = 1 THEN 0
      WHEN MAX(CASE WHEN co.joken_kind2 = '304' AND (co.delete_flg = 0 AND cog.delete_flg = 0) THEN 1 ELSE 0 END) = 0 THEN 0
      ELSE 1
    END AS delete_flg --キャンペーン設定条件の削除によって連携対象外になった場合1、そうでない場合0
  FROM wk_cp001_df01_updated_promotion AS up
  INNER JOIN campaign_instructions AS ci ON --キャンペーン設定
    up.campaign_instructions_code = ci.campaign_instructions_code
  INNER JOIN campaign_order_view AS co ON --キャンペーン設定条件
    ci.campaign_instructions_code = co.campaign_instructions_code
  INNER JOIN campaign_order_group_view AS cog ON --キャンペーン設定条件グループ
    co.campaign_instructions_code = cog.campaign_instructions_code
    AND co.campaign_group_no = cog.campaign_group_no
  WHERE ci.campaign_applied_scope = '01' --単商品明細単位
  GROUP BY ci.campaign_instructions_code
)
-- ↑からEC向けもしくは全体向けのキャンペーンを取得
,target_promotion_work2 AS (
  SELECT
    campaign_instructions_code
    ,delete_flg
  FROM target_promotion_work
  WHERE ec_or_all_flg = 1
)
-- ↑のうちキャンペーン連携対象のもの
,target_promotion_ec AS (
  SELECT
    tpw.campaign_instructions_code
    ,MIN(CASE WHEN tpw.delete_flg = 1 OR co.delete_flg = 1 OR cog.delete_flg = 1 THEN 1 ELSE 0 END) AS delete_flg
  FROM target_promotion_work2 AS tpw
  INNER JOIN campaign_order_view AS co ON
    tpw.campaign_instructions_code = co.campaign_instructions_code
    AND (
        co.joken_kind1 = '1' --会員
      OR 
        co.joken_kind1 = '2' --購入商品
      OR 
        (co.joken_kind1 = '3' AND co.joken_kind2 = '302') --受注かつ初回購入
    )
  INNER JOIN campaign_order_group_view AS cog ON
    co.campaign_instructions_code = cog.campaign_instructions_code
    AND co.campaign_group_no = cog.campaign_group_no
  GROUP BY tpw.campaign_instructions_code
)
--連携対象のキャンペーンコードリスト
--キャンペーン価格、静的顧客、動的顧客の連携フラグ付き
INSERT INTO wk_CP001_DF01_updated_campaign_promotion_list (campaign_instructions_code, campaign_exist_flg, campaign_price_flg, static_customer_flg, static_customer_exist_flg, dynamic_customer_flg, dynamic_customer_exist_flg)
SELECT 
  tpe.campaign_instructions_code
  ,CASE WHEN tpe.delete_flg = 1 THEN '0' ELSE '1' END
  ,CASE 
    WHEN tpe.delete_flg = 1 THEN '0' --連携対象外キャンペーンの価格表は連携しない(EC側で自動で削除されるため)
    WHEN MAX(CASE WHEN cp.campaign_instructions_code IS NOT NULL THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END
  ,CASE
    WHEN MAX(CASE WHEN co.joken_kind1 = '1' AND co.joken_kind2 = '102' THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END
  ,CASE
    WHEN tpe.delete_flg = 1 THEN '0' --連携対象外キャンペーンの静的顧客は削除する
    WHEN MAX(CASE WHEN co.joken_kind1 = '1' AND co.joken_kind2 = '102' AND co.delete_flg = 0 AND cog.delete_flg = 0 THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END
  ,CASE 
    WHEN MAX(CASE WHEN co.joken_kind1 = '1' AND co.joken_kind2 = '101' THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END
  ,CASE 
    WHEN tpe.delete_flg = 1 THEN '0' --連携対象外キャンペーンの動的顧客は削除する
    WHEN MAX(CASE WHEN co.joken_kind1 = '1' AND co.joken_kind2 = '101' AND co.delete_flg = 0 AND cog.delete_flg = 0 THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END
FROM target_promotion_ec AS tpe
--キャンペーン価格連携フラグの判定
LEFT OUTER JOIN campaign_promotion_view AS cp ON
  tpe.campaign_instructions_code = cp.campaign_instructions_code
  AND cp.promotion_type = '03'
  AND cp.discount_retail_price IS NOT NULL
  AND cp.delete_flg = 0
--静的顧客、動的顧客の連携対象か判定
INNER JOIN campaign_order_view AS co ON 
  tpe.campaign_instructions_code = co.campaign_instructions_code
INNER JOIN campaign_order_group_view AS cog ON 
  co.campaign_instructions_code = cog.campaign_instructions_code
  AND co.campaign_group_no = cog.campaign_group_no
GROUP BY tpe.campaign_instructions_code, tpe.delete_flg;
-- キャンペーン出力対象(wk_CP001_DF01_updated_campaign_promotion_listとwk_cp001_df01_updated_campaignの両方に存在するもの)の抽出
WITH target_campaign_ec AS (
  SELECT
    ucpl.campaign_instructions_code
    ,ucpl.campaign_exist_flg
    ,co.joken_kind2
    ,co.joken
  FROM
    wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
  INNER JOIN campaign_order_view AS co ON --キャンペーン設定条件
    ucpl.campaign_instructions_code = co.campaign_instructions_code
  INNER JOIN wk_cp001_df01_updated_campaign AS uc ON --キャンペーン設定条件グループ
    co.campaign_instructions_code = uc.campaign_instructions_code
)
-- 連携対象のキャンペーンをexportテーブルに登録
-- キャンペーンの種別（会員や商品など）がマッピングの条件となっているため、FLGで条件分岐できるようにする
INSERT INTO
  wk_CP001_DF01_campaign_export (campaign_instructions_code,campaign_exist_flg,campaign_customer_flg)
SELECT
  campaign_instructions_code
  ,campaign_exist_flg
  ,CAST(MAX(campaign_customer_flg) AS VARCHAR)
FROM (
  SELECT
    tce.campaign_instructions_code
    ,tce.campaign_exist_flg
    ,CASE WHEN co.joken_kind1 = '1' AND co.delete_flg = 0 THEN 1 ELSE 0 END AS campaign_customer_flg
  FROM
    target_campaign_ec AS tce
  INNER JOIN campaign_order_view AS co ON
    tce.campaign_instructions_code = co.campaign_instructions_code
) AS tcew
GROUP BY campaign_instructions_code, campaign_exist_flg;
-- キャンペーンのメインクエリ
UPDATE wk_cp001_df01_campaign_main AS dcm
SET
  campaign_instructions_name = ci.campaign_instructions_name
  ,enabled_flag = CASE
    WHEN ci.delete_flg = 0 THEN 'true'
    ELSE 'false'
  END
  ,campaign_start_date = ci.campaign_start_date
  ,campaign_end_date = ci.campaign_end_date
  ,customer_groups_group_id = CASE
    WHEN ce.campaign_customer_flg = '1' THEN 'txGroupId' || ce.campaign_instructions_code
    ELSE 'Everyone'
  END
  ,campaign_applied_scope = ci.campaign_applied_scope
  ,campaign_exist_flg = ce.campaign_exist_flg
FROM wk_cp001_df01_campaign_export AS ce
INNER JOIN campaign_instructions AS ci ON
  ce.campaign_instructions_code = ci.campaign_instructions_code
WHERE dcm.campaign_instructions_code = ce.campaign_instructions_code;
-- キャンペーン対象商品が優待商品かどうかの判定(単商品の場合)
-- 単商品の場合の商品コードはcampaign_order_view.jokenに設定される
INSERT INTO wk_CP001_DF01_product_campaign_flag_list (campaign_instructions_code, preferential_product_flg, single_product_flg)
SELECT
  co.campaign_instructions_code
  ,CASE
    WHEN MAX(CASE WHEN pl.preferential_product_flg = '1' THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END AS preferential_product_flg
  ,'1'
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_order_view AS co ON
  ucpl.campaign_instructions_code = co.campaign_instructions_code
  AND ucpl.campaign_exist_flg = '1'
  AND co.delete_flg = 0
  AND co.joken_kind1 = '2' 
  AND co.joken_kind2 = '201'
LEFT OUTER JOIN mdm.product_linkage AS pl ON
  co.joken = pl.mail_order_product_cd
GROUP BY co.campaign_instructions_code;
-- キャンペーン対象商品が優待商品かどうかの判定(複数商品の場合)
-- 複数商品の場合の商品コードはcampaign_instructions_commodity_view.commodity_codeに設定される
INSERT INTO wk_CP001_DF01_product_campaign_flag_list (campaign_instructions_code, preferential_product_flg, single_product_flg)
SELECT
  co.campaign_instructions_code
  ,CASE
    WHEN MAX(CASE WHEN pl.preferential_product_flg = '1' THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END AS preferential_product_flg
  ,'0'
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_order_view AS co ON
  ucpl.campaign_instructions_code = co.campaign_instructions_code
  AND ucpl.campaign_exist_flg = '1'
  AND co.delete_flg = 0
  AND co.joken_kind1 = '2'
  AND co.joken_kind2 = '205'
INNER JOIN campaign_instructions_commodity_view AS cic ON
  co.campaign_instructions_code = cic.campaign_instructions_code
  AND cic.delete_flg = 0
LEFT OUTER JOIN mdm.product_linkage AS pl ON
  cic.commodity_code = pl.mail_order_product_cd
GROUP BY co.campaign_instructions_code;
-- プロモーションのメインクエリ
WITH first_order_only_sub_flg_list AS (
  SELECT
    ucpl.campaign_instructions_code
    ,MAX(CASE WHEN co.joken_kind1 = '3' AND co.joken_kind2 = '302' AND co.delete_flg = 0 THEN 1 ELSE 0 END) AS txIsFirstOrderOnlySubscription
  FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
  INNER JOIN campaign_order_view AS co ON
    ucpl.campaign_instructions_code = co.campaign_instructions_code
  GROUP BY ucpl.campaign_instructions_code
)
UPDATE wk_cp001_df01_promotion_main AS dpm
SET
  name = ci.campaign_instructions_name
  ,promotion_type = cp.promotion_type
  ,present_product_code = CASE 
    WHEN cp.promotion_type = '01' THEN cp.commodity_code
    ELSE NULL
  END
  ,first_order_only = CASE 
    WHEN fo.txIsFirstOrderOnlySubscription = 1 THEN 'true'
    ELSE 'false'
  END
  ,discount_rate = cp.discount_rate
  ,oneshot_order_limit = ci.oneshot_order_limit
  ,campaign_quantity_limit = ci.campaign_quantity_limit
  ,campaign_end_date = ci.campaign_end_date
  ,preferential_product_flg = CASE
    WHEN pcfl.preferential_product_flg = '1' THEN 'true'
    ELSE 'false'
  END
  ,baitai_code = ci.baitai_code
  ,campaign_priority = ci.campaign_priority
  ,discount_amount = cp.discount_amount
  ,discount_retail_price = cp.discount_retail_price
  ,single_product_flg = pcfl.single_product_flg
  ,discount_amount_amount = CAST(COALESCE(cp.discount_amount, 0) AS VARCHAR) || '.0'
  ,discount_rate_amount = CAST(COALESCE(cp.discount_rate, 0) AS VARCHAR) || '.0'
  ,pricebook_id = 'txCampaignPricebookId' || ci.campaign_instructions_code
  ,campaign_exist_flg = ucpl.campaign_exist_flg
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_instructions AS ci ON
  ucpl.campaign_instructions_code = ci.campaign_instructions_code
INNER JOIN campaign_promotion_view AS cp ON
  ci.campaign_instructions_code = cp.campaign_instructions_code
  AND cp.promotion_type IN ('01',  '02', '03')
  AND cp.delete_flg = 0
INNER JOIN first_order_only_sub_flg_list AS fo ON
  cp.campaign_instructions_code = fo.campaign_instructions_code
LEFT OUTER JOIN wk_CP001_DF01_product_campaign_flag_list AS pcfl ON
  ci.campaign_instructions_code = pcfl.campaign_instructions_code
WHERE dpm.campaign_instructions_code = ci.campaign_instructions_code
  AND dpm.promotion_id = ci.campaign_instructions_code || '_' || cp.promotion_no;
--キャンペーンプロモーションのサブクエリ(キャンペーン設定条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT
        co.campaign_instructions_code
        ,jsonb_build_object(
          ''campaign_instructions_code''
          ,co.campaign_instructions_code
          ,''campaign_group_no''
          ,co.campaign_group_no
          ,''joken_type''
          ,co.joken_type
          ,''campaign_joken_no''
          ,co.campaign_joken_no
          ,''joken_kind1''
          ,co.joken_kind1
          ,''joken_kind2''
          ,co.joken_kind2
          ,''joken''
          ,co.joken
        ) AS campaign_condition_group
      FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
      INNER JOIN campaign_order_view AS co ON
        ucpl.campaign_instructions_code = co.campaign_instructions_code
        AND co.delete_flg = 0
      INNER JOIN campaign_order_group_view AS cog ON
        co.campaign_instructions_code = cog.campaign_instructions_code
        AND co.campaign_group_no = cog.campaign_group_no
        AND cog.delete_flg = 0
      ORDER BY co.campaign_instructions_code, co.campaign_group_no, co.campaign_joken_no;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pro_order_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--キャンペーンプロモーションのサブクエリ(値引きプロモーションルール(単商品))
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT 
        co.campaign_instructions_code
        ,co.joken
      FROM campaign_order_view AS co
      INNER JOIN campaign_order_group_view AS cog ON
        co.campaign_instructions_code = cog.campaign_instructions_code
        AND co.campaign_group_no = cog.campaign_group_no
        AND cog.delete_flg = 0
      WHERE joken_kind1 = ''2'' 
        AND joken_kind2 = ''201''
        AND co.delete_flg = 0
      ORDER BY co.campaign_instructions_code;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pro_single_disc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--キャンペーンプロモーションのサブクエリ(値引きプロモーションルール(複数商品))
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT 
        campaign_instructions_code
        ,commodity_code
      FROM campaign_instructions_commodity_view
      WHERE delete_flg = 0
      ORDER BY campaign_instructions_code, commodity_code;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pro_multi_disc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--プロモーション割り当て情報のメインクエリ
INSERT INTO wk_cp001_df01_campaign_promotion_assignment_main (promotion_id, campaign_instructions_code)
SELECT 
  promotion_id
  ,campaign_instructions_code
FROM wk_cp001_df01_promotion_main
WHERE campaign_exist_flg = '1';
-- ワークテーブルに分割単位を入れる
-- プロモーション
WITH temp_parallel_num AS (
  SELECT
     campaign_instructions_code
    ,MOD(ROW_NUMBER() OVER (ORDER BY campaign_instructions_code) - 1, :parallel_num) + 1 AS parallel_num
  FROM (SELECT DISTINCT campaign_instructions_code FROM wk_cp001_df01_promotion_main)
)
UPDATE wk_cp001_df01_promotion_main AS pm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE pm.campaign_instructions_code = t.campaign_instructions_code;
--分割単位の設定
-- キャンペーン
-- プロモーションが存在する場合、プロモーションの分割単位と合わせる
UPDATE wk_cp001_df01_campaign_main AS cm
SET split_num = pm.split_num
FROM wk_cp001_df01_promotion_main AS pm
WHERE cm.campaign_instructions_code = pm.campaign_instructions_code;
-- プロモーションが存在しないキャンペーンに分割単位を設定する
WITH temp_parallel_num AS (
  SELECT
     campaign_instructions_code
    ,MOD(ROW_NUMBER() OVER (ORDER BY campaign_instructions_code) - 1, :parallel_num) + 1 AS parallel_num
  FROM (SELECT campaign_instructions_code FROM wk_cp001_df01_campaign_main WHERE split_num IS NULL)
)
UPDATE wk_cp001_df01_campaign_main AS cm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE cm.campaign_instructions_code = t.campaign_instructions_code;
-- プロモーション割り当て
UPDATE wk_cp001_df01_campaign_promotion_assignment_main AS pam
SET split_num = pm.split_num
FROM wk_cp001_df01_promotion_main AS pm
WHERE pam.campaign_instructions_code = pm.campaign_instructions_code;