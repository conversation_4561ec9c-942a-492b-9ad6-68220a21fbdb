TRUNCATE TABLE wk_cp001_df01_pricebook_main;
--価格表のメインクエリ
INSERT INTO wk_cp001_df01_pricebook_main (campaign_instructions_code, pricebook_id, campaign_instructions_name, campaign_start_date, campaign_end_date, single_product_flg)
SELECT DISTINCT ON (ucpl.campaign_instructions_code)
  ucpl.campaign_instructions_code
  ,'txCampaignPricebookId' || cp.campaign_instructions_code
  ,ci.campaign_instructions_name
  ,ci.campaign_start_date
  ,ci.campaign_end_date
  ,pcfl.single_product_flg
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_instructions AS ci ON
  ucpl.campaign_price_flg = '1'
  AND ucpl.campaign_instructions_code = ci.campaign_instructions_code
INNER JOIN campaign_promotion_view AS cp ON 
  ci.campaign_instructions_code = cp.campaign_instructions_code
LEFT OUTER JOIN wk_cp001_df01_product_campaign_flag_list AS pcfl ON
  cp.campaign_instructions_code = pcfl.campaign_instructions_code
WHERE cp.promotion_type = '03'
  AND cp.discount_retail_price IS NOT NULL
  AND cp.delete_flg = 0;
--価格表のサブクエリ(単商品)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT ON (ucpl.campaign_instructions_code, co.joken)
      ucpl.campaign_instructions_code,
      co.joken,
      cp.discount_retail_price
    FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
    INNER JOIN campaign_promotion_view AS cp ON 
      ucpl.campaign_price_flg = ''1''
      AND ucpl.campaign_instructions_code = cp.campaign_instructions_code
      AND cp.delete_flg = 0
      AND cp.promotion_type = ''03''
      AND cp.discount_retail_price IS NOT NULL
    INNER JOIN campaign_order_view AS co ON 
      cp.campaign_instructions_code = co.campaign_instructions_code
      AND co.delete_flg = 0
      AND co.joken_kind1 = ''2'' 
      AND co.joken_kind2 = ''201''
    INNER JOIN campaign_order_group_view AS cog ON 
      co.campaign_instructions_code = cog.campaign_instructions_code
      AND co.campaign_group_no = cog.campaign_group_no
      AND cog.delete_flg = 0
    ORDER BY ucpl.campaign_instructions_code;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pri_single_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--価格表のサブクエリ(複数商品)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT
      ucpl.campaign_instructions_code,
      cic.commodity_code,
      cp.discount_retail_price
    FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
    INNER JOIN campaign_promotion_view AS cp ON 
      ucpl.campaign_price_flg = ''1''
      AND ucpl.campaign_instructions_code = cp.campaign_instructions_code
      AND cp.promotion_type = ''03''
      AND cp.discount_retail_price IS NOT NULL
      AND cp.delete_flg = 0
    INNER JOIN campaign_instructions_commodity_view AS cic ON
      cp.campaign_instructions_code = cic.campaign_instructions_code
      AND cic.delete_flg = 0
    ORDER BY ucpl.campaign_instructions_code, cic.commodity_code;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pri_multi_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--分割単位の設定
-- 価格表
WITH temp_parallel_num AS (
  SELECT
     campaign_instructions_code
    ,MOD(ROW_NUMBER() OVER (ORDER BY campaign_instructions_code) - 1, :parallel_num) + 1 AS parallel_num
  FROM (SELECT DISTINCT campaign_instructions_code FROM wk_cp001_df01_pricebook_main)
)
UPDATE wk_cp001_df01_pricebook_main AS pm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE pm.campaign_instructions_code = t.campaign_instructions_code;