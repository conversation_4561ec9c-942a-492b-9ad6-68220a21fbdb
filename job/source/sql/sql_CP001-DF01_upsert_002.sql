WITH source_data AS (
    SELECT campaign_instructions_code,
        customer_code,
        joken_type,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    FROM dlpf.campaign_customer_view_work
)
INSERT INTO dlpf.campaign_customer_view (
        campaign_instructions_code,
        customer_code,
        joken_type,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_CP001-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_CP001-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (campaign_instructions_code, customer_code) DO
UPDATE
SET joken_type = EXCLUDED.joken_type,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    delete_flg = EXCLUDED.delete_flg,
    d_updated_user = 'JN_CP001-DF01_001',
    d_updated_datetime = NOW(),
    d_version = campaign_customer_view.d_version + 1;
