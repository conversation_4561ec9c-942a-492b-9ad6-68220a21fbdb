WITH source_data AS (
    SELECT campaign_instructions_code,
        campaign_group_no,
        campaign_joken_disp,
        exclude_joken_disp,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg
    FROM dlpf.campaign_order_group_view_work
)
INSERT INTO dlpf.campaign_order_group_view (
        campaign_instructions_code,
        campaign_group_no,
        campaign_joken_disp,
        exclude_joken_disp,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_CP001-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_CP001-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (campaign_instructions_code, campaign_group_no) DO
UPDATE
SET campaign_joken_disp = EXCLUDED.campaign_joken_disp,
    exclude_joken_disp = EXCLUDED.exclude_joken_disp,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    delete_flg = EXCLUDED.delete_flg,
    d_updated_user = 'JN_CP001-DF01_001',
    d_updated_datetime = NOW(),
    d_version = campaign_order_group_view.d_version + 1;
