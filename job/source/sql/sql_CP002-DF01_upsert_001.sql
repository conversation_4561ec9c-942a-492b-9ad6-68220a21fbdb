INSERT INTO coupon_view (
        coupon_management_code,
        coupon_code,
        coupon_name,
        coupon_invalid_flag,
        coupon_type,
        coupon_issue_type,
        coupon_use_limit,
        coupon_use_purchase_price,
        coupon_discount_type,
        coupon_discount_rate,
        coupon_discount_price,
        coupon_start_datetime,
        coupon_end_datetime,
        coupon_limit_display_period,
        coupon_limit_display,
        coupon_description,
        coupon_message,
        coupon_kbn,
        coupon_post_in_charge,
        coupon_commodity_flag,
        marketing_channel_list,
        goods_group,
        commodity_series,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version,
        delete_flg
    )
SELECT coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_invalid_flag,
    coupon_type,
    coupon_issue_type,
    coupon_use_limit,
    coupon_use_purchase_price,
    coupon_discount_type,
    coupon_discount_rate,
    coupon_discount_price,
    coupon_start_datetime,
    coupon_end_datetime,
    coupon_limit_display_period,
    coupon_limit_display,
    coupon_description,
    coupon_message,
    coupon_kbn,
    coupon_post_in_charge,
    coupon_commodity_flag,
    marketing_channel_list,
    goods_group,
    commodity_series,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_CP002-DF01_001' AS d_created_user,
    NOW() AS d_created_datetime,
    'JN_CP002-DF01_001' AS d_updated_user,
    NOW() AS d_updated_datetime,
    1 AS d_version,
    delete_flg
FROM coupon_view_work ON CONFLICT (coupon_management_code) DO
UPDATE
SET coupon_code = EXCLUDED.coupon_code,
    coupon_name = EXCLUDED.coupon_name,
    coupon_invalid_flag = EXCLUDED.coupon_invalid_flag,
    coupon_type = EXCLUDED.coupon_type,
    coupon_issue_type = EXCLUDED.coupon_issue_type,
    coupon_use_limit = EXCLUDED.coupon_use_limit,
    coupon_use_purchase_price = EXCLUDED.coupon_use_purchase_price,
    coupon_discount_type = EXCLUDED.coupon_discount_type,
    coupon_discount_rate = EXCLUDED.coupon_discount_rate,
    coupon_discount_price = EXCLUDED.coupon_discount_price,
    coupon_start_datetime = EXCLUDED.coupon_start_datetime,
    coupon_end_datetime = EXCLUDED.coupon_end_datetime,
    coupon_limit_display_period = EXCLUDED.coupon_limit_display_period,
    coupon_limit_display = EXCLUDED.coupon_limit_display,
    coupon_description = EXCLUDED.coupon_description,
    coupon_message = EXCLUDED.coupon_message,
    coupon_kbn = EXCLUDED.coupon_kbn,
    coupon_post_in_charge = EXCLUDED.coupon_post_in_charge,
    coupon_commodity_flag = EXCLUDED.coupon_commodity_flag,
    marketing_channel_list = EXCLUDED.marketing_channel_list,
    goods_group = EXCLUDED.goods_group,
    commodity_series = EXCLUDED.commodity_series,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_CP002-DF01_001',
    d_updated_datetime = NOW(),
    d_version = coupon_view.d_version + 1,
    delete_flg = EXCLUDED.delete_flg;