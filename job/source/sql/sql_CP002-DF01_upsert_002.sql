INSERT INTO coupon_commodity_view (
    coupon_management_code,
    shop_code,
    commodity_code,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version,
    delete_flg
)
SELECT
    coupon_management_code,
    shop_code,
    commodity_code,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_CP002-DF01_001' AS d_created_user,
    NOW() AS d_created_datetime,
    'JN_CP002-DF01_001' AS d_updated_user,
    NOW() AS d_updated_datetime,
    1 AS d_version,
    delete_flg
FROM coupon_commodity_view_work
ON CONFLICT (coupon_management_code, shop_code, commodity_code) 
DO UPDATE SET
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_CP002-DF01_001',
    d_updated_datetime = NOW(),
    d_version = coupon_commodity_view.d_version + 1,
    delete_flg = EXCLUDED.delete_flg;