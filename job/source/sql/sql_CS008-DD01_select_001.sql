SELECT customer_code,
    customer_no,
    customer_group_code,
    last_name,
    first_name,
    last_name_kana,
    first_name_kana,
    login_id,
    email,
    password,
    birth_date,
    sex,
    request_mail_type,
    client_mail_type,
    caution,
    login_datetime,
    login_error_count,
    login_locked_flg,
    customer_status,
    customer_valid_status,
    customer_attribute_reply_date,
    latest_point_acquired_date,
    rest_point,
    temporary_point,
    withdrawal_request_date,
    withdrawal_date,
    auth_secret_key,
    customer_type,
    black_customer_kbn,
    black_reason_kbn,
    black_register_date,
    mail_advisability_flg,
    bd_advisability_flg,
    mail_magazine_flg,
    shipped_mail_flg,
    receipt_to,
    receipt_detail,
    demand_exclude_flg,
    crm_customer_id,
    unity_customer_code,
    unity_datetime,
    niyose_flg,
    shipping_method_kbn,
    ec_login_id,
    guest_flg,
    member_no,
    member_status,
    shortage_declare_flg,
    order_monitor_flg,
    member_memo,
    crm_customer_updated_datetime,
    member_rank,
    order_ng_flg,
    free_only_purchase_flg,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM customer
where updated_datetime > :sync_datetime
    AND updated_datetime <= :diff_base_timestamp;
