SELECT
    coupon_management_code ,
    customer_code ,
    neo_customer_no ,
    coupon_issue_status ,
    coupon_used_count ,
    TO_CHAR(coupon_used_date, 'YYYY-MM-DD') as coupon_used_date ,
    baitai_code ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime ,
    delete_flg
FROM
    coupon_customer_view
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;
