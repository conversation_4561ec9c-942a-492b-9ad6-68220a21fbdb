SELECT
    shipping_no ,
    shipping_detail_no ,
    shop_code ,
    sku_code ,
    unit_price ,
    discount_price ,
    discount_amount ,
    retail_price ,
    retail_tax_group_code ,
    retail_tax_no ,
    retail_tax_rate ,
    retail_tax ,
    purchasing_amount ,
    gift_code ,
    gift_name ,
    gift_price ,
    gift_tax_group_code ,
    gift_tax_no ,
    gift_tax_rate ,
    gift_tax ,
    gift_tax_type ,
    noshi_code ,
    noshi_name ,
    noshi_price ,
    noshi_tax_group_code ,
    noshi_tax_no ,
    noshi_tax_rate ,
    noshi_tax ,
    noshi_tax_type ,
    noshi_nameplate ,
    noshi_message ,
    air_transport_flg ,
    delivery_note_no_disp_flg ,
    hasso_souko_cd ,
    shipping_hold_kbn ,
    TO_CHAR(shipping_hold_date, 'YYYY-MM-DD') as shipping_hold_date ,
    order_detail_no ,
    tracking_out_flg ,
    souko_shiji ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime ,
    delete_flg
FROM
    shipping_detail_view
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;
