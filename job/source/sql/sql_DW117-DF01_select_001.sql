SELECT
    shipping_no ,
    shipping_detail_no ,
    composition_no ,
    shop_code ,
    parent_commodity_code ,
    parent_sku_code ,
    child_commodity_code ,
    child_sku_code ,
    commodity_name ,
    standard_detail1_name ,
    standard_detail2_name ,
    unit_price ,
    discount_amount ,
    retail_price ,
    retail_tax ,
    commodity_tax_group_code ,
    commodity_tax_no ,
    commodity_tax_rate ,
    commodity_tax ,
    commodity_tax_type ,
    composition_quantity ,
    stock_management_type ,
    stock_allocated_kbn ,
    allocated_warehouse_code ,
    allocated_quantity ,
    arrival_reserved_quantity ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime ,
    delete_flg
FROM
    shipping_detail_composition_view
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;
