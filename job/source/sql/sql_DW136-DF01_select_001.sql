SELECT
    regular_contract_no ,
    regular_contract_detail_no ,
    shop_code ,
    sku_code ,
    commodity_code ,
    contract_amount ,
    commodity_name ,
    commodity_subcategory_code ,
    commodity_subcategory_code_name ,
    baitai_code ,
    regular_cycle_delivery_kbn ,
    TO_CHAR(regular_cycle_kijun_date, 'YYYY-MM-DD') as regular_cycle_kijun_date ,
    regular_kind ,
    regular_cycle_day_int ,
    regular_cycle_day ,
    regular_cycle_mon_interval ,
    regular_cycle_mon_interval_day ,
    regular_cycle_week_num ,
    regular_cycle_week_kbn ,
    regular_cycle_week_mon ,
    regular_cycle_week_tue ,
    regular_cycle_week_wed ,
    regular_cycle_week_thu ,
    regular_cycle_week_fri ,
    regular_cycle_week_sat ,
    regular_cycle_week_sun ,
    regular_cycle_week_hol ,
    cycle_disp_name ,
    TO_CHAR(next_shipping_plan_date, 'YYYY-MM-DD') as next_shipping_plan_date ,
    TO_CHAR(next_shipping_date, 'YYYY-MM-DD') as next_shipping_date ,
    TO_CHAR(next_delivery_plan_date, 'YYYY-MM-DD') as next_delivery_plan_date ,
    TO_CHAR(next_delivery_date, 'YYYY-MM-DD') as next_delivery_date ,
    TO_CHAR(lastest_delivery_date, 'YYYY-MM-DD') as lastest_delivery_date ,
    regular_kaiji ,
    shipped_regular_count ,
    regular_sale_stop_from ,
    regular_sale_stop_to ,
    hasso_souko_cd ,
    shipping_area ,
    regular_check_memo ,
    regular_memo_hold_flg ,
    souko_shiji ,
    next_regular_sale_stop_status ,
    regular_stop_reason_kbn ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime ,
    delete_flg
FROM
    regular_sale_cont_detail_view
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;
