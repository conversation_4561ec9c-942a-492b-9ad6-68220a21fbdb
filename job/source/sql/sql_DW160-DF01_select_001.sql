SELECT
    order_no ,
    shop_code ,
    TO_CHAR(order_datetime,'YYYY-MM-DD HH24:MI:SS')AS order_datetime ,
    customer_code ,
    neo_customer_no ,
    guest_flg ,
    last_name ,
    first_name ,
    last_name_kana ,
    first_name_kana ,
    email ,
    TO_CHAR(birth_date,'YYYY-MM-DD') AS birth_date ,
    sex ,
    postal_code ,
    prefecture_code ,
    address1 ,
    address2 ,
    address3 ,
    address4 ,
    corporation_post_name ,
    phone_number ,
    advance_later_flg ,
    payment_method_no ,
    payment_method_type ,
    payment_method_name ,
    ext_payment_method_type ,
    payment_commission ,
    payment_commission_tax_gr_code ,
    payment_commission_tax_no ,
    payment_commission_tax_rate ,
    payment_commission_tax ,
    payment_commission_tax_type ,
    coupon_management_code ,
    coupon_code ,
    coupon_name ,
    coupon_type ,
    coupon_use_purchase_price ,
    coupon_discount_type ,
    coupon_discount_price ,
    coupon_discount_rate ,
    coupon_used_amount ,
    TO_CHAR(coupon_start_datetime,'YYYY-MM-DD HH24:MI:SS') AS coupon_start_datetime ,
    TO_CHAR(coupon_end_datetime,'YYYY-MM-DD HH24:MI:SS') AS coupon_end_datetime ,
    coupon_kbn ,
    goods_group ,
    commodity_category_code ,
    commodity_series ,
    coupon_commodity_code_display ,
    baitai_name ,
    used_point ,
    total_amount ,
    ec_promotion_id ,
    ec_promotion_name ,
    ec_promotion_discount_price ,
    ec_campaign_id ,
    ec_campaign_name ,
    TO_CHAR(payment_date,'YYYY-MM-DD') AS payment_date ,
    TO_CHAR(payment_limit_date,'YYYY-MM-DD') AS payment_limit_date ,
    payment_status ,
    ext_payment_status ,
    customer_group_code ,
    data_transport_status ,
    order_status ,
    ext_order_status ,
    TO_CHAR(tax_reference_date,'YYYY-MM-DD') AS tax_reference_date ,
    TO_CHAR(cancel_date,'YYYY-MM-DD') AS cancel_date ,
    client_group ,
    device_type ,
    caution ,
    message ,
    payment_order_id ,
    cvs_code ,
    payment_receipt_no ,
    payment_receipt_url ,
    receipt_no ,
    customer_no ,
    confirm_no ,
    career_key ,
    order_create_error_code ,
    order_display_status ,
    order_kind_kbn ,
    marketing_channel ,
    original_order_no ,
    external_order_no ,
    TO_CHAR(order_recieve_datetime,'YYYY-MM-DD HH24:MI:SS') AS order_recieve_datetime ,
    TO_CHAR(order_update_datetime,'YYYY-MM-DD HH24:MI:SS') AS order_update_datetime ,
    order_update_reason_kbn ,
    cancel_reason_kbn ,
    TO_CHAR(uncollectible_date,'YYYY-MM-DD') AS uncollectible_date ,
    order_total_price ,
    account_receivable_balance ,
    appropriate_amount ,
    bill_address_kbn ,
    receipt_flg ,
    receipt_to ,
    receipt_detail ,
    bill_price ,
    bill_no ,
    bill_print_count ,
    authority_result_kbn ,
    authority_no ,
    card_password ,
    authority_approval_no ,
    TO_CHAR(authority_date,'YYYY-MM-DD') AS authority_date ,
    authority_price ,
    authority_cancel_approval_no ,
    TO_CHAR(authority_cancel_date,'YYYY-MM-DD') AS authority_cancel_date ,
    credit_payment_no ,
    TO_CHAR(credit_payment_date,'YYYY-MM-DD') AS credit_payment_date ,
    credit_payment_price ,
    credit_cancel_payment_no ,
    TO_CHAR(credit_cancel_payment_date,'YYYY-MM-DD') AS credit_cancel_payment_date ,
    credit_result_kbn ,
    card_brand ,
    credit_card_kanri_no ,
    credit_card_kanri_detail_no ,
    credit_card_no ,
    credit_card_meigi ,
    credit_card_valid_year ,
    credit_card_valid_month ,
    credit_card_pay_count ,
    payment_bar_code ,
    amzn_charge_permission_id ,
    amzn_charge_id ,
    amzn_charge_status ,
    TO_CHAR(amzn_authorization_datetime,'YYYY-MM-DD HH24:MI:SS') AS amzn_authorization_datetime ,
    TO_CHAR(amzn_capture_initiated_datetime,'YYYY-MM-DD HH24:MI:SS') AS amzn_capture_initiated_datetime ,
    TO_CHAR(amzn_captured_datetime,'YYYY-MM-DD HH24:MI:SS') AS amzn_captured_datetime ,
    TO_CHAR(amzn_canceled_datetime,'YYYY-MM-DD HH24:MI:SS') AS amzn_canceled_datetime ,
    order_user_code ,
    order_user ,
    change_user_code ,
    change_user ,
    demand_kbn ,
    TO_CHAR(demand1_ref_date,'YYYY-MM-DD') AS demand1_ref_date ,
    TO_CHAR(demand1_date,'YYYY-MM-DD') AS demand1_date ,
    TO_CHAR(demand1_limit_date,'YYYY-MM-DD') AS demand1_limit_date ,
    demand1_amount ,
    demand1_bar_code ,
    TO_CHAR(demand2_ref_date,'YYYY-MM-DD') AS demand2_ref_date ,
    TO_CHAR(demand2_date,'YYYY-MM-DD') AS demand2_date ,
    TO_CHAR(demand2_limit_date,'YYYY-MM-DD') AS demand2_limit_date ,
    demand2_amount ,
    demand2_bar_code ,
    TO_CHAR(demand3_ref_date,'YYYY-MM-DD') AS demand3_ref_date ,
    TO_CHAR(demand3_date,'YYYY-MM-DD') AS demand3_date ,
    TO_CHAR(demand3_limit_date,'YYYY-MM-DD') AS demand3_limit_date ,
    demand3_amount ,
    demand3_bar_code ,
    TO_CHAR(kashidaore_date,'YYYY-MM-DD') AS kashidaore_date ,
    demand_exclude_reason_kbn ,
    TO_CHAR(demand_exclude_start_date,'YYYY-MM-DD') AS demand_exclude_start_date ,
    TO_CHAR(demand_exclude_end_date,'YYYY-MM-DD') AS demand_exclude_end_date ,
    bill_sei_kj ,
    bill_mei_kj ,
    bill_sei_kn ,
    bill_mei_kn ,
    bill_tel_no ,
    bill_zipcd ,
    bill_addr1 ,
    bill_addr2 ,
    bill_addr3 ,
    bill_addr4 ,
    bill_corporation_post_name ,
    nohinsyo_uketsuke_tanto ,
    grant_plan_point_prod ,
    grant_plan_point_other ,
    grant_plan_point_total ,
    grant_point_prod ,
    grant_point_other ,
    grant_point_total ,
    reduction_plan_point_total ,
    reduction_point_total ,
    subtotal_before_campaign ,
    subtotal_after_campaign ,
    total_before_campaign ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') AS created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') AS updated_datetime 
FROM
    order_header
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                d_updated_datetime <= :diff_base_timestamp
    else true
    end;
