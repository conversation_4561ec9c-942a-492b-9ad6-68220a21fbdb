SELECT
    shop_code ,
    regular_sale_code ,
    sku_code ,
    commodity_code ,
    regular_cycle_kind_list ,
    regular_cycle_days_list ,
    regular_cycle_months_list ,
    regular_sale_stop_from ,
    regular_sale_stop_to ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') AS created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') AS updated_datetime ,
    delete_flg
FROM
    regular_sale_base_view
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                d_updated_datetime <= :diff_base_timestamp
    else true
    end;
