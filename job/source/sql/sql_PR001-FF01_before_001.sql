-- 商品ヘッダと商品明細の連携基準日時をそろえる
WITH now AS (SELECT LOCALTIMESTAMP AS ts)
INSERT INTO sync_timestamp (
    job_schedule_id,
    file_name,
    sync_datetime,
    sync_datetime_temp,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
) 
SELECT
  'JN_PR001-FF01_001',
  v.file_name,
  '1900-01-01 00:00:00',
  n.ts,
  'JN_PR001-FF01_001',
  n.ts,
  'JN_PR001-FF01_001',
  n.ts,
  1
FROM (VALUES ('EISR0020'), ('EISR0030')) AS v(file_name), now AS n
ON CONFLICT (job_schedule_id, file_name) DO UPDATE
SET sync_datetime_temp = EXCLUDED.sync_datetime_temp;