WITH combined_products AS (
    SELECT
        pl.mail_order_product_cd,
        pl.lgroup,
        pl.mgroup,
        pl.sgroup,
        pl.dgroup,
        pl.pms_u_ymd
    FROM
        mdm.product_linkage pl
    WHERE pl.mail_order_product_cd IS NOT NULL 
        AND (
            pl.product_type < '20'
            OR pl.product_type > '29'
        )
        AND (
            pl.period_set_sales_channel_1 = '10'
            OR pl.period_set_sales_channel_2 = '10'
            OR pl.period_set_sales_channel_3 = '10'
        )
    UNION
    SELECT
        pe.mail_order_product_cd,
        pe.lgroup,
        pe.mgroup,
        pe.sgroup,
        pe.dgroup,
        pe.pms_u_ymd
    FROM
        mdm.product_edit pe
    WHERE
        pe.mail_order_product_cd IS NOT NULL 
        AND (
            pe.product_type < '20'
            OR pe.product_type > '29'
        )
        AND (
            pe.period_set_sales_channel_1 = '10'
            OR pe.period_set_sales_channel_2 = '10'
            OR pe.period_set_sales_channel_3 = '10'
        )
        AND pe.composition_oms_link_flg = '1'
        AND NOT EXISTS (
            SELECT 1
            FROM mdm.product_linkage pl_inner
            WHERE pl_inner.mdm_integration_management_cd = pe.mdm_integration_management_cd
        )
),
updated_combined_products AS (
    SELECT
        cp.mail_order_product_cd,
        lg.lgroup,
        mg.mgroup,
        sg.sgroup,
        dg.dgroup
    FROM combined_products cp
    LEFT JOIN mdm.lgroup lg ON
        lpad(cp.lgroup,2,'0') = lg.lgroup
        AND lg.lgroup_nk IS NOT NULL
    LEFT JOIN mdm.mgroup mg ON
        lpad(cp.lgroup,2,'0') = mg.lgroup
        AND lpad(cp.mgroup,2,'0') = mg.mgroup
        AND mg.lgroup_nk IS NOT NULL
    LEFT JOIN mdm.sgroup sg ON
        lpad(cp.lgroup,2,'0') = sg.lgroup
        AND lpad(cp.mgroup,2,'0') = sg.mgroup
        AND lpad(cp.sgroup,2,'0') = sg.sgroup
        AND sg.lgroup_nk IS NOT NULL
    LEFT JOIN mdm.dgroup dg ON
        lpad(cp.lgroup,2,'0') = dg.lgroup
        AND lpad(cp.mgroup,2,'0') = dg.mgroup
        AND lpad(cp.sgroup,2,'0') = dg.sgroup
        AND lpad(cp.dgroup,2,'0') = dg.dgroup
        AND dg.lgroup_nk IS NOT NULL
    WHERE (cp.pms_u_ymd > :sync_datetime AND cp.pms_u_ymd <= :diff_base_timestamp)
        OR (lg.pms_u_ymd > :sync_datetime AND lg.pms_u_ymd <= :diff_base_timestamp)
        OR (mg.pms_u_ymd > :sync_datetime AND mg.pms_u_ymd <= :diff_base_timestamp)
        OR (dg.pms_u_ymd > :sync_datetime AND dg.pms_u_ymd <= :diff_base_timestamp)
        OR (sg.pms_u_ymd > :sync_datetime AND sg.pms_u_ymd <= :diff_base_timestamp)
)

SELECT
    '0000' AS shop_code,
    category_code,
    commodity_code,
    '' AS category_search_path,
    '' AS search_category_code0,
    '' AS search_category_code1,
    '' AS search_category_code2,
    '' AS search_category_code3,
    '' AS search_category_code4,
    '' AS search_category_code5,
    '' AS search_category_code6,
    '' AS orm_rowid,
    '' AS created_user,
    '' AS created_datetime,
    '' AS updated_user,
    '' AS updated_datetime
FROM
    (SELECT
        '0' AS category_code,
        mail_order_product_cd AS commodity_code,
        1 AS sort_no
    FROM updated_combined_products
    UNION ALL
    SELECT
        lgroup AS category_code,
        mail_order_product_cd AS commodity_code,
        2 AS sort_no
    FROM updated_combined_products
    WHERE lgroup IS NOT NULL
    UNION ALL
    SELECT
        CONCAT(lgroup, mgroup) AS category_code,
        mail_order_product_cd AS commodity_code,
        3 AS sort_no
    FROM updated_combined_products
    WHERE lgroup IS NOT NULL
        AND mgroup IS NOT NULL
    UNION ALL
    SELECT
        CONCAT(lgroup,  mgroup,  sgroup) AS category_code,
        mail_order_product_cd AS commodity_code,
        4 AS sort_no
    FROM updated_combined_products
    WHERE lgroup IS NOT NULL
        AND mgroup IS NOT NULL
        AND sgroup IS NOT NULL
    UNION ALL
    SELECT
        CONCAT(lgroup, mgroup, sgroup, dgroup) AS  category_code,
        mail_order_product_cd AS commodity_code,
        5 AS sort_no
    FROM updated_combined_products
    WHERE lgroup IS NOT NULL
        AND mgroup IS NOT NULL
        AND sgroup IS NOT NULL
        AND dgroup IS NOT NULL
    )
ORDER BY
    sort_no

