-- セット商品関連情報一時csv出力
select
    *
from
    aws_s3.query_export_to_s3(
        'SELECT
            set_commodity_composition_view.commodity_code AS mail_order_product_cd,
            set_commodity_composition_view.child_commodity_code AS product_id,
            SUM(set_commodity_composition_view.composition_quantity) AS quantity,
            MAX(set_commodity_composition_view.composition_order) AS composition_order
        FROM
            set_commodity_composition_view
            INNER JOIN mdm.product_linkage ON set_commodity_composition_view.child_commodity_code = mdm.product_linkage.MAIL_ORDER_PRODUCT_CD
        WHERE
            set_commodity_composition_view.delete_flg=0
            AND(
                mdm.product_linkage.period_set_sales_channel_1 = ''10''
                OR mdm.product_linkage.period_set_sales_channel_2 = ''10''
                OR mdm.product_linkage.period_set_sales_channel_3 = ''10''
                )
        GROUP BY
            set_commodity_composition_view.commodity_code,
            set_commodity_composition_view.child_commodity_code
        ORDER BY
            composition_order',
        aws_commons.create_s3_uri(
            :bucket_name,
            -- bucket名
            :object_path,
            -- object名
            'ap-northeast-1' -- region名
        ),
        options := 'format csv, header true, quote ''"'' '
    );