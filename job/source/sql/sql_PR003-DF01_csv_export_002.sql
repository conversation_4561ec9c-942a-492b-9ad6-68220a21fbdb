-- 定期便商品関連情報一時csv出力
select
    *
from
    aws_s3.query_export_to_s3(
        'SELECT
            regular_sale_base_view.sku_code AS mail_order_product_cd,
            regular_sale_commodity_view.sku_code AS product_id
        FROM
            regular_sale_base_view
            INNER JOIN regular_sale_commodity_view ON regular_sale_base_view.shop_code = regular_sale_commodity_view.shop_code
            AND regular_sale_base_view.regular_sale_code = regular_sale_commodity_view.regular_sale_code
            LEFT OUTER JOIN mdm.product_linkage ON regular_sale_commodity_view.sku_code = mdm.product_linkage.mail_order_product_cd
        WHERE
            regular_sale_base_view.delete_flg=0 AND regular_sale_commodity_view.delete_flg=0
            AND(
                mdm.product_linkage.period_set_sales_channel_1 = ''10''
                OR mdm.product_linkage.period_set_sales_channel_2 = ''10''
                OR mdm.product_linkage.period_set_sales_channel_3 = ''10''
                )',
        aws_commons.create_s3_uri(
            :bucket_name,
            -- bucket名
            :object_path,
            -- object名
            'ap-northeast-1' -- region名
        ),
        options := 'format csv, header true, quote ''"'' '
    );