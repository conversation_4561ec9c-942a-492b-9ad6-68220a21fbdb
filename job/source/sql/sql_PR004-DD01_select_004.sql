SELECT shop_code,
    regular_sale_code,
    regular_sale_composition_no,
    sku_code,
    commodity_code,
    display_order,
    regular_sale_commodity_type,
    regular_sale_commodity_point,
    difference_price,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg
FROM regular_sale_commodity_view
where updated_datetime > :sync_datetime
    AND updated_datetime <= :diff_base_timestamp;
