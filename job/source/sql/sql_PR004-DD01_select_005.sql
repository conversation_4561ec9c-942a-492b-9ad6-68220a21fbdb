SELECT shop_code,
    regular_sale_code,
    regular_sale_composition_no,
    regular_sale_composition_name,
    regular_order_count_min_limit,
    regular_order_count_max_limit,
    regular_order_count_interval,
    retail_price,
    regular_sale_commodity_point,
    display_order,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg
FROM regular_sale_composition_view
where updated_datetime > :sync_datetime
    AND updated_datetime <= :diff_base_timestamp;
