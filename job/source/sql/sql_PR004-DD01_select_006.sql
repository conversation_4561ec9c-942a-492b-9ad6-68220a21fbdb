SELECT shop_code,
    regular_sale_code,
    sku_code,
    commodity_code,
    regular_cycle_kind_list,
    regular_cycle_days_list,
    regular_cycle_months_list,
    regular_sale_stop_from,
    regular_sale_stop_to,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg
FROM regular_sale_base_view
where updated_datetime > :sync_datetime
    AND updated_datetime <= :diff_base_timestamp;
