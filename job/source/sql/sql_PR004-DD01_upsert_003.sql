INSERT INTO set_commodity_composition_view (
        shop_code,
        commodity_code,
        child_commodity_code,
        composition_quantity,
        composition_order,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT shop_code,
    commodity_code,
    child_commodity_code,
    composition_quantity,
    composition_order,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg,
    'JN_PR004-DD01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_PR004-DD01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM set_commodity_composition_view_work ON CONFLICT (
        shop_code,
        commodity_code,
        child_commodity_code,
        composition_order
    ) DO
UPDATE
SET composition_quantity = EXCLUDED.composition_quantity,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    delete_flg = EXCLUDED.delete_flg,
    d_updated_user = 'JN_PR004-DD01_001',
    d_updated_datetime = NOW(),
    d_version = set_commodity_composition_view.d_version + 1;
