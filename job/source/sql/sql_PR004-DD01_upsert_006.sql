INSERT INTO regular_sale_base_view (
        shop_code,
        regular_sale_code,
        sku_code,
        commodity_code,
        regular_cycle_kind_list,
        regular_cycle_days_list,
        regular_cycle_months_list,
        regular_sale_stop_from,
        regular_sale_stop_to,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT shop_code,
    regular_sale_code,
    sku_code,
    commodity_code,
    regular_cycle_kind_list,
    regular_cycle_days_list,
    regular_cycle_months_list,
    regular_sale_stop_from,
    regular_sale_stop_to,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg,
    'JN_PR004-DD01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_PR004-DD01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM regular_sale_base_view_work ON CONFLICT (shop_code, regular_sale_code) DO
UPDATE
SET sku_code = EXCLUDED.sku_code,
    commodity_code = EXCLUDED.commodity_code,
    regular_cycle_kind_list = EXCLUDED.regular_cycle_kind_list,
    regular_cycle_days_list = EXCLUDED.regular_cycle_days_list,
    regular_cycle_months_list = EXCLUDED.regular_cycle_months_list,
    regular_sale_stop_from = EXCLUDED.regular_sale_stop_from,
    regular_sale_stop_to = EXCLUDED.regular_sale_stop_to,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    delete_flg = EXCLUDED.delete_flg,
    d_updated_user = 'JN_PR004-DD01_001',
    d_updated_datetime = NOW(),
    d_version = regular_sale_base_view.d_version + 1;
