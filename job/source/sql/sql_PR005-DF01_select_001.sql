WITH linkage_target_products AS (
  -- 商品連携マスタと期間別価格連携マスタで更新日時が前回差分取得日時以降、または、期間別価格連携マスタの適用開始日が処理日である商品一覧を取得
  SELECT DISTINCT
    pl.MDM_INTEGRATION_MANAGEMENT_CD
  FROM
    mdm.product_linkage pl
    LEFT JOIN mdm.period_price_linkage ppl ON pl.mdm_integration_management_cd = ppl.mdm_integration_management_cd
    AND ppl.mdm_integration_management_cd_nk IS NOT NULL
  WHERE
    (
      (
        pl.PMS_U_YMD > :sync_datetime
        AND pl.PMS_U_YMD <= :diff_base_timestamp
      )
      OR TO_CHAR(ppl.apply_start_date, 'YYYYMMDD') = TO_CHAR(:diff_base_timestamp, 'YYYYMMDD')
    )
    AND (
       pl.PERIOD_SET_SALES_CHANNEL_1 = '10'
       OR pl.PERIOD_SET_SALES_CHANNEL_2 = '10'
       OR pl.PERIOD_SET_SALES_CHANNEL_3 = '10'
    )
    AND pl.MAIL_ORDER_PRODUCT_CD IS NOT NULL
),
linkage_target_price AS (
  SELECT
    pl.MDM_INTEGRATION_MANAGEMENT_CD,
    COALESCE(ppl2.apply_start_date, ppl4.apply_start_date) AS apply_start_date -- ①があれば採用、なければ②を採用
  FROM
    linkage_target_products pl
    LEFT JOIN (
      -- ①処理日より前で、直近の価格
      SELECT
        mdm_integration_management_cd,
        max(apply_start_date) AS apply_start_date
      FROM
        mdm.period_price_linkage AS ppl3
      WHERE
        ppl3.mdm_integration_management_cd_nk IS NOT NULL
        AND ppl3.apply_start_date <= :diff_base_timestamp
      GROUP BY
        mdm_integration_management_cd
    ) AS ppl2 ON pl.mdm_integration_management_cd = ppl2.mdm_integration_management_cd
    LEFT JOIN (
      -- ②処理日より後で、直近の価格
      SELECT
        mdm_integration_management_cd,
        min(apply_start_date) AS apply_start_date
      FROM
        mdm.period_price_linkage AS ppl3
      WHERE
        ppl3.mdm_integration_management_cd_nk IS NOT NULL
        AND ppl3.apply_start_date > :diff_base_timestamp
      GROUP BY
        mdm_integration_management_cd
    ) AS ppl4 ON pl.mdm_integration_management_cd = ppl4.mdm_integration_management_cd
)
SELECT p.MDM_INTEGRATION_MANAGEMENT_CD,
    p.MAIL_ORDER_PRODUCT_CD,
    p.WAREHOUSE_MANAGEMENT_CD,
    p.JAN,
    p.CORE_PRODUCT_NAME,
    p.PRODUCT_NAME,
    p.PRODUCT_SEGMENT,
    p.BUSINESS_SEGMENT,
    p.PRODUCT_CAT,
    p.PRODUCT_SERIES,
    TO_CHAR(p.SALE_START_DATE, 'YYYY/MM/DD') AS sale_start_date,
    CASE
        WHEN p.PERIOD_SET_SALES_CHANNEL_3 = '10' THEN TO_CHAR(p.SALES_CHANNEL_3_SALE_START_DATE, 'YYYY/MM/DD HH24:MI:SS')
        WHEN p.PERIOD_SET_SALES_CHANNEL_1 = '10' THEN TO_CHAR(p.SALES_CHANNEL_1_SALE_START_DATE, 'YYYY/MM/DD HH24:MI:SS')
        WHEN p.PERIOD_SET_SALES_CHANNEL_2 = '10' THEN TO_CHAR(p.SALES_CHANNEL_2_SALE_START_DATE, 'YYYY/MM/DD HH24:MI:SS')
    END AS sale_start_date_time,
    CASE
        WHEN p.PERIOD_SET_SALES_CHANNEL_1 = '10' THEN TO_CHAR(p.SALES_CHANNEL_1_SALE_END_DATE, 'YYYY/MM/DD HH24:MI:SS')
        WHEN p.PERIOD_SET_SALES_CHANNEL_3 = '10' THEN TO_CHAR(p.SALES_CHANNEL_3_SALE_END_DATE, 'YYYY/MM/DD HH24:MI:SS')
        WHEN p.PERIOD_SET_SALES_CHANNEL_2 = '10' THEN TO_CHAR(p.SALES_CHANNEL_2_SALE_END_DATE, 'YYYY/MM/DD HH24:MI:SS')
    END AS sale_end_date_time,
    pp.TAX_EXC,
    pp.TAX_INC,
    pp.TAX,
    pp.TAX_RATE,
    p.SALE_STATUS,
    p.LGROUP,
    p.MGROUP,
    p.SGROUP,
    p.DGROUP,
    p.PRODUCT_TYPE,
    p.CORE_DEPARTMENT,
    p.ACCOUNTIN_PATTERN_GB,
    p.MATERIAL,
    p.SET_PRODUCT_FLG,
    p.SET_COMPOSITION_FLG,
    p.STORE_PO_GB,
    p.REPRESENTATIVE_PRODUCT_CD,
    p.LOT_MANAGEMENT_TARGET_PRODUCT,
    p.REDUCTION_BASE,
    p.COLOR_NAME,
    CASE
        WHEN p.LGROUP IN ('30', '51') THEN SUBSTRING(p.WAREHOUSE_MANAGEMENT_CD, LENGTH(p.WAREHOUSE_MANAGEMENT_CD) - 3, 2)
        ELSE p.COLOR_CD
    END AS COLOR_CD,
    p.SIZE_NAME,
    CASE
        WHEN p.LGROUP IN ('30', '51') THEN SUBSTRING(p.WAREHOUSE_MANAGEMENT_CD, LENGTH(p.WAREHOUSE_MANAGEMENT_CD) - 1, 2)
        ELSE p.SIZE_CD
    END AS SIZE_CD,
    p.SEASON,
    p.USE_POINT_CNT,
    p.PRODUCT_NO
FROM
    linkage_target_price t
    INNER JOIN mdm.product_linkage p ON t.mdm_integration_management_cd = p.mdm_integration_management_cd
    LEFT JOIN mdm.period_price_linkage pp ON t.mdm_integration_management_cd = pp.mdm_integration_management_cd
    AND t.apply_start_date = pp.apply_start_date
;
