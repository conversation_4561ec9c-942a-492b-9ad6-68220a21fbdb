SELECT
    p.PRODUCT_NO,
    CONCAT(
        p.PRODUCT_NAME,
        ' ',
        p.ORIGINAL_COLOR_CD,
        ' ',
        p.SIZE_CD
    ) AS product_full_name,
    p2.PRODUCT_SEGMENT,
    p2.BUSINESS_SEGMENT,
    '94000' AS PRODUCT_CAT,
    NULL AS PRODUCT_SERIES,
    p2.MAIN_PRODUCT_NO
FROM
    mdm.product_linkage p
    JOIN dlpf.set_commodity_composition c ON c.shop_code = '0000'
    AND c.commodity_code = p.MAIL_ORDER_PRODUCT_CD :: character varying
    JOIN mdm.product_linkage p2 ON p2.MAIL_ORDER_PRODUCT_CD = c.child_commodity_code
    AND p2.PRODUCT_NO = (
        SELECT
            MIN(pl3.PRODUCT_NO)
        FROM
            set_commodity_composition scc3
            JOIN mdm.product_linkage pl3 ON scc3.child_commodity_code = pl3.MAIL_ORDER_PRODUCT_CD
        WHERE
            scc3.commodity_code = p.MAIL_ORDER_PRODUCT_CD
            AND pl3.PRODUCT_TYPE != '02'
    )
WHERE
    p.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
    AND p.BUSINESS_SEGMENT != 'ZS02'
    AND p.PRODUCT_SEGMENT != 'ZM00'
    AND p.SET_PRODUCT_FLG = '1'
UNION
ALL
SELECT
    p.PRODUCT_NO,
    CONCAT(
        p.PRODUCT_NAME,
        ' ',
        p.ORIGINAL_COLOR_CD,
        ' ',
        p.SIZE_CD
    ) AS product_full_name,
    p.PRODUCT_SEGMENT,
    p.BUSINESS_SEGMENT,
    p.PRODUCT_CAT,
    p.PRODUCT_SERIES,
    p.MAIN_PRODUCT_NO
FROM
    mdm.product_linkage p
WHERE
    p.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
    AND p.BUSINESS_SEGMENT != 'ZS02'
    AND p.PRODUCT_SEGMENT != 'ZM00'
    AND p.SET_PRODUCT_FLG != '1'
ORDER BY PRODUCT_NO;
