WITH shipping_items AS (
  -- 次回出荷が今月以降の削除されていない定期契約明細のうち、定期契約ヘッダの定期ステータスが1のもの
  SELECT DISTINCT
    ch.neo_customer_no AS neo_customer_no, 
    cd.regular_contract_no AS regular_contract_no, 
    cd.commodity_code AS commodity_code, 
    1 AS valid_data
  FROM regular_sale_cont_header AS ch
  JOIN regular_sale_cont_detail AS cd 
  ON ch.regular_contract_no = cd.regular_contract_no
    AND ch.regular_sale_cont_status = '1' 
    AND TO_CHAR(cd.next_shipping_date, 'YYYYMM') >= TO_CHAR(CURRENT_DATE, 'YYYYMM')
  UNION ALL
  -- 次回出荷が今月以降の削除された定期契約明細のうち、定期契約ヘッダの定期ステータスが1のもの
  SELECT DISTINCT
    ch.neo_customer_no AS neo_customer_no, 
    del.regular_contract_no AS regular_contract_no, 
    del.commodity_code AS commodity_code, 
    0 AS valid_data
  FROM regular_sale_cont_header AS ch
  JOIN deldata.regular_sale_cont_detail AS del
  ON ch.regular_contract_no = del.regular_contract_no
    AND ch.regular_sale_cont_status = '1' 
    AND TO_CHAR(del.next_shipping_date, 'YYYYMM') >= TO_CHAR(CURRENT_DATE, 'YYYYMM')
),
-- 明細構成品を↑と結合させる
target_data AS (
  -- 連携対象の削除されていない明細構成品
  SELECT DISTINCT
    si.neo_customer_no AS neo_customer_no, 
    COALESCE(cc.child_commodity_code, si.commodity_code) AS commodity_code,
    si.valid_data AS valid_data
  FROM shipping_items AS si
  LEFT JOIN regular_sale_cont_composition AS cc 
  ON si.regular_contract_no = cc.regular_contract_no
    AND si.commodity_code = cc.parent_commodity_code
  -- 明細と構成品が全て物理削除された契約の親商品コードが出ないようにする
  WHERE NOT (
    si.valid_data = 0
      AND cc.regular_contract_no IS NULL
      AND EXISTS (
        SELECT 1
        FROM deldata.regular_sale_cont_composition AS del
        WHERE del.regular_contract_no = si.regular_contract_no
          AND del.parent_commodity_code = si.commodity_code))
  UNION ALL
  -- 連携対象の削除された明細構成品
  -- 重複削除しなくても集計結果が0件なことを考えると、SELECT DISTINCTじゃなくてよさそう
  SELECT
    si.neo_customer_no AS neo_customer_no, 
    del.child_commodity_code AS commodity_code,
    0 AS valid_data
  FROM shipping_items AS si
  JOIN deldata.regular_sale_cont_composition AS del
  ON si.regular_contract_no = del.regular_contract_no
    AND si.commodity_code = del.parent_commodity_code
)
SELECT 
  TO_CHAR(CURRENT_DATE, 'YYYYMM') AS "YEAR_MONTH",
  LPAD(commodity_code, 10, '0') AS "PRODUCT_CD",
  COUNT(CASE WHEN valid_data = 1 THEN 1 ELSE NULL END) AS "KANYUSHA_SU"
FROM target_data
GROUP BY LPAD(commodity_code, 10, '0')
ORDER BY LPAD(commodity_code, 10, '0');