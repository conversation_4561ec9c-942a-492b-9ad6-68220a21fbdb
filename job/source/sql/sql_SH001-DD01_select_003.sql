SELECT
    regular_contract_no,
    shop_code,
    regular_sale_cont_datetime,
    customer_code,
    neo_customer_no,
    payment_method_no,
    address_no,
    regular_sale_cont_status,
    next_delivery_request_date,
    external_order_no,
    order_user_code,
    regular_update_datetime,
    change_user_code,
    regular_update_reason_kbn,
    otodoke_hope_time_kbn,
    marketing_channel,
    delivery_type_no,
    shipping_method_flg,
    ext_payment_method_type,
    card_brand,
    credit_card_kanri_no,
    credit_card_kanri_detail_no,
    credit_card_no,
    credit_card_meigi,
    credit_card_valid_year,
    credit_card_valid_month,
    credit_card_pay_count,
    amzn_charge_permission_id,
    bill_address_kbn,
    bill_print_otodoke_id,
    o_name_disp_kbn,
    delivery_note_flg,
    include_flg,
    receipt_flg,
    receipt_to,
    receipt_detail,
    first_shipping_date,
    lastest_shipping_date,
    first_delivery_date,
    lastest_delivery_date,
    regular_stop_date,
    regular_stop_reason_kbn,
    regular_hold_date,
    regular_hold_clear_date,
    regular_kaiji,
    shipped_regular_count,
    delivery_memo,
    regular_hold_reason_kbn,
    niyose_flg,
    device_type,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM
    regular_sale_cont_header
where
    updated_datetime > :sync_datetime
AND updated_datetime <= :diff_base_timestamp;
