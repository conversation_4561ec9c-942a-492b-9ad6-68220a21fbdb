INSERT INTO order_detail_view (
        order_no,
        order_detail_no,
        shop_code,
        sku_code,
        commodity_code,
        commodity_name,
        commodity_kind,
        baitai_code,
        baitai_name,
        hinban_code,
        standard_detail1_name,
        standard_detail2_name,
        purchasing_amount,
        unit_price,
        retail_price,
        retail_tax,
        commodity_tax_group_code,
        commodity_tax_no,
        commodity_tax_rate,
        commodity_tax,
        commodity_tax_type,
        campaign_code,
        campaign_name,
        campaign_instructions_code,
        campaign_instructions_name,
        campaign_discount_rate,
        campaign_discount_price,
        campaign_reference_datetime,
        present_campaign_instructions_code,
        present_order_detail_no,
        age_limit_code,
        age_limit_name,
        age,
        age_limit_confirm_type,
        applied_point_rate,
        benefits_code,
        benefits_name,
        benefits_commodity_code,
        stock_management_type,
        stock_allocated_kbn,
        allocated_warehouse_code,
        allocated_quantity,
        arrival_reserved_quantity,
        cancel_quantity,
        henpin_qt,
        coupon_management_code,
        coupon_code,
        coupon_name,
        coupon_discount_rate,
        coupon_discount_price,
        ec_promotion_id,
        ec_promotion_name,
        ec_promotion_discount_price,
        ec_campaign_id,
        ec_campaign_name,
        adjustment_price,
        keihi_hurikae_target_flg,
        member_price_applied_flg,
        shipping_charge_target_flg,
        regular_contract_no,
        regular_contract_detail_no,
        regular_kaiji,
        regular_check_memo,
        total_commodity_buy_count,
        total_commodity_regular_kaiji,
        regular_total_commodity_regular_kaiji,
        commodity_category_code,
        total_category_buy_count,
        total_categoryregular_kaiji,
        regular_total_categoryregular_kaiji,
        commodity_subcategory_code,
        total_subcategory_buy_count,
        total_subcategoryregular_kaiji,
        regular_total_subcategoryregular_kaiji,
        commodity_subsubcategory_code,
        total_subsubcategory_buy_count,
        total_subsubcategoryregular_kaiji,
        regular_total_subsubcategoryregular_kaiji,
        grant_plan_point_prod_detail,
        reduction_plan_point_prod_detail,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT order_no,
    order_detail_no,
    shop_code,
    sku_code,
    commodity_code,
    commodity_name,
    commodity_kind,
    baitai_code,
    baitai_name,
    hinban_code,
    standard_detail1_name,
    standard_detail2_name,
    purchasing_amount,
    unit_price,
    retail_price,
    retail_tax,
    commodity_tax_group_code,
    commodity_tax_no,
    commodity_tax_rate,
    commodity_tax,
    commodity_tax_type,
    campaign_code,
    campaign_name,
    campaign_instructions_code,
    campaign_instructions_name,
    campaign_discount_rate,
    campaign_discount_price,
    campaign_reference_datetime,
    present_campaign_instructions_code,
    present_order_detail_no,
    age_limit_code,
    age_limit_name,
    age,
    age_limit_confirm_type,
    applied_point_rate,
    benefits_code,
    benefits_name,
    benefits_commodity_code,
    stock_management_type,
    stock_allocated_kbn,
    allocated_warehouse_code,
    allocated_quantity,
    arrival_reserved_quantity,
    cancel_quantity,
    henpin_qt,
    coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_discount_rate,
    coupon_discount_price,
    ec_promotion_id,
    ec_promotion_name,
    ec_promotion_discount_price,
    ec_campaign_id,
    ec_campaign_name,
    adjustment_price,
    keihi_hurikae_target_flg,
    member_price_applied_flg,
    shipping_charge_target_flg,
    regular_contract_no,
    regular_contract_detail_no,
    regular_kaiji,
    regular_check_memo,
    total_commodity_buy_count,
    total_commodity_regular_kaiji,
    regular_total_commodity_regular_kaiji,
    commodity_category_code,
    total_category_buy_count,
    total_categoryregular_kaiji,
    regular_total_categoryregular_kaiji,
    commodity_subcategory_code,
    total_subcategory_buy_count,
    total_subcategoryregular_kaiji,
    regular_total_subcategoryregular_kaiji,
    commodity_subsubcategory_code,
    total_subsubcategory_buy_count,
    total_subsubcategoryregular_kaiji,
    regular_total_subsubcategoryregular_kaiji,
    grant_plan_point_prod_detail,
    reduction_plan_point_prod_detail,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg,
    'JN_SH001-DD01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_SH001-DD01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM order_detail_view_work ON CONFLICT (order_no, order_detail_no) DO
UPDATE
SET shop_code = EXCLUDED.shop_code,
    sku_code = EXCLUDED.sku_code,
    commodity_code = EXCLUDED.commodity_code,
    commodity_name = EXCLUDED.commodity_name,
    commodity_kind = EXCLUDED.commodity_kind,
    baitai_code = EXCLUDED.baitai_code,
    baitai_name = EXCLUDED.baitai_name,
    hinban_code = EXCLUDED.hinban_code,
    standard_detail1_name = EXCLUDED.standard_detail1_name,
    standard_detail2_name = EXCLUDED.standard_detail2_name,
    purchasing_amount = EXCLUDED.purchasing_amount,
    unit_price = EXCLUDED.unit_price,
    retail_price = EXCLUDED.retail_price,
    retail_tax = EXCLUDED.retail_tax,
    commodity_tax_group_code = EXCLUDED.commodity_tax_group_code,
    commodity_tax_no = EXCLUDED.commodity_tax_no,
    commodity_tax_rate = EXCLUDED.commodity_tax_rate,
    commodity_tax = EXCLUDED.commodity_tax,
    commodity_tax_type = EXCLUDED.commodity_tax_type,
    campaign_code = EXCLUDED.campaign_code,
    campaign_name = EXCLUDED.campaign_name,
    campaign_instructions_code = EXCLUDED.campaign_instructions_code,
    campaign_instructions_name = EXCLUDED.campaign_instructions_name,
    campaign_discount_rate = EXCLUDED.campaign_discount_rate,
    campaign_discount_price = EXCLUDED.campaign_discount_price,
    campaign_reference_datetime = EXCLUDED.campaign_reference_datetime,
    present_campaign_instructions_code = EXCLUDED.present_campaign_instructions_code,
    present_order_detail_no = EXCLUDED.present_order_detail_no,
    age_limit_code = EXCLUDED.age_limit_code,
    age_limit_name = EXCLUDED.age_limit_name,
    age = EXCLUDED.age,
    age_limit_confirm_type = EXCLUDED.age_limit_confirm_type,
    applied_point_rate = EXCLUDED.applied_point_rate,
    benefits_code = EXCLUDED.benefits_code,
    benefits_name = EXCLUDED.benefits_name,
    benefits_commodity_code = EXCLUDED.benefits_commodity_code,
    stock_management_type = EXCLUDED.stock_management_type,
    stock_allocated_kbn = EXCLUDED.stock_allocated_kbn,
    allocated_warehouse_code = EXCLUDED.allocated_warehouse_code,
    allocated_quantity = EXCLUDED.allocated_quantity,
    arrival_reserved_quantity = EXCLUDED.arrival_reserved_quantity,
    cancel_quantity = EXCLUDED.cancel_quantity,
    henpin_qt = EXCLUDED.henpin_qt,
    coupon_management_code = EXCLUDED.coupon_management_code,
    coupon_code = EXCLUDED.coupon_code,
    coupon_name = EXCLUDED.coupon_name,
    coupon_discount_rate = EXCLUDED.coupon_discount_rate,
    coupon_discount_price = EXCLUDED.coupon_discount_price,
    ec_promotion_id = EXCLUDED.ec_promotion_id,
    ec_promotion_name = EXCLUDED.ec_promotion_name,
    ec_promotion_discount_price = EXCLUDED.ec_promotion_discount_price,
    ec_campaign_id = EXCLUDED.ec_campaign_id,
    ec_campaign_name = EXCLUDED.ec_campaign_name,
    adjustment_price = EXCLUDED.adjustment_price,
    keihi_hurikae_target_flg = EXCLUDED.keihi_hurikae_target_flg,
    member_price_applied_flg = EXCLUDED.member_price_applied_flg,
    shipping_charge_target_flg = EXCLUDED.shipping_charge_target_flg,
    regular_contract_no = EXCLUDED.regular_contract_no,
    regular_contract_detail_no = EXCLUDED.regular_contract_detail_no,
    regular_kaiji = EXCLUDED.regular_kaiji,
    regular_check_memo = EXCLUDED.regular_check_memo,
    total_commodity_buy_count = EXCLUDED.total_commodity_buy_count,
    total_commodity_regular_kaiji = EXCLUDED.total_commodity_regular_kaiji,
    regular_total_commodity_regular_kaiji = EXCLUDED.regular_total_commodity_regular_kaiji,
    commodity_category_code = EXCLUDED.commodity_category_code,
    total_category_buy_count = EXCLUDED.total_category_buy_count,
    total_categoryregular_kaiji = EXCLUDED.total_categoryregular_kaiji,
    regular_total_categoryregular_kaiji = EXCLUDED.regular_total_categoryregular_kaiji,
    commodity_subcategory_code = EXCLUDED.commodity_subcategory_code,
    total_subcategory_buy_count = EXCLUDED.total_subcategory_buy_count,
    total_subcategoryregular_kaiji = EXCLUDED.total_subcategoryregular_kaiji,
    regular_total_subcategoryregular_kaiji = EXCLUDED.regular_total_subcategoryregular_kaiji,
    commodity_subsubcategory_code = EXCLUDED.commodity_subsubcategory_code,
    total_subsubcategory_buy_count = EXCLUDED.total_subsubcategory_buy_count,
    total_subsubcategoryregular_kaiji = EXCLUDED.total_subsubcategoryregular_kaiji,
    regular_total_subsubcategoryregular_kaiji = EXCLUDED.regular_total_subsubcategoryregular_kaiji,
    grant_plan_point_prod_detail = EXCLUDED.grant_plan_point_prod_detail,
    reduction_plan_point_prod_detail = EXCLUDED.reduction_plan_point_prod_detail,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    delete_flg = EXCLUDED.delete_flg,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = order_detail_view.d_version + 1;
