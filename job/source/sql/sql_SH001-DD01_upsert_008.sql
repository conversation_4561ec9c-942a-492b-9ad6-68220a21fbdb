INSERT INTO order_campaign_use_amount_view (
        order_no,
        tax_group_code,
        tax_no,
        use_code_type,
        use_code,
        use_amount,
        tax_rate,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        delete_flg,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT order_no,
    tax_group_code,
    tax_no,
    use_code_type,
    use_code,
    use_amount,
    tax_rate,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    delete_flg,
    'JN_SH001-DD01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_SH001-DD01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM order_campaign_use_amount_view_work ON CONFLICT (
        order_no,
        tax_group_code,
        tax_no,
        use_code_type,
        use_code
    ) DO
UPDATE
SET use_amount = EXCLUDED.use_amount,
    tax_rate = EXCLUDED.tax_rate,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    delete_flg = EXCLUDED.delete_flg,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = order_campaign_use_amount_view.d_version + 1;
