with shipping_header_data as (
    --出荷実績から連携対象の出荷番号を取得
    select 
        sh.shipping_no, 
        CASE WHEN sh.shipping_status ='3' THEN '1' ELSE '0' END AS OUT_CLOSE_YN, 
        sh.shipping_date
    from shipping_header sh 
    where sh.shipping_date >= (DATE_TRUNC('month', CURRENT_DATE) - interval '1 month')
    and sh.shipping_date < (DATE_TRUNC('day', CURRENT_DATE) + interval '1 day')
),
--  セット商品の実績集計
set_product_data as (
     --出荷明細と出荷指示からセット商品情報を取得(有効データ)
    select sdc.shipping_no ,sdc.parent_commodity_code, sdc.child_commodity_code,sdc.commodity_name,
        sdc.composition_quantity * sd.purchasing_amount as count,
        shd.shipping_date, shd.OUT_CLOSE_YN,sd.hasso_souko_cd,
        case when ch.commodity_kind ='11'  THEN '1' ELSE '0' END AS PERIOD_YN --商品種別:11 定期便
    from shipping_detail sd 
    inner join shipping_detail_composition sdc on sd.shipping_no = sdc.shipping_no and sd.shipping_detail_no = sdc.shipping_detail_no
    inner join shipping_header_data shd on shd.shipping_no = sd.shipping_no
    inner join commodity_header ch on ch.commodity_code = sdc.parent_commodity_code
     --出荷明細と出荷指示を結合してセット商品情報を取得(物理削除 明細:有効 / 構成品:無効)
    union all
    select sdc.shipping_no ,sdc.parent_commodity_code, sdc.child_commodity_code,sdc.commodity_name,
        0 as count,
        shd.shipping_date, shd.OUT_CLOSE_YN , sd.hasso_souko_cd,
        case when ch.commodity_kind ='11'  THEN '1' ELSE '0' END AS PERIOD_YN
    from deldata.shipping_detail_composition sdc
    inner join shipping_header_data shd on shd.shipping_no = sdc.shipping_no 
    inner join shipping_detail sd on sd.shipping_no = sdc.shipping_no and sd.sku_code = sdc.parent_sku_code
    inner join commodity_header ch on ch.commodity_code = sdc.parent_commodity_code
     --出荷明細と出荷指示を結合してセット商品情報を取得(物理削除 明細:無効 / 構成品:無効)
    union all
    select sdc.shipping_no ,sdc.parent_commodity_code, sdc.child_commodity_code,sdc.commodity_name,
        0 as count,
        shd.shipping_date, shd.OUT_CLOSE_YN , dsd.hasso_souko_cd,
        case when ch.commodity_kind ='11'  THEN '1' ELSE '0' END AS PERIOD_YN
    from deldata.shipping_detail_composition sdc
    inner join shipping_header_data shd on shd.shipping_no = sdc.shipping_no 
    inner join deldata.shipping_detail dsd on dsd.shipping_no = sdc.shipping_no and dsd.sku_code = sdc.parent_sku_code
    inner join commodity_header ch on ch.commodity_code = sdc.parent_commodity_code
 ),
--  単体商品(セット商品以外)の実績集計
unit_product_data as (
    --セット商品以外（出荷明細の商品からセット商品を除く）
    select sd.shipping_no,sd.sku_code,ch.commodity_name ,sd.purchasing_amount,
        shd.shipping_date, shd.OUT_CLOSE_YN,sd.hasso_souko_cd, '0' AS PERIOD_YN
    from shipping_detail sd
    inner join shipping_header_data shd on shd.shipping_no = sd.shipping_no
    inner join commodity_header ch on ch.commodity_code = sd.sku_code
    where not exists (
      select 1 from set_product_data spa
      where sd.shipping_no = spa.shipping_no
      and sd.sku_code = spa.parent_commodity_code
    )
    -- セット商品以外（物理削除 出荷明細:削除　受注明細:有効）
    union all
    select sd.shipping_no,sd.sku_code,ch.commodity_name ,0,
        shd.shipping_date, shd.OUT_CLOSE_YN,sd.hasso_souko_cd, '0' AS PERIOD_YN
    from deldata.shipping_detail sd
    inner join shipping_header_data shd on shd.shipping_no = sd.shipping_no
    --  受注明細が物理削除された場合、明細番号が再採番となるため、SKUコードを含めた結合とする
    inner join commodity_header ch on ch.commodity_code = sd.sku_code
    where not exists (
      select 1 from set_product_data spa
      where sd.shipping_no = spa.shipping_no 
      and sd.sku_code = spa.parent_commodity_code
    )
    -- セット商品以外（物理削除 出荷明細:削除　受注明細:削除）
    union all
    select sd.shipping_no,sd.sku_code,ch.commodity_name ,0,
        shd.shipping_date, shd.OUT_CLOSE_YN,sd.hasso_souko_cd, '0' AS PERIOD_YN
    from deldata.shipping_detail sd
    inner join shipping_header_data shd on shd.shipping_no = sd.shipping_no
    inner join commodity_header ch on ch.commodity_code = sd.sku_code
    where not exists (
      select 1 from set_product_data spa
      where sd.shipping_no = spa.shipping_no 
      and sd.sku_code = spa.parent_commodity_code
    )
),
-- 集計対象のサマリ(通常商品とセット商品の合算)
target_data as (
    select 'S' as data_kbn,spd.shipping_date, spd.child_commodity_code as GOODS_CODE,spd.commodity_name as GOODS_NAME,spd.count as QTY,spd.OUT_CLOSE_YN,hasso_souko_cd,spd.PERIOD_YN
    from set_product_data spd
    union all
    select 'U' as data_kbn ,upd.shipping_date,upd.sku_code as GOODS_CODE,upd.commodity_name as GOODS_NAME,upd.purchasing_amount as QTY,upd.OUT_CLOSE_YN,hasso_souko_cd,upd.PERIOD_YN
    from unit_product_data upd
)
SELECT 
    TO_CHAR(A.shipping_date, 'YYYYMMDD') AS "SYUKKA_DATE",
    CASE WHEN A.PERIOD_YN = '1' THEN '02' ELSE '01' END AS "CHANNEL_CD",
    CASE WHEN A.PERIOD_YN = '1' THEN '定期' ELSE '通販' END AS "CHANNEL_NAME",
    A.GOODS_CODE as "PRODUCT_CD",
    A.GOODS_NAME as "PRODUCT_NAME",
    sum(A.QTY) as "KOSU"
FROM target_data A
WHERE A.OUT_CLOSE_YN = '1'
and hasso_souko_cd in ('000001', '000002', '000003', '000004', '000101', '000051')
-- アパレル、インナーウェアに該当する商品を除外する
and not exists (
    select 1
    from category_commodity cc
    where cc.commodity_code = A.GOODS_CODE
    and cc.category_code IN ('30', '51')
)
group by A.shipping_date,A.GOODS_NAME ,A.PERIOD_YN, A.GOODS_CODE
ORDER BY A.shipping_date, A.PERIOD_YN, A.GOODS_CODE;