-- sync_timestampのtempに基準日時を入れる
INSERT INTO sync_timestamp
  (job_schedule_id, file_name, sync_datetime, sync_datetime_temp, d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
VALUES
  ('JN_ST002-DF01_001', 'txInventory', '1900-01-01 0:00:00', NOW(), 'JN_ST002-DF01_001', NOW(), 'JN_ST002-DF01_001', NOW(), 0)
ON CONFLICT (job_schedule_id, file_name)
DO UPDATE SET
  sync_datetime_temp = NOW()
  ,d_updated_user = 'JN_ST002-DF01_001'
  ,d_updated_datetime = NOW();
-- ワークテーブルに出力データを格納
TRUNCATE TABLE wk_st002_df01_inventory;
INSERT INTO wk_st002_df01_inventory (commodity_code,stock_quantity,stock_arrival_date,arrival_quantity,allocation,allocation_timestamp)
  SELECT
    sku_code,
    stock_quantity,
    TO_CHAR(stock_arrival_date, 'YYYY-MM-DD'),
    COALESCE(arrival_quantity, 0),
    stock_quantity + COALESCE(arrival_quantity, 0),
    TO_CHAR(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo', 'YYYY-MM-DD"T"HH24:MI:SS".000+09:00"')
  FROM stock_view
  WHERE d_updated_datetime > :sync_datetime
    AND delete_flg = 0;
-- ワークテーブルに分割単位を入れる
WITH temp_parallel_num AS (
  SELECT
     commodity_code
    ,MOD(ROW_NUMBER() OVER (ORDER BY commodity_code) - 1, :split_num) + 1 AS parallel_num
  FROM (SELECT commodity_code FROM wk_st002_df01_inventory)
)
UPDATE wk_st002_df01_inventory AS inv
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE inv.commodity_code = t.commodity_code;