-- 同期済タイムスタンプのUPSERT
INSERT INTO sync_timestamp (
    job_schedule_id,
    file_name,
    sync_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
) VALUES (
    :job_schedule_id,
    :file_name,
    :timestamp,
    :user,
    CURRENT_TIMESTAMP,
    :user,
    CURRENT_TIMESTAMP,
    1
)
ON CONFLICT (job_schedule_id, file_name) DO UPDATE
SET sync_datetime = :timestamp,
    d_updated_user = :user,
    d_updated_datetime = CURRENT_TIMESTAMP,
    d_version = sync_timestamp.d_version + 1;
