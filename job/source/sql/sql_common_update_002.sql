-- 前回同期済タイムスタンプ（一時格納用）のUPSERT
INSERT INTO sync_timestamp (
    job_schedule_id,
    file_name,
    sync_datetime,
    sync_datetime_temp,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
) VALUES (
    :job_schedule_id,
    :file_name,
    '1900-01-01 00:00:00',-- 差分抽出のデフォルト値
    :timestamp,
    :user,
    CURRENT_TIMESTAMP,
    :user,
    CURRENT_TIMESTAMP,
    1
)
ON CONFLICT (job_schedule_id, file_name) DO UPDATE
SET sync_datetime_temp = :timestamp,
    d_updated_user = :user,
    d_updated_datetime = CURRENT_TIMESTAMP,
    d_version = sync_timestamp.d_version + 1;
