-- 3. セット品定期品構成品キー紐づけ情報テーブルUPSERT処理
WITH combined_data AS (
    --  1. 定期品構成品キー全件取得処理の結果を取得
    SELECT
        regular_sale_base.commodity_code AS mail_order_product_cd,
        string_agg(regular_sale_commodity.commodity_code, ',' ORDER BY regular_sale_commodity.commodity_code) AS component_key
    FROM
        oms_readreplica.regular_sale_commodity
    INNER JOIN
        oms_readreplica.regular_sale_base
    ON
        regular_sale_commodity.shop_code = regular_sale_base.shop_code
    AND
        regular_sale_commodity.regular_sale_code = regular_sale_base.regular_sale_code
    GROUP BY
        regular_sale_base.commodity_code

    UNION ALL

    -- 2. セット品構成品キー全件取得処理の結果を取得
    SELECT
        commodity_code AS mail_order_product_cd,
        string_agg(child_commodity_code, ',' ORDER BY child_commodity_code) AS component_key
    FROM
        oms_readreplica.set_commodity_composition
    GROUP BY
        commodity_code
)

INSERT INTO
    mdm.component_link_info (
        mail_order_product_cd,
        component_key,
        update_flg,
        pms_i_ymd,
        pms_i_usr,
        pms_i_class,
        pms_u_ymd,
        pms_u_usr,
        pms_u_class
    )
SELECT
    mail_order_product_cd,
    component_key,
    '1',
    NOW(),
    'job_assign_product_no',
    'job_assign_product_no',
    NOW(),
    'job_assign_product_no',
    'job_assign_product_no'
FROM combined_data
ON CONFLICT (mail_order_product_cd) DO UPDATE SET
    component_key = EXCLUDED.component_key,
    update_flg = '1',
    pms_u_ymd = NOW(),
    pms_u_usr = 'job_assign_product_no',
    pms_u_class = 'job_assign_product_no'
 WHERE mdm.component_link_info.component_key != EXCLUDED.component_key;


-- 4. 単品構成品キー紐づけ情報登録処理
INSERT INTO
    mdm.component_link_info (
        mail_order_product_cd,
        component_key,
        update_flg,
        pms_i_ymd,
        pms_i_usr,
        pms_i_class,
        pms_u_ymd,
        pms_u_usr,
        pms_u_class
    )
SELECT
    product_edit.mail_order_product_cd,
    product_edit.mail_order_product_cd,
    '1',
    NOW(),
    'job_assign_product_no',
    'job_assign_product_no',
    NOW(),
    'job_assign_product_no',
    'job_assign_product_no'
FROM
    mdm.product_edit
LEFT JOIN
    mdm.component_link_info
ON
    mdm.product_edit.mail_order_product_cd = mdm.component_link_info.mail_order_product_cd
WHERE
    mdm.product_edit.set_product_flg = '0'
AND
    mdm.product_edit.product_type != '11'
AND
    mdm.product_edit.mail_order_product_cd IS NOT NULL
AND
    mdm.component_link_info.mail_order_product_cd IS NULL;


-- 5. 商品No紐づけ情報登録処理
WITH max_product_no AS (
    -- 商品No下5桁の最大値の結果を取得
     SELECT
        COALESCE(MAX(CAST(RIGHT(CAST(product_no AS VARCHAR), 5) AS INTEGER)), 0) AS max_code
     FROM mdm.product_no_link_info
)

INSERT INTO mdm.product_no_link_info
SELECT
    data.component_key,
    CAST(
    CASE WHEN product_edit.set_product_flg = '1' THEN CAST(('20' || RIGHT(EXTRACT(YEAR FROM CURRENT_DATE)::TEXT, 2)) AS INTEGER )
         WHEN product_edit.set_product_flg = '0' AND period_set_sales_channel_1 = '50' THEN CAST(('30' || RIGHT(EXTRACT(YEAR FROM CURRENT_DATE)::TEXT, 2)) AS INTEGER )
         ELSE CAST(('10' || RIGHT(EXTRACT(YEAR FROM CURRENT_DATE)::TEXT, 2)) AS INTEGER )
    END
    || LPAD((max_product_no.max_code + row_number() OVER ())::TEXT , 5, '0') AS INTEGER) AS product_no,
    NOW() AS pms_i_ymd,
    'job_assign_product_no' AS pms_i_usr,
    'job_assign_product_no' AS pms_i_class,
    NOW() AS pms_u_ymd,
    'job_assign_product_no' AS pms_u_usr,
    'job_assign_product_no' AS pms_u_class
FROM max_product_no
JOIN (
SELECT
    mdm.component_link_info.component_key AS component_key,
    MIN(product_edit.mail_order_product_cd) AS mail_order_product_cd
FROM
    mdm.component_link_info
LEFT JOIN
    mdm.product_no_link_info
ON
    mdm.component_link_info.component_key = mdm.product_no_link_info.component_key
INNER JOIN
    mdm.product_edit
ON
    mdm.component_link_info.mail_order_product_cd = product_edit.mail_order_product_cd
WHERE
    mdm.product_no_link_info.component_key IS NULL
AND
    mdm.component_link_info.update_flg = '1'
GROUP BY
    mdm.component_link_info.component_key
) AS data ON TRUE
JOIN
    mdm.product_edit
ON
    data.mail_order_product_cd = product_edit.mail_order_product_cd;


-- 6. 商品編集マスタ商品No更新処理
UPDATE
    mdm.product_edit
SET
    product_no = mdm.product_no_link_info.product_no,
    main_product_no = COALESCE(mdm.product_edit.main_product_no, mdm.product_no_link_info.product_no),
    pms_u_ymd = NOW(),
    pms_u_usr = 'job_assign_product_no',
    pms_u_class = 'job_assign_product_no'
FROM
    mdm.product_no_link_info
JOIN
    mdm.component_link_info
ON
    mdm.component_link_info.component_key = mdm.product_no_link_info.component_key
WHERE
    mdm.product_edit.mail_order_product_cd = mdm.component_link_info.mail_order_product_cd
AND
    mdm.component_link_info.update_flg = '1';


-- 7. 構成品キー紐づけ情報更新フラグ更新処理
UPDATE
    mdm.component_link_info
SET
    update_flg = '0',
    pms_u_ymd = NOW(),
    pms_u_usr = 'job_assign_product_no',
    pms_u_class = 'job_assign_product_no'
WHERE
    update_flg = '1';
