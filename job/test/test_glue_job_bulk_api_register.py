#!/usr/bin/env python
# -*- coding: utf-8 -*-
import pytest
import io
import os
import sys
import json
import boto3
from source.glue_job_bulk_api_register import (
    GlueJobBulkApiRegister,
    initialize_env,
    get_params,
    main,
)
from moto import mock_secretsmanager, mock_ssm
from unittest.mock import patch, MagicMock, call
import responses


@pytest.fixture
def s3_bucket():
    """テスト用バケット名を環境変数に設定"""
    os.environ["S3_BUCKET_NAME"] = "s3-dev-dlpf-if-886436956581"
    os.environ["S3_RETRY_LIMIT"] = "3"
    os.environ["S3_RETRY_INTERVAL"] = "1"
    yield
    if "S3_BUCKET_NAME" in os.environ:
        del os.environ["S3_BUCKET_NAME"]
    if "S3_RETRY_LIMIT" in os.environ:
        del os.environ["S3_RETRY_LIMIT"]
    if "S3_RETRY_INTERVAL" in os.environ:
        del os.environ["S3_RETRY_INTERVAL"]
    if "test_api_info" in os.environ:
        del os.environ["test_api_info"]


@pytest.fixture
def glue_job():
    """GlueJobBulkApiRegisterのインスタンス生成"""
    return GlueJobBulkApiRegister("test-jobnet")


@pytest.fixture
def mock_api_secret():
    """Secrets Managerのmock - 実際のAPI接続情報を使用"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        api_secret = {
            "host": "https://api-test.com",
            "client_id": "test_user",
            "client_secret": "test_secret_str",
            "token_endpoint": "/token",
            "bulk_api_path": "/bulk_api/",
        }
        secrets.create_secret(Name="test_api_info", SecretString=json.dumps(api_secret))
        yield secrets


@pytest.fixture
def mock_api_secret_error_token_endpoint():
    """Secrets Managerのmock - 実際のAPI接続情報を使用"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        api_secret = {
            "host": "https://api-test.com",
            "client_id": "test_user",
            "client_secret": "test_secret_str",
            "token_endpoint": "/testtoken",  # ケース51向け
            "bulk_api_path": "/bulk_api/",
        }
        secrets.create_secret(Name="test_api_info", SecretString=json.dumps(api_secret))
        yield secrets


@pytest.fixture
def mock_api_secret_error():
    """Secrets Managerのmock - 実際のAPI接続情報を使用"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        api_secret = {
            "host": "http://localhost",
            "client_id": "test_user",
            "client_secret": "test_secret_str",
            "token_endpoint": "/token",
            "bulk_api_path": "/bulk_api/",
        }
        secrets.create_secret(Name="test_api_info", SecretString=json.dumps(api_secret))
        yield secrets


def set_data(glue_job: GlueJobBulkApiRegister, backup_flag: int, file_name: str):
    """
    テスト用データ・パラメータを初期設定（UTF8→SJIS用）
    Args:
        glue_job: ジョブ
    """
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    if backup_flag == 1:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "api_secret_name",
            api_secret_name,
            "bulk_api_setting",
            bulk_api_setting,
            "input_character_encoding",
            input_character_encoding,
            "file_name",
            file_name,
            "input_file_dir",
            input_file_dir,
            "backup_flag",
            True,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    elif backup_flag == 0:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "api_secret_name",
            api_secret_name,
            "bulk_api_setting",
            bulk_api_setting,
            "input_character_encoding",
            input_character_encoding,
            "file_name",
            file_name,
            "input_file_dir",
            input_file_dir,
            "backup_flag",
            False,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    else:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "api_secret_name",
            api_secret_name,
            "bulk_api_setting",
            bulk_api_setting,
            "input_character_encoding",
            input_character_encoding,
            "file_name",
            file_name,
            "input_file_dir",
            input_file_dir,  # backup_flg指定なし
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]


def test_1_get_params(glue_job):
    set_data(glue_job, 1, "test1.csv")

    # 実行
    params = get_params()

    # 確認
    assert params["api_secret_name"] == "test_api_info"
    assert (
        params["bulk_api_setting"]
        == '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    )
    assert params["input_character_encoding"] == "utf-8"
    assert params["file_name"] == "test1.csv"
    assert params["input_file_dir"] == "saito/input-output/OMS_IN/"
    assert params["backup_flag"] == True
    assert params["backup_file_dir"] == "saito/back-up/OMS_IN/"
    assert params["jobnet_id"] == "test-jobnet"


def test_2_get_params(glue_job):
    set_data(glue_job, 2, "test1.csv")

    # 実行
    params = get_params()

    # 確認
    assert params["api_secret_name"] == "test_api_info"
    assert (
        params["bulk_api_setting"]
        == '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    )
    assert params["input_character_encoding"] == "utf-8"
    assert params["file_name"] == "test1.csv"
    assert params["input_file_dir"] == "saito/input-output/OMS_IN/"
    assert params["backup_flag"] == False
    assert params["backup_file_dir"] == "saito/back-up/OMS_IN/"
    assert params["jobnet_id"] == "test-jobnet"


def test_2_main(glue_job):
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    file_name = "test1_0byte.csv"
    set_data(glue_job, 2, file_name)

    # 実行
    try:
        with patch("boto3.client") as mock_boto_client:
            mock_ssm_client = MagicMock()
            mock_ssm_client.get_parameter.return_value = {
                "Parameter": {
                    "Value": json.dumps(
                        {
                            "process": {"TZ": "Asia/Tokyo"},
                            "aws": {
                                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                                "REGION_NAME": "ap-northeast-1",
                                "S3_RETRY_LIMIT": "3",
                                "S3_RETRY_INTERVAL": "1",
                                "S3_RETRY_MODE": "standard",
                            },
                        }
                    )
                }
            }
            mock_boto_client.return_value = mock_ssm_client

            with patch.object(
                GlueJobBulkApiRegister,
                "get_s3_file",
                return_value={
                    "ContentLength": 0,
                    "Body": io.BytesIO(b"a1,b1,c1\nd1,e1"),
                },
            ):
                main()
    finally:
        # S3ファイル配置したものを削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )
        # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file_dir + file_name,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": "saito/back-up/" + file_name,
            },
        )


@responses.activate
def test_2_3_5_45_48_main_0byte(glue_job, s3_bucket, capsys):
    # 正常系挙動(0byte、バックアップフラグ指定なし)で一括で確認
    backup_flag_mode = 2
    exit_mode = 0
    file_name = "test1_0byte.csv"
    set_data(glue_job, backup_flag_mode, file_name)

    # 実行
    execute_main(glue_job, backup_flag_mode, exit_mode, "", file_name, "")

    # 通信確認
    assert len(responses.calls) == 0

    # ログ確認
    captured = capsys.readouterr()
    assert_success(captured, file_name)


def test_4_get_s3_file_upper_0byte(glue_job, s3_bucket):
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"
    byte_data = b"a1,b1,c1\nd1,e1"
    response = glue_job.get_s3_file(input_file_dir, file_name)

    # 確認
    assert response["ContentLength"] > 0
    assert response["Body"].read() == byte_data


def test_5_get_s3_file_0byte(glue_job, s3_bucket):
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1_0byte.csv"
    response = glue_job.get_s3_file(input_file_dir, file_name)

    # 確認
    assert response["ContentLength"] == 0


def test_6_get_s3_file_error(s3_bucket: None, glue_job, capsys):
    set_data(glue_job, 1, "test1.csv")

    # 実行
    with patch.object(
        glue_job.s3_client, "get_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(BaseException):
            # メイン処理実行
            params = get_params()
            glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=S3ファイル取得)"
        in captured.out
    )
    assert_error(captured, "S3ファイル取得", glue_job.file_name)


def test_7_get_s3_file_retry_success(s3_bucket: None, glue_job, capsys):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"
    input_data_encode_expected = b"a1,b1,c1\nd1,e1"

    # テスト実行
    response = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
    )
    with patch.object(
        glue_job.s3_client,
        "get_object",
        side_effect=[
            Exception("Test Exception"),
            Exception("Test Exception"),
            response,  # 3回目で成功
        ],
    ):
        file_data = glue_job.get_s3_file(input_file_dir, file_name)

    # 確認
    assert file_data["Body"].read() == input_data_encode_expected

    # ログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=S3ファイル取得)"
        in captured.out
    )


def assert_success(captured, file_name: str):
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_bulk_api_register_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_bulk_api_register_003]例外発生しました。"
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_bulk_api_register_001]ジョブが異常終了しました。(ファイル名="
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )


def assert_error(captured, method_name: str, file_name: str):
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    if method_name:
        assert (
            "[ERROR][test-jobnet][E_job_bulk_api_register_002]処理で異常が発生しました。(処理名="
            + method_name
            + ")"
            in captured.out
        )
    assert (
        "[ERROR][test-jobnet][E_job_bulk_api_register_003]例外発生しました。"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_bulk_api_register_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )


def assert_api_log(captured, api_name, request_param, status):
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + api_name
        + ",リクエストパラメータ="
        + request_param
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + api_name
        + ",HTTPステータス="
        + status
        + ")"
        in captured.out
    )


@responses.activate
def test_8_get_access_token_200(glue_job, s3_bucket, mock_api_secret, capsys):
    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}

    status = 200
    responses.add(
        responses.GET,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    api_info = glue_job.get_api_info("test_api_info")
    token = glue_job.get_access_token(api_info)

    # 通信確認
    assert responses.calls[0].request.method == "GET"
    assert (
        responses.calls[0].request.url
        == mock_url
        + "?grant_type=client_credentials&client_id=test_user&client_secret=test_secret_str"
    )
    assert responses.calls[0].response.json()["access_token"] == "token_success"
    assert responses.calls[0].response.status_code == status
    assert token == "token_success"

    # ログ確認
    method_name = "アクセストークン取得"
    assert_api_log(capsys.readouterr(), method_name, "認証情報", str(status))


@responses.activate
def test_9_get_access_token_400(glue_job, s3_bucket, mock_api_secret, capsys):
    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}

    status = 400
    responses.add(
        responses.GET,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        params = get_params()
        glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "GET"
    assert (
        responses.calls[0].request.url
        == mock_url
        + "?grant_type=client_credentials&client_id=test_user&client_secret=test_secret_str"
    )
    assert responses.calls[0].response.status_code == status

    # ログ確認
    method_name = "アクセストークン取得"
    captured = capsys.readouterr()
    assert_api_log(captured, method_name, "認証情報", str(status))
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_10_get_access_token_error(glue_job, s3_bucket, mock_api_secret_error, capsys):
    # mock_api_secret_errorでlocalhostを指定して接続エラー発生させる

    mock_url = "https://api-test.com/token"
    mock_response = {"message": "success", "access_token": "token_success"}

    status = 200
    responses.add(
        responses.GET,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        params = get_params()
        glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "GET"
    assert (
        responses.calls[0].request.url
        == "http://localhost/token"
        + "?grant_type=client_credentials&client_id=test_user&client_secret=test_secret_str"
    )

    # ログ確認
    method_name = "アクセストークン取得"
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + method_name
        + ",リクエストパラメータ="
        + "認証情報"
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + method_name
        + ",HTTPステータス="
        not in captured.out
    )
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_11_create_bulk_api_job_200(glue_job, s3_bucket, mock_api_secret, capsys):
    mock_url = "https://api-test.com/bulk_api/"
    mock_response = {"id": 100}

    status = 200
    responses.add(
        responses.POST,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    params = get_params()
    api_info = glue_job.get_api_info("test_api_info")
    id = glue_job.create_bulk_api_job(
        api_info, "token_success", json.loads(params["bulk_api_setting"])
    )

    # 通信確認
    assert responses.calls[0].request.method == "POST"
    assert responses.calls[0].request.url == mock_url
    request_param = '{"operation": "upsert", "object": "Product__c", "contentType": "CSV", "lineEnding": "LF", "externalIdFieldName": "ProductCode__c"}'
    assert responses.calls[0].request.body == request_param
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "application/json",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"
    assert id == 100
    assert responses.calls[0].response.status_code == status

    # ログ確認
    method_name = "BulkAPIジョブ作成"
    assert_api_log(capsys.readouterr(), method_name, request_param, str(status))


@responses.activate
def test_12_create_bulk_api_job_noid(glue_job, s3_bucket, mock_api_secret, capsys):
    mock_url = "https://api-test.com/bulk_api/"
    mock_response = {"idtest": 100}

    status = 200
    responses.add(
        responses.POST,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            params = get_params()
            glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "POST"
    assert responses.calls[0].request.url == mock_url
    request_param = '{"operation": "upsert", "object": "Product__c", "contentType": "CSV", "lineEnding": "LF", "externalIdFieldName": "ProductCode__c"}'
    assert responses.calls[0].request.body == request_param
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "application/json",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[0].response.status_code == status

    # ログ確認
    method_name = "BulkAPIジョブ作成"
    captured = capsys.readouterr()
    assert_api_log(captured, method_name, request_param, str(status))
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_13_create_bulk_api_job_401(glue_job, s3_bucket, mock_api_secret, capsys):
    mock_url = "https://api-test.com/bulk_api/"
    mock_response = {"id": 100}

    status = 401
    responses.add(
        responses.POST,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            params = get_params()
            glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "POST"
    assert responses.calls[0].request.url == mock_url
    request_param = '{"operation": "upsert", "object": "Product__c", "contentType": "CSV", "lineEnding": "LF", "externalIdFieldName": "ProductCode__c"}'
    assert responses.calls[0].request.body == request_param
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "application/json",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[0].response.status_code == status

    # ログ確認
    method_name = "BulkAPIジョブ作成"
    captured = capsys.readouterr()
    assert_api_log(captured, method_name, request_param, str(status))
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_14_create_bulk_api_job_error(
    glue_job, s3_bucket, mock_api_secret_error, capsys
):
    # mock_api_secret_errorでlocalhostを指定して接続エラー発生させる

    mock_url = "https://api-test.com/bulk_api/"
    mock_response = {}

    status = 200
    responses.add(
        responses.POST,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            params = get_params()
            glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "POST"
    assert responses.calls[0].request.url == "http://localhost/bulk_api/"
    request_param = '{"operation": "upsert", "object": "Product__c", "contentType": "CSV", "lineEnding": "LF", "externalIdFieldName": "ProductCode__c"}'
    assert responses.calls[0].request.body == request_param
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "application/json",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"

    # ログ確認
    method_name = "BulkAPIジョブ作成"
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + method_name
        + ",リクエストパラメータ="
        + request_param
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + method_name
        + ",HTTPステータス="
        not in captured.out
    )
    assert_error(captured, method_name, glue_job.file_name)


def mock_api_job_common():
    # アクセストークン取得
    responses.add(
        responses.GET,
        "https://api-test.com/token",
        status=200,
        json={"message": "success", "access_token": "token_success"},
    )

    # BulkAPIジョブ作成
    responses.add(
        responses.POST,
        "https://api-test.com/bulk_api/",
        status=200,
        json={"id": "100"},
    )

    # BulkAPI CSVアップロード
    responses.add(
        responses.PUT,
        "https://api-test.com/bulk_api/100/batches/",
        status=201,
        json={},
    )

    # BulkAPIステータス更新
    responses.add(
        responses.PATCH,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={},
    )


def mock_api_job_complete():
    mock_api_job_common()

    # BulkAPIステータス確認
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "JobComplete"},
    )


def mock_api_job_failed_or_abort(state: str):
    # stateは"Failed"もしくは"Aborted"を指定する
    mock_api_job_common()

    # BulkAPIステータス確認
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": state},
    )

    # BulkAPI未処理レコード取得
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/unprocessedrecords/",
        status=200,
        body=b"a,b,c\nd,e,f",
        content_type="text/csv",
    )

    # BulkAPI失敗レコード取得
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/failedResults/",
        status=200,
        body=b"a,b,c\nd,e,f\ng,h,i",
        content_type="text/csv",
    )


@responses.activate
def test_15_18_21_42_49_main_job_complete(glue_job, s3_bucket, mock_api_secret, capsys):
    # 正常系挙動(0byte以外、バックアップフラグTrue、BulkAPIステータス確認でJobComplete)で一括で確認
    mock_api_job_complete()
    csv_data = b"a1,b1,c1\nd1,e1"

    # 実行
    with patch.object(glue_job, "get_access_token", return_value="token_success"):
        with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
            execute_main(glue_job, 1, 0, "", "test1.csv", "JobComplete")

    assert len(responses.calls) == 3

    # BulkAPI CSVアップロード確認
    status = 201
    captured = capsys.readouterr()
    assert responses.calls[0].request.method == "PUT"
    assert (
        responses.calls[0].request.url == "https://api-test.com/bulk_api/100/batches/"
    )
    assert responses.calls[0].request.body == csv_data
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "text/csv",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[0].response.status_code == status
    assert_api_log(captured, "BulkAPI_CSVアップロード", glue_job.file_name, str(status))

    # BulkAPIステータス更新確認
    status = 200
    assert responses.calls[1].request.method == "PATCH"
    assert responses.calls[1].request.url == "https://api-test.com/bulk_api/100/"
    request_param = '{"state": "UploadComplete"}'
    assert responses.calls[1].request.body == request_param
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "application/json",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[1].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[1].response.status_code == status
    assert_api_log(captured, "BulkAPIステータス更新", request_param, str(status))

    # BulkAPIステータス確認
    status = 200
    assert responses.calls[2].request.method == "GET"
    assert responses.calls[2].request.url == "https://api-test.com/bulk_api/100/"
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[2].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[2].response.status_code == status
    assert responses.calls[2].response.json()["state"] == "JobComplete"
    assert_api_log(captured, "BulkAPIステータス確認", "なし", str(status))

    # 正常終了確認
    assert_success(captured, glue_job.file_name)


@responses.activate
def test_16_upload_bulk_api_csv_401(glue_job, s3_bucket, mock_api_secret, capsys):
    mock_url = "https://api-test.com/bulk_api/100/batches/"
    mock_response = {}
    csv_data = b"a1,b1,c1\nd1,e1"

    status = 401
    responses.add(
        responses.PUT,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
                params = get_params()
                glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "PUT"
    assert responses.calls[0].request.url == mock_url
    assert responses.calls[0].request.body == csv_data
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "text/csv",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[0].response.status_code == status

    # ログ確認
    method_name = "BulkAPI_CSVアップロード"
    captured = capsys.readouterr()
    assert_api_log(captured, method_name, glue_job.file_name, str(status))
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_17_upload_bulk_api_csv_error(
    glue_job, s3_bucket, mock_api_secret_error, capsys
):
    # mock_api_secret_errorでlocalhostを指定して接続エラー発生させる

    mock_url = "https://api-test.com/bulk_api/100/batches/"
    mock_response = {}
    csv_data = b"a1,b1,c1\nd1,e1"

    status = 200
    responses.add(
        responses.POST,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
                params = get_params()
                glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "PUT"
    assert responses.calls[0].request.url == "http://localhost/bulk_api/100/batches/"
    assert responses.calls[0].request.body == csv_data
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "text/csv",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"

    # ログ確認
    method_name = "BulkAPI_CSVアップロード"
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + method_name
        + ",リクエストパラメータ="
        + glue_job.file_name
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + method_name
        + ",HTTPステータス="
        not in captured.out
    )
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_19_update_bulk_api_status_401(glue_job, s3_bucket, mock_api_secret, capsys):
    mock_url = "https://api-test.com/bulk_api/100/"
    mock_response = {}

    status = 401
    responses.add(
        responses.PATCH,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
                with patch.object(glue_job, "upload_bulk_api_csv", return_value=None):
                    params = get_params()
                    glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "PATCH"
    assert responses.calls[0].request.url == "https://api-test.com/bulk_api/100/"
    request_param = '{"state": "UploadComplete"}'
    assert responses.calls[0].request.body == request_param
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "application/json",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[0].response.status_code == status

    # ログ確認
    method_name = "BulkAPIステータス更新"
    captured = capsys.readouterr()
    assert_api_log(captured, method_name, request_param, str(status))
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_20_update_bulk_api_status_error(
    glue_job, s3_bucket, mock_api_secret_error, capsys
):
    # mock_api_secret_errorでlocalhostを指定して接続エラー発生させる

    mock_url = "https://api-test.com/bulk_api/100/"
    mock_response = {}

    status = 200
    responses.add(
        responses.PATCH,
        mock_url,
        status=status,
        json=mock_response,
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
                with patch.object(glue_job, "upload_bulk_api_csv", return_value=None):
                    params = get_params()
                    glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "PATCH"
    assert responses.calls[0].request.url == "http://localhost/bulk_api/100/"
    request_param = '{"state": "UploadComplete"}'
    assert responses.calls[0].request.body == request_param
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "application/json",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"

    # ログ確認
    method_name = "BulkAPIステータス更新"
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + method_name
        + ",リクエストパラメータ="
        + request_param
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + method_name
        + ",HTTPステータス="
        not in captured.out
    )
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_22_23_24_get_bulk_api_status(glue_job, mock_api_secret):
    set_data(glue_job, "2", "test1.csv")

    # 1回目は"InProgress"、2回目は"UploadComplete"、3回目は"Failed"になるようにする
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "InProgress"},
    )
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "UploadComplete"},
    )
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "Failed"},
    )

    # 実行
    params = get_params()
    api_info = glue_job.get_api_info("test_api_info")
    bulk_api_setting = json.loads(params["bulk_api_setting"])
    with patch("time.sleep", return_value=None) as mock_sleep:
        state = glue_job.get_bulk_api_status(
            api_info, "token_success", bulk_api_setting, "100"
        )

        assert state == "Failed"

        # time.sleepが3回呼ばれ、10秒ごとになることを確認
        assert mock_sleep.call_count == 3
        mock_sleep.assert_has_calls([call(10), call(10), call(10)])


@responses.activate
def test_22_23_25_get_bulk_api_status(glue_job, mock_api_secret):
    set_data(glue_job, "2", "test1.csv")

    # 1回目は"UploadComplete"、2回目は"InProgress"、3回目は"Aborted"になるようにする
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "UploadComplete"},
    )
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "InProgress"},
    )
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "Aborted"},
    )

    # 実行
    params = get_params()
    api_info = glue_job.get_api_info("test_api_info")
    bulk_api_setting = json.loads(params["bulk_api_setting"])
    with patch("time.sleep", return_value=None) as mock_sleep:
        state = glue_job.get_bulk_api_status(
            api_info, "token_success", bulk_api_setting, "100"
        )

        assert state == "Aborted"

        # time.sleepが3回呼ばれ、10秒ごとになることを確認
        assert mock_sleep.call_count == 3
        mock_sleep.assert_has_calls([call(10), call(10), call(10)])


@responses.activate
def test_24_30_33_36_39_50_main_failed(glue_job, s3_bucket, mock_api_secret, capsys):
    # 異常系挙動(0byte以外、バックアップフラグFalse、BulkAPIステータス確認でFailed)で一括で確認
    target_state = "Failed"
    mock_api_job_failed_or_abort(target_state)
    csv_data = b"a1,b1,c1\nd1,e1"

    # 実行
    with patch.object(glue_job, "get_access_token", return_value="token_success"):
        with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
            execute_main(glue_job, 0, 1, "", "test1.csv", target_state)

    # 確認
    captured = capsys.readouterr()
    assert_failed_or_abort(glue_job, target_state, captured)


@responses.activate
def test_25_main_abort(glue_job, s3_bucket, mock_api_secret, capsys):
    target_state = "Aborted"
    mock_api_job_failed_or_abort(target_state)
    csv_data = b"a1,b1,c1\nd1,e1"

    # 実行
    with patch.object(glue_job, "get_access_token", return_value="token_success"):
        with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
            execute_main(glue_job, 0, 1, "", "test1.csv", target_state)

    # 確認
    captured = capsys.readouterr()
    assert_failed_or_abort(glue_job, target_state, captured)


@responses.activate
def test_26_get_bulk_api_status_invalid(glue_job, s3_bucket, mock_api_secret, capsys):
    set_data(glue_job, "2", "test1.csv")
    mock_api_job_common()

    status = 200
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=status,
        json={"state": "Dummy"},
    )

    # 実行
    with pytest.raises(BaseException):
        params = get_params()
        glue_job.execute(params)

    captured = capsys.readouterr()
    assert_error(captured, "BulkAPIステータス確認", glue_job.file_name)


@responses.activate
def test_27_get_bulk_api_status_none(glue_job, s3_bucket, mock_api_secret, capsys):
    set_data(glue_job, "2", "test1.csv")
    mock_api_job_common()

    status = 200
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=status,
        json={},
    )

    # 実行
    with pytest.raises(BaseException):
        params = get_params()
        glue_job.execute(params)

    captured = capsys.readouterr()
    assert_error(captured, "BulkAPIステータス確認", glue_job.file_name)


@responses.activate
def test_28_get_bulk_api_status_401(glue_job, s3_bucket, mock_api_secret, capsys):
    mock_url = "https://api-test.com/bulk_api/100/"
    mock_response = {}

    status = 401
    responses.add(
        responses.GET,
        mock_url,
        status=status,
        json={},
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
                with patch.object(glue_job, "upload_bulk_api_csv", return_value=None):
                    with patch.object(
                        glue_job, "update_bulk_api_status", return_value=None
                    ):
                        params = get_params()
                        glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "GET"
    assert responses.calls[0].request.url == "https://api-test.com/bulk_api/100/"
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[0].response.status_code == status

    # ログ確認
    method_name = "BulkAPIステータス確認"
    captured = capsys.readouterr()
    assert_api_log(captured, method_name, "なし", str(status))
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_29_get_bulk_api_status_error(
    glue_job, s3_bucket, mock_api_secret_error, capsys
):
    # mock_api_secret_errorでlocalhostを指定して接続エラー発生させる

    mock_url = "https://api-test.com/bulk_api/100/"
    mock_response = {}

    status = 200
    responses.add(
        responses.GET,
        mock_url,
        status=status,
        json={},
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
                with patch.object(glue_job, "upload_bulk_api_csv", return_value=None):
                    with patch.object(
                        glue_job, "update_bulk_api_status", return_value=None
                    ):
                        params = get_params()
                        glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "GET"
    assert responses.calls[0].request.url == "http://localhost/bulk_api/100/"
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"

    # ログ確認
    method_name = "BulkAPIステータス確認"
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + method_name
        + ",リクエストパラメータ="
        + "なし"
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + method_name
        + ",HTTPステータス="
        not in captured.out
    )
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_31_get_bulk_api_unprocessed_records_401(
    glue_job, s3_bucket, mock_api_secret, capsys
):
    # APIモック化
    mock_api_job_common()
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "Failed"},
    )
    status = 401
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/unprocessedrecords/",
        status=status,
        body=b"a,b,c\nd,e,f",
        content_type="text/csv",
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        params = get_params()
        glue_job.execute(params)

    # 通信確認
    assert len(responses.calls) == 6
    assert responses.calls[5].request.method == "GET"
    assert (
        responses.calls[5].request.url
        == "https://api-test.com/bulk_api/100/unprocessedrecords/"
    )
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[5].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[5].response.status_code == status

    # ログ確認
    method_name = "BulkAPI未処理レコード取得"
    captured = capsys.readouterr()
    assert_api_log(captured, method_name, "なし", str(status))
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_32_get_bulk_api_unprocessed_records_error(
    glue_job, s3_bucket, mock_api_secret_error, capsys
):
    # mock_api_secret_errorでlocalhostを指定して接続エラー発生させる

    mock_url = "https://api-test.com/bulk_api/100/unprocessedrecords/"
    mock_response = {}

    status = 200
    responses.add(
        responses.GET,
        mock_url,
        status=status,
        json={},
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
                with patch.object(glue_job, "upload_bulk_api_csv", return_value=None):
                    with patch.object(
                        glue_job, "update_bulk_api_status", return_value=None
                    ):
                        with patch.object(
                            glue_job, "get_bulk_api_status", return_value="Failed"
                        ):
                            params = get_params()
                            glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "GET"
    assert (
        responses.calls[0].request.url
        == "http://localhost/bulk_api/100/unprocessedrecords/"
    )
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"

    # ログ確認
    method_name = "BulkAPI未処理レコード取得"
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + method_name
        + ",リクエストパラメータ="
        + "なし"
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + method_name
        + ",HTTPステータス="
        not in captured.out
    )
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_34_put_s3_unprocessed_records_error(
    s3_bucket: None, mock_api_secret, glue_job, capsys
):
    file_name = "test1.csv"
    set_data(glue_job, 2, file_name)
    mock_api_job_failed_or_abort("Failed")

    # テスト実行
    with patch.object(
        glue_job.s3_client, "put_object", side_effect=Exception("Test Exception")
    ):
        with patch.object(
            glue_job,
            "get_s3_file",
            return_value={"ContentLength": 1, "Body": io.BytesIO(b"a1,b1,c1\nd1,e1")},
        ):
            with pytest.raises(BaseException):
                # メイン処理実行
                params = get_params()
                glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=S3未処理レコード配置)"
        in captured.out
    )
    assert_error(captured, "S3未処理レコード配置", file_name)


def test_35_put_s3_unprocessed_records_retry_success(s3_bucket: None, glue_job, capsys):
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test.csv"
    input_file_data = b"test\ndata"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "put_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.put_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=output_file_dir + file_name,
                    Body=input_file_data,
                ),  # 3回目で成功
            ],
        ):
            glue_job.put_s3_unprocessed_records(
                output_file_dir, file_name, input_file_data
            )

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert response["Body"].read() == input_file_data
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=S3未処理レコード配置)"
            in captured.out
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


@responses.activate
def test_37_get_bulk_api_failed_records_401(
    glue_job, s3_bucket, mock_api_secret, capsys
):
    # APIモック化
    mock_api_job_common()
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/",
        status=200,
        json={"state": "Failed"},
    )
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/unprocessedrecords/",
        status=200,
        body=b"a,b,c\nd,e,f",
        content_type="text/csv",
    )
    status = 401
    responses.add(
        responses.GET,
        "https://api-test.com/bulk_api/100/failedResults/",
        status=status,
        body=b"a,b,c\nd,e,f\ng,h,i",
        content_type="text/csv",
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with patch.object(glue_job, "put_s3_unprocessed_records", return_value=None):
        with pytest.raises(BaseException):
            params = get_params()
            glue_job.execute(params)

    # 通信確認
    assert len(responses.calls) == 7
    assert responses.calls[6].request.method == "GET"
    assert (
        responses.calls[6].request.url
        == "https://api-test.com/bulk_api/100/failedResults/"
    )
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[6].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[6].response.status_code == status

    # ログ確認
    method_name = "BulkAPI失敗レコード取得"
    captured = capsys.readouterr()
    assert_api_log(captured, method_name, "なし", str(status))
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_38_get_bulk_api_failed_records_error(
    glue_job, s3_bucket, mock_api_secret_error, capsys
):
    # mock_api_secret_errorでlocalhostを指定して接続エラー発生させる

    mock_url = "https://api-test.com/bulk_api/100/failedResults/"
    mock_response = {}

    status = 200
    responses.add(
        responses.GET,
        mock_url,
        status=status,
        json={},
    )

    # 実行
    set_data(glue_job, 2, "test1.csv")
    with pytest.raises(BaseException):
        with patch.object(glue_job, "get_access_token", return_value="token_success"):
            with patch.object(glue_job, "create_bulk_api_job", return_value="100"):
                with patch.object(glue_job, "upload_bulk_api_csv", return_value=None):
                    with patch.object(
                        glue_job, "update_bulk_api_status", return_value=None
                    ):
                        with patch.object(
                            glue_job, "get_bulk_api_status", return_value="Failed"
                        ):
                            with patch.object(
                                glue_job,
                                "get_bulk_api_unprocessed_records",
                                return_value=b"a,b,c\nd,e,f",
                            ):
                                with patch.object(
                                    glue_job,
                                    "put_s3_unprocessed_records",
                                    return_value=None,
                                ):
                                    params = get_params()
                                    glue_job.execute(params)

    # 通信確認
    assert responses.calls[0].request.method == "GET"
    assert (
        responses.calls[0].request.url == "http://localhost/bulk_api/100/failedResults/"
    )
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"

    # ログ確認
    method_name = "BulkAPI失敗レコード取得"
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + method_name
        + ",リクエストパラメータ="
        + "なし"
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + method_name
        + ",HTTPステータス="
        not in captured.out
    )
    assert_error(captured, method_name, glue_job.file_name)


@responses.activate
def test_40_put_s3_failed_records_error(
    s3_bucket: None, mock_api_secret, glue_job, capsys
):
    file_name = "test1.csv"
    set_data(glue_job, 2, file_name)
    mock_api_job_failed_or_abort("Failed")

    # テスト実行
    with patch.object(
        glue_job.s3_client, "put_object", side_effect=Exception("Test Exception")
    ):
        with patch.object(
            glue_job,
            "get_s3_file",
            return_value={"ContentLength": 1, "Body": io.BytesIO(b"a1,b1,c1\nd1,e1")},
        ):
            with patch.object(
                glue_job,
                "put_s3_unprocessed_records",
                return_value=None,
            ):
                with pytest.raises(BaseException):
                    # メイン処理実行
                    params = get_params()
                    glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=S3失敗レコード配置)"
        in captured.out
    )
    assert_error(captured, "S3失敗レコード配置", file_name)


def test_41_put_s3_failed_records_retry_success(s3_bucket: None, glue_job, capsys):
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test.csv"
    input_file_data = b"test\ndata"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "put_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.put_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=output_file_dir + file_name,
                    Body=input_file_data,
                ),  # 3回目で成功
            ],
        ):
            glue_job.put_s3_failed_records(output_file_dir, file_name, input_file_data)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert response["Body"].read() == input_file_data
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=S3失敗レコード配置)"
            in captured.out
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


def test_43_backup_error(s3_bucket: None, glue_job, capsys):
    mock_api_job_complete()

    # 実行
    with patch.object(
        glue_job.s3_client, "copy_object", side_effect=Exception("Test Exception")
    ):
        execute_main(
            glue_job, 1, 1, "backup_input_file", "test1_0byte.csv", "JobComplete"
        )

    # ログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=インプットファイルバックアップ)"
        in captured.out
    )
    assert_error(captured, "インプットファイルバックアップ", glue_job.file_name)


def test_44_backup_retry_success(s3_bucket: None, glue_job, capsys):
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    file_name = "test1.csv"
    input_data_encode_expected = b"a1,b1,c1\nd1,e1"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "copy_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.copy_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + file_name,
                    CopySource={
                        "Bucket": os.environ["S3_BUCKET_NAME"],
                        "Key": input_file_dir + file_name,
                    },
                ),  # 3回目で成功
            ],
        ):
            glue_job.backup_input_file(input_file_dir, backup_file_dir, file_name)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )
        assert response["Body"].read() == input_data_encode_expected
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=インプットファイルバックアップ)"
            in captured.out
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )


def test_46_delete_error(s3_bucket: None, glue_job, capsys):
    mock_api_job_complete()

    # 実行
    with patch.object(
        glue_job.s3_client, "delete_object", side_effect=Exception("Test Exception")
    ):
        execute_main(
            glue_job, 0, 1, "delete_input_file", "test1_0byte.csv", "JobComplete"
        )

    # ログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=インプットファイル削除)"
        in captured.out
    )
    assert_error(captured, "インプットファイル削除", glue_job.file_name)


def test_47_delete_retry_success(s3_bucket: None, glue_job, capsys):
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "delete_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.delete_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=input_file_dir + file_name,
                ),  # 3回目で成功
            ],
        ):
            glue_job.delete_input_file(input_file_dir, file_name)

        # インプットファイル削除されたか確認
        with pytest.raises(Exception):
            glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
            )

        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][test-jobnet][I_job_bulk_api_register_003]処理をリトライしました。(処理名=インプットファイル削除)"
            in captured.out
        )
    finally:
        # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file_dir + file_name,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": "saito/back-up/" + file_name,
            },
        )


@responses.activate
def test_51_main_endpoint_error(
    glue_job, s3_bucket, mock_api_secret_error_token_endpoint, capsys
):
    # 正常系挙動(0byte以外、バックアップフラグTrue、BulkAPIステータス確認でJobComplete)で一括で確認
    mock_api_job_complete()
    csv_data = b"a1,b1,c1\nd1,e1"

    # 実行
    execute_main(glue_job, 0, 1, "", "test1.csv", "")

    # 通信確認
    assert len(responses.calls) == 1
    assert responses.calls[0].request.method == "GET"
    assert (
        responses.calls[0].request.url
        == "https://api-test.com/testtoken"
        + "?grant_type=client_credentials&client_id=test_user&client_secret=test_secret_str"
    )

    # ログ確認
    method_name = "アクセストークン取得"
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_004]APIを開始します。(API名="
        + method_name
        + ",リクエストパラメータ="
        + "認証情報"
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_bulk_api_register_005]APIが完了しました。(API名="
        + method_name
        + ",HTTPステータス="
        not in captured.out
    )
    assert_error(captured, method_name, glue_job.file_name)


def test_52_value_error_api_secret_name(s3_bucket: None, glue_job):
    # api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        # "api_secret_name",
        # api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-ifdata-742862712683",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            initialize_env()
            get_params()
        assert str(exc_info.value) == "Required parameter 'api_secret_name' is missing"


def test_53_value_error_bulk_api_setting(s3_bucket: None, glue_job):
    api_secret_name = "test_api_info"
    # bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        # "bulk_api_setting",
        # bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-ifdata-742862712683",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            initialize_env()
            get_params()
        assert str(exc_info.value) == "Required parameter 'bulk_api_setting' is missing"


def test_54_value_error_input_character_encoding(s3_bucket: None, glue_job):
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    # input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        # "input_character_encoding",
        # input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-ifdata-742862712683",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            initialize_env()
            get_params()
        assert (
            str(exc_info.value)
            == "Required parameter 'input_character_encoding' is missing"
        )


def test_55_value_error_file_name(s3_bucket: None, glue_job):
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    # file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        # "file_name",
        # file_name,
        "input_file_dir",
        input_file_dir,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-ifdata-742862712683",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            initialize_env()
            get_params()
        assert str(exc_info.value) == "Required parameter 'file_name' is missing"


def test_56_value_error_input_file_dir(s3_bucket: None, glue_job):
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    # input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        # "input_file_dir",
        # input_file_dir,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-ifdata-742862712683",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            initialize_env()
            get_params()
        assert str(exc_info.value) == "Required parameter 'input_file_dir' is missing"


def test_57_value_error_backup_file_dir(s3_bucket: None, glue_job):
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    # backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        # "backup_file_dir",
        # backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-ifdata-742862712683",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            initialize_env()
            get_params()
        assert str(exc_info.value) == "Required parameter 'backup_file_dir' is missing"


def test_58_value_error_jobnet_id(s3_bucket: None, glue_job):
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_file_dir",
        backup_file_dir,
        # "jobnet_id",
        # glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-ifdata-742862712683",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            initialize_env()
            get_params()
        assert str(exc_info.value) == "Required parameter 'jobnet_id' is missing"

def test_check_param_backup_file_dir1(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」のときはTrueと扱うこと
    """
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_bulk_api_register",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_flag",
        "TRUE",
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == True
    except ValueError:
        assert False

def test_check_param_backup_file_dir2(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「true」のときはTrueと扱うこと
    """
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_bulk_api_register",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_flag",
        "true",
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == True
    except ValueError:
        assert False

def test_check_param_backup_file_dir3(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「FALSE」のときはFalseと扱うこと
    """
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_bulk_api_register",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_flag",
        "FALSE",
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == False
    except ValueError:
        assert False

def test_check_param_backup_file_dir4(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグを指定しない場合、バックアップフラグはFalseと扱うこと
    """
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_bulk_api_register",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == False
    except ValueError:
        assert False

def test_value_error_main(s3_bucket: None, glue_job):
    api_secret_name = "test_api_info"
    bulk_api_setting = '{"interval":"10","request":{"operation":"upsert","object":"Product__c","contentType":"CSV","lineEnding":"LF","externalIdFieldName":"ProductCode__c"}}'
    input_character_encoding = "utf-8"
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "api_secret_name",
        api_secret_name,
        "bulk_api_setting",
        bulk_api_setting,
        "input_character_encoding",
        input_character_encoding,
        "file_name",
        file_name,
        "input_file_dir",
        input_file_dir,
        "backup_file_dir",
        backup_file_dir,
        # "jobnet_id",
        # glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-ifdata-742862712683",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(SystemExit):
            main()


def assert_failed_or_abort(glue_job, target_state, captured):

    assert len(responses.calls) == 5

    # BulkAPI CSVアップロード確認
    status = 201

    assert responses.calls[0].request.method == "PUT"
    assert (
        responses.calls[0].request.url == "https://api-test.com/bulk_api/100/batches/"
    )
    assert responses.calls[0].request.body == b"a1,b1,c1\nd1,e1"
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "text/csv",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[0].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[0].response.status_code == status
    assert_api_log(captured, "BulkAPI_CSVアップロード", glue_job.file_name, str(status))

    # BulkAPIステータス更新確認
    status = 200
    assert responses.calls[1].request.method == "PATCH"
    assert responses.calls[1].request.url == "https://api-test.com/bulk_api/100/"
    request_param = '{"state": "UploadComplete"}'
    assert responses.calls[1].request.body == request_param
    expected_headers = {
        "Authorization": "Bearer token_success",
        "Content-Type": "application/json",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[1].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[1].response.status_code == status
    assert_api_log(captured, "BulkAPIステータス更新", request_param, str(status))

    # BulkAPIステータス確認
    status = 200
    assert responses.calls[2].request.method == "GET"
    assert responses.calls[2].request.url == "https://api-test.com/bulk_api/100/"
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[2].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[2].response.status_code == status
    assert responses.calls[2].response.json()["state"] == target_state
    assert_api_log(captured, "BulkAPIステータス確認", "なし", str(status))

    # BulkAPI未処理レコード取得
    status = 200
    assert responses.calls[3].request.method == "GET"
    assert (
        responses.calls[3].request.url
        == "https://api-test.com/bulk_api/100/unprocessedrecords/"
    )
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[3].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[3].response.status_code == status
    assert responses.calls[3].response.content == b"a,b,c\nd,e,f"
    assert_api_log(captured, "BulkAPI未処理レコード取得", "なし", str(status))

    # BulkAPI失敗レコード取得
    status = 200
    assert responses.calls[4].request.method == "GET"
    assert (
        responses.calls[4].request.url
        == "https://api-test.com/bulk_api/100/failedResults/"
    )
    expected_headers = {
        "Authorization": "Bearer token_success",
    }
    for key, value in expected_headers.items():
        assert (
            responses.calls[4].request.headers[key] == value
        ), f"Header {key} does not match"
    assert responses.calls[4].response.status_code == status
    assert responses.calls[4].response.content == b"a,b,c\nd,e,f\ng,h,i"
    assert_api_log(captured, "BulkAPI失敗レコード取得", "なし", str(status))

    # 異常終了確認
    assert_error(captured, None, glue_job.file_name)


def execute_main(
    glue_job,
    backup_flag_mode: int,
    exit_mode: int,
    error_method: str,
    file_name: str,
    target_state: str,
):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data(glue_job, backup_flag_mode, file_name)
    params = get_params()
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    byte_data = b""
    if file_name == "test1.csv":
        byte_data = b"a1,b1,c1\nd1,e1"
    backup_done = False
    unprocessed_done = False
    failed_done = False

    try:
        # メイン処理実行
        backup_flag = params["backup_flag"]
        if backup_flag_mode == 1:
            assert backup_flag
        else:
            assert not backup_flag

        if exit_mode == 1:
            with pytest.raises(BaseException):
                glue_job.execute(params)
        else:
            glue_job.execute(params)

        if backup_flag and error_method != "backup_input_file":
            # バックアップされたかを確認
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
            backup_done = True
            assert response["Body"].read() == byte_data
        else:
            # バックアップされていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
                )

        if exit_mode == 1:
            # インプットファイル削除されていないことを確認
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
            )
            assert response["Body"].read() == byte_data
        else:
            # インプットファイル削除されたことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
                )

        if target_state == "JobComplete":
            # 未処理レコードが配置されていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + "unprocessed_" + file_name,
                )

            # 失敗レコードが配置されていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + "failed_" + file_name,
                )
        elif target_state == "Failed" or target_state == "Aborted":
            # 未処理レコードが配置されているかを確認
            if error_method == "put_s3_unprocessed_records":
                with pytest.raises(Exception):
                    glue_job.s3_client.get_object(
                        Bucket=os.environ["S3_BUCKET_NAME"],
                        Key=backup_file_dir + "unprocessed_" + file_name,
                    )
            else:
                response = glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + "unprocessed_" + file_name,
                )
                unprocessed_done = True
                assert response["Body"].read() == b"a,b,c\nd,e,f"

            # 失敗レコードが配置されていることを確認
            if error_method == "put_s3_failed_records":
                with pytest.raises(Exception):
                    glue_job.s3_client.get_object(
                        Bucket=os.environ["S3_BUCKET_NAME"],
                        Key=backup_file_dir + "failed_" + file_name,
                    )
            else:
                response = glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + "failed_" + file_name,
                )
                failed_done = True
                assert response["Body"].read() == b"a,b,c\nd,e,f\ng,h,i"
    finally:
        if backup_done and error_method != "backup_input_file":
            # バックアップしたファイルを削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
        if exit_mode != 1:
            # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
            glue_job.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file_dir + file_name,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": "saito/back-up/" + file_name,
                },
            )
        if target_state == "Failed" or target_state == "Aborted":
            if unprocessed_done:
                glue_job.s3_client.delete_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + "unprocessed_" + file_name,
                )
            if failed_done:
                glue_job.s3_client.delete_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + "failed_" + file_name,
                )
