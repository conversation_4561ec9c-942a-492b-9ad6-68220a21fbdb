import pytest
import os
import sys
import json
import io
import zipfile
from source.glue_job_file_compress import (
    GlueJobFileCompress,
    get_params,
    main,
)
from unittest.mock import patch, MagicMock


@pytest.fixture
def s3_bucket():
    """テスト用バケット名を環境変数に設定"""
    os.environ["S3_BUCKET_NAME"] = "s3-dev-dlpf-if-886436956581"
    os.environ["S3_RETRY_LIMIT"] = "3"
    os.environ["S3_RETRY_INTERVAL"] = "1"
    yield
    if "S3_BUCKET_NAME" in os.environ:
        del os.environ["S3_BUCKET_NAME"]
    if "S3_RETRY_LIMIT" in os.environ:
        del os.environ["S3_RETRY_LIMIT"]
    if "S3_RETRY_INTERVAL" in os.environ:
        del os.environ["S3_RETRY_INTERVAL"]


@pytest.fixture
def glue_job():
    """GlueJobFileCompressのインスタンス生成"""
    return GlueJobFileCompress("JN_PR002-DD01_001")


def set_data_default(glue_job: GlueJobFileCompress, backup_flag: int):
    """
    テスト用データ・パラメータを初期設定
    Args:
        glue_job: ジョブ
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    if backup_flag == 1:
        sys.argv = [
            "glue_job_file_compress",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_name",
            output_file_name,
            "output_file_dir",
            output_file_dir,
            "backup_flag",
            True,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    elif backup_flag == 0:
        sys.argv = [
            "glue_job_file_compress",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_name",
            output_file_name,
            "output_file_dir",
            output_file_dir,
            "backup_flag",
            False,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    else:
        sys.argv = [
            "glue_job_file_compress",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_name",
            output_file_name,
            "output_file_dir",
            output_file_dir,  # backup_flg指定なし
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]


def decompress(file_data):
    file_obj = io.BytesIO(file_data)
    with zipfile.ZipFile(file_obj, 'r') as zipf:
        file_name = zipf.namelist()[0]
        with zipf.open(file_name, 'r') as f:
            return f.read()


def test_1_get_s3_file_success(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    """
    正常系テスト
    S3ファイル取得が正常に完了することを確認
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    file_name = "test1.csv"
    compressed_data_expected = "a1,b1,c1\nd1,e1"

    # テスト実行
    file_data = glue_job.get_s3_file(input_file_dir, file_name)

    # 確認
    assert (
       file_data["Body"].read().decode("utf-8") == compressed_data_expected
    )


def test_2_get_s3_file_error(
    s3_bucket: None, glue_job: GlueJobFileCompress, capsys
):
    """
    異常系テスト
    S3ファイル取得が失敗（リトライ超過）することを確認
    """
    set_data_default(glue_job, 0)

    # テスト実行
    with patch.object(
        glue_job.s3_client, "get_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            # メイン処理実行
            params = get_params()
            glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "S3ファイル取得", "test1.csv")
    print(captured)


def test_3_get_s3_file_retry_success(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    file_name = "test1.csv"
    expected_Data = "a1,b1,c1\nd1,e1"

    # テスト実行
    response = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
    )
    with patch.object(
        glue_job.s3_client,
        "get_object",
        side_effect=[
            Exception("Test Exception"),
            Exception("Test Exception"),
            response,  # 3回目で成功
        ],
    ):
        file_data = glue_job.get_s3_file(input_file_dir, file_name)

    # 確認
    assert (
        file_data["Body"].read().decode("utf-8")
        == expected_Data
    )


def assert_error(captured, method_name: str, file_name: str):
    assert (
        "[INFO][JN_PR002-DD01_001][I_job_file_compress_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][JN_PR002-DD01_001][E_job_file_compress_002]処理で異常が発生しました。(処理名="
        + method_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][JN_PR002-DD01_001][E_job_file_compress_003]例外発生しました。"
        in captured.out
    )
    assert (
        "[ERROR][JN_PR002-DD01_001][E_job_file_compress_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[INFO][JN_PR002-DD01_001][I_job_file_compress_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )


def test_4_put_s3_file_success(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    """
    正常系テスト
    ファイル圧縮、S3ファイル配置が正常に完了することを確認
    """
    # テストデータ準備
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test.csv"
    output_file_name = "test.zip"
    expect_file_data = b"test\ndata"

    # ファイル取得
    file_data = glue_job.get_s3_file(
        input_file_dir, file_name
    )
    # ファイル圧縮
    compressed_data = glue_job.file_compress(
        file_name, file_data["Body"].read()
    )
    assert decompress(compressed_data) == expect_file_data

    try:
        # S3ファイル配置
        glue_job.put_s3_file(output_file_dir, output_file_name, compressed_data)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        )
        assert (
            decompress(response["Body"].read()) == expect_file_data
        )

    finally:
        pass
        # テストファイル削除
        # glue_job.s3_client.delete_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        # )


def test_4_put_s3_zerofile_success(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    """
    正常系テスト
    ファイル圧縮、S3ファイル配置が正常に完了することを確認
    """
    # テストデータ準備
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "testzero.csv"
    output_file_name = "test.zip"
    expect_file_data = b""

    # ファイル取得
    file_data = glue_job.get_s3_file(
        input_file_dir, file_name
    )
    # ファイル圧縮
    compressed_data = glue_job.file_compress(
        file_name, file_data["Body"].read()
    )
    assert decompress(compressed_data) == expect_file_data

    try:
        # S3ファイル配置
        glue_job.put_s3_file(output_file_dir, output_file_name, compressed_data)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        )
        assert (
            decompress(response["Body"].read()) == expect_file_data
        )

    finally:
        pass
        # テストファイル削除
        # glue_job.s3_client.delete_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        # )

def test_7_put_s3_file_error(
    s3_bucket: None, glue_job: GlueJobFileCompress, capsys
):
    """
    異常系テスト
    S3ファイル配置が失敗（リトライ超過）することを確認
    """
    set_data_default(glue_job, 0)

    # テスト実行
    with patch.object(
        glue_job.s3_client, "put_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            # メイン処理実行
            params = get_params()
            glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "S3ファイル配置", "test1.csv")
    print(captured)


def test_8_put_s3_file_retry_success(
    s3_bucket: None, glue_job: GlueJobFileCompress, capsys
):
    """
    準正常系テスト
    S3ファイル配置がリトライして成功することを確認
    """
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test.csv"
    output_file_name = "test.zip"
    input_file_data = b"test\ndata"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "put_object",
            side_effect=[
                Exception("Test Exception"),
                glue_job.s3_client.put_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=output_file_dir + output_file_name,
                    Body=glue_job.file_compress(file_name, input_file_data),
                ),  # 3回目で成功
            ],
        ):
            glue_job.put_s3_file(
                output_file_dir,
                file_name,
                glue_job.file_compress(file_name, input_file_data)
            )

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        )
        assert (
            decompress(response["Body"].read())
            == input_file_data
        )
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_PR002-DD01_001][I_job_file_compress_003]処理をリトライしました。(処理名=S3ファイル配置)"
            in captured.out
        )
        print(captured)

    finally:
        pass
        # テストファイル削除
        # glue_job.s3_client.delete_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        # )


def test_9_backup_input_file_success(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    """
    正常系テスト
    インプットファイルバックアップが正常に完了することを確認
    """
    # テストデータ準備
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    input_data_expected = "a1,b1,c1\nd1,e1"

    try:
        # S3ファイル配置
        glue_job.backup_input_file(input_file_dir, backup_file_dir, file_name)

        # S3ファイル取得
        file_data = glue_job.get_s3_file(input_file_dir, file_name)

        # ファイル圧縮
        compress_data = glue_job.file_compress(
            file_name,
            file_data["Body"].read()
        )

        # S3ファイル配置
        glue_job.put_s3_file(
            output_file_dir,
            output_file_name,
            compress_data
        )

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=os.path.join(backup_file_dir, file_name)
        )
        assert (
            response["Body"].read().decode("utf-8")
            == input_data_expected
        )
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=os.path.join(output_file_dir, output_file_name)
        )
        assert (
            decompress(response["Body"].read()).decode("utf-8")
            == input_data_expected
        )

    finally:
        pass
        # テストファイル削除
        # glue_job.s3_client.delete_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"], Key=os.path.join(backup_file_dir, file_name)
        # )


def test_9_backup_input_zerofile_success(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    """
    正常系テスト
    インプットファイルバックアップが正常に完了することを確認
    """
    # テストデータ準備
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "testzero.csv"
    output_file_name = "test0.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    input_data_expected = ""

    try:
        # S3ファイル配置
        glue_job.backup_input_file(input_file_dir, backup_file_dir, file_name)

        # S3ファイル取得
        file_data = glue_job.get_s3_file(input_file_dir, file_name)

        # ファイル圧縮
        compress_data = glue_job.file_compress(
            file_name,
            file_data["Body"].read()
        )

        # S3ファイル配置
        glue_job.put_s3_file(
            output_file_dir,
            output_file_name,
            compress_data
        )

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=os.path.join(backup_file_dir, file_name)
        )
        assert (
            response["Body"].read().decode("utf-8")
            == input_data_expected
        )
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=os.path.join(output_file_dir, output_file_name)
        )
        assert (
            decompress(response["Body"].read()).decode("utf-8")
            == input_data_expected
        )

    finally:
        pass
        # テストファイル削除
        # glue_job.s3_client.delete_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"], Key=os.path.join(backup_file_dir, file_name)
        # )


def test_9_13_main_backup_true(
    glue_job: GlueJobFileCompress,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグTrueで正常終了することを確認
    """
    execute(glue_job, 1)


def test_10_14_main_backup_false(
    glue_job: GlueJobFileCompress,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグFalseで正常終了することを確認
    """
    execute(glue_job, 0)


def test_11_backup_error(
    glue_job: GlueJobFileCompress, s3_bucket: None, capsys
):
    """
    異常系テスト
    バックアップで異常終了することを確認
    """
    set_data_default(glue_job, 1)
    with patch.object(
        glue_job.s3_client, "copy_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            params = get_params()
            glue_job.execute(params)

    # バックアップされていないことを確認
    with pytest.raises(Exception):
        glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=params["backup_file_dir"] + params["file_name"],
        )

    # S3ファイル配置したものを削除
    glue_job.s3_client.delete_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=params["output_file_dir"] + params["output_file_name"],
    )

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "インプットファイルバックアップ", "test1.csv")
    print(captured)


def test_12_backup_retry_success(
    s3_bucket: None, glue_job: GlueJobFileCompress, capsys
):
    """
    準正常系テスト
    インプットファイルバックアップがリトライして成功することを確認
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    file_name = "test2.csv"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    output_file_expected = "あ,い,う\r\nえ,髙"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "copy_object",
            side_effect=[
                Exception("Test Exception"),
                glue_job.s3_client.copy_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + file_name,
                    CopySource={
                        "Bucket": os.environ["S3_BUCKET_NAME"],
                        "Key": input_file_dir + file_name,
                    },
                ),  # 2回目で成功
            ],
        ):
            glue_job.backup_input_file(input_file_dir, backup_file_dir, file_name)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )
        assert (
            response["Body"].read().decode("ms932")
            == output_file_expected
        )
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_PR002-DD01_001][I_job_file_compress_003]処理をリトライしました。(処理名=インプットファイルバックアップ)"
            in captured.out
        )
        print(captured)
    finally:
        pass
        # テストファイル削除
        # glue_job.s3_client.delete_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        # )


def test_15_delete_error(
    glue_job: GlueJobFileCompress, s3_bucket: None, capsys
):
    """
    異常系テスト
    インプットファイル削除で異常終了することを確認
    """
    set_data_default(glue_job, 1)
    with patch.object(
        glue_job.s3_client, "delete_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            params = get_params()
            glue_job.execute(params)

    # インプットファイル削除されていないことを確認
    input_file_dir = params["input_file_dir"]
    file_name = params["file_name"]
    output_file_expected = "a1,b1,c1\nd1,e1"
    response = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=input_file_dir + file_name,
    )
    assert (
        response["Body"].read().decode("utf-8") == output_file_expected
    )

    # S3ファイル配置したものを削除
    glue_job.s3_client.delete_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=params["output_file_dir"] + file_name,
    )
    # バックアップしたファイルを削除
    glue_job.s3_client.delete_object(
        Bucket=os.environ["S3_BUCKET_NAME"], Key=params["backup_file_dir"] + file_name
    )

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "インプットファイル削除", "test1.csv")
    print(captured)


def test_16_delete_retry_success(
    s3_bucket: None, glue_job: GlueJobFileCompress, capsys
):
    """
    準正常系テスト
    インプットファイル削除がリトライして成功することを確認
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    file_name = "test1.csv"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "delete_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.delete_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=input_file_dir + file_name,
                ),  # 3回目で成功
            ],
        ):
            glue_job.delete_input_file(input_file_dir, file_name)

        # インプットファイル削除されたか確認
        with pytest.raises(Exception):
            glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
            )
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_PR002-DD01_001][I_job_file_compress_003]処理をリトライしました。(処理名=インプットファイル削除)"
            in captured.out
        )
        print(captured)
    finally:
        pass
        # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
        # glue_job.s3_client.copy_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"],
        #     Key=input_file_dir + file_name,
        #     CopySource={
        #         "Bucket": os.environ["S3_BUCKET_NAME"],
        #         "Key": "horiguchi/back-up/" + file_name,
        #     },
        # )


def test_17_main_backup_none(
    glue_job: GlueJobFileCompress,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグ指定なしで正常終了することを確認
    """
    # execute(glue_job, 2)
    execute(glue_job, 2, False)


def test_17_main_backup_none2(
    glue_job: GlueJobFileCompress,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグ指定なしで正常終了することを確認（main呼び出し）
    """
    set_data_default(glue_job, 2)
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"

    try:
        main()
        # バックアップされていないことを確認
        with pytest.raises(Exception):
            glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
    finally:
        pass
        # S3ファイル配置したものを削除
        # glue_job.s3_client.delete_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        # )
        # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
        # glue_job.s3_client.copy_object(
        #     Bucket=os.environ["S3_BUCKET_NAME"],
        #     Key=input_file_dir + file_name,
        #     CopySource={
        #         "Bucket": os.environ["S3_BUCKET_NAME"],
        #         "Key": "horiguchi/back-up/" + file_name,
        #     },
        # )


def execute(glue_job: GlueJobFileCompress, backup_flag_mode: int, delete_flag=True):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data_default(glue_job, backup_flag_mode)
    output_data_expected = "a1,b1,c1\nd1,e1"
    put_done = False
    backup_done = False
    input_file_dir = None
    file_name = None
    output_file_name = None
    backup_file_dir = None
    try:
        # メイン処理実行
        params = get_params()
        input_file_dir = params["input_file_dir"]
        output_file_dir = params["output_file_dir"]
        file_name = params["file_name"]
        output_file_name = params["output_file_name"]
        backup_file_dir = params["backup_file_dir"]
        backup_flag = params["backup_flag"]
        if backup_flag_mode == 1:
            assert backup_flag
        else:
            assert not backup_flag

        glue_job.execute(params)

        # S3ファイル配置されたか確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        )
        put_done = True
        assert (
            decompress(response["Body"].read()).decode("utf-8")
            == output_data_expected
        )

        if backup_flag:
            # バックアップされたか確認
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
            backup_done = True
            assert (
                response["Body"].read().decode("utf-8")
                == output_data_expected
            )
        else:
            # バックアップされていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
                )

        # インプットファイル削除されたか確認
        with pytest.raises(Exception):
            glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
            )
    finally:
        if delete_flag and put_done:
            # S3ファイル配置したものを削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
            )
        if delete_flag and backup_done:
            # バックアップしたファイルをインプットディレクトリに複製（次のテストで利用できるように）
            glue_job.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file_dir + file_name,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": backup_file_dir + file_name,
                },
            )
            # バックアップしたファイルを削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
        elif delete_flag:
            # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
            glue_job.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file_dir + file_name,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": "horiguchi/back-up/" + file_name,
                },
            )


def test_22_value_error_input_file_dir(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    # input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        # "input_file_dir",
        # input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'input_file_dir' is missing"
        print(exc_info.value)


def test_23_value_error_file_name(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    # file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        # "file_name",
        # file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'file_name' is missing"
        print(exc_info.value)


def test_24_value_error_output_file_dir(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    # output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        # "output_file_dir",
        # output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'output_file_dir' is missing"
        print(exc_info.value)


def test_25_value_error_output_file_name(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    # output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        # "output_file_name",
        # output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'output_file_name' is missing"
        print(exc_info.value)


def test_26_value_error_jobnet_id(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        # "jobnet_id",
        # glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'jobnet_id' is missing"
        print(exc_info.value)


def test_27_value_error_backup_file_dir(
    s3_bucket: None, glue_job: GlueJobFileCompress
):
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    # backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        # "backup_file_dir",
        # backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'backup_file_dir' is missing"
        print(exc_info.value)


def execute_file_change(glue_job: GlueJobFileCompress, indicate_file: str):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data_default(glue_job, 1)
    try:
        # メイン処理実行
        params = get_params()
        params["file_name"] = indicate_file
        input_file_dir = params["input_file_dir"]
        output_file_dir = params["output_file_dir"]
        file_name = indicate_file
        output_file_name = params["output_file_name"]
        backup_file_dir = params["backup_file_dir"]

        glue_job.execute(params)

        # S3ファイル配置されたか確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
        )
        assert (
            decompress(response["Body"].read())
        )
        # バックアップファイルがあるか確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )
        assert (
            response["Body"].read()
        )
        # インプットファイル削除されたか確認
        with pytest.raises(Exception):
            glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
            )
    finally:
        pass


def test_28_file_normal(
    glue_job: GlueJobFileCompress,
    s3_bucket: None,
):
    """
    正常系テスト
    ファイルをtest1.csvで実施
    """
    # execute(glue_job, 2)
    execute_file_change(glue_job, 'test1.csv')


def test_28_file_tsv(
    glue_job: GlueJobFileCompress,
    s3_bucket: None,
):
    """
    正常系テスト
    ファイルをtest_comp1.tsvで実施
    """
    # execute(glue_job, 2)
    execute_file_change(glue_job, 'test_comp1.tsv')

def test_28_file_sjiscsv(
    glue_job: GlueJobFileCompress,
    s3_bucket: None,
):
    """
    正常系テスト
    ファイルをtest_comp2.csvで実施
    """
    # execute(glue_job, 2)
    execute_file_change(glue_job, 'test_comp2.csv')

def test_check_param_backup_file_dir1(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」のときはTrueと扱うこと
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        "TRUE",
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == True
        assert params["backup_file_dir"] == backup_file_dir
    except ValueError:
        assert False

def test_check_param_backup_file_dir2(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「true」のときはTrueと扱うこと
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        "true",
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == True
        assert params["backup_file_dir"] == backup_file_dir
    except ValueError:
        assert False

def test_check_param_backup_file_dir3(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「FALSE」のときはFalseと扱うこと
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        "FALSE",
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == False
        assert params["backup_file_dir"] == backup_file_dir
    except ValueError:
        assert False

def test_check_param_backup_file_dir4(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグ、バックアップファイルディレクトリを指定しない場合、バックアップフラグはFalseと扱うこと
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == False
        assert not params.get("backup_file_dir")
    except ValueError:
        assert False

def test_check_param_backup_file_dir5_1(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」、かつバックアップファイルディレクトリを指定しない(Noneと同じ扱い)場合はエラーになること
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = "horiguchi/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        "TRUE",
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert False
    except ValueError as e:
        assert True
        assert str(e) == "Required parameter 'backup_file_dir' is missing"

def test_check_param_backup_file_dir5_2(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」、かつバックアップファイルディレクトリが空文字の場合はエラーになること
    """
    input_file_dir = "horiguchi/input-output/OMS_IN/"
    output_file_dir = "horiguchi/input-output/WMS_OUT/"
    file_name = "test1.csv"
    output_file_name = "test1.zip"
    backup_file_dir = ""
    sys.argv = [
        "glue_job_file_compress",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_name",
        output_file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        "TRUE",
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert False
    except ValueError as e:
        assert True
        assert str(e) == "Required parameter 'backup_file_dir' is missing"
