import json
import os
import pytest
import boto3
import sys
from moto import mock_secretsmanager
from unittest.mock import patch, MagicMock
from source.glue_job_get_file import GlueJobGetFile, get_params, main
from ftplib import FTP


@pytest.fixture
def s3_bucket():
    """テスト用バケット名を環境変数に設定"""
    os.environ["S3_BUCKET_NAME"] = "s3-dev-dlpf-if-886436956581"
    os.environ["S3_RETRY_LIMIT"] = "3"
    os.environ["S3_RETRY_INTERVAL"] = "1"
    yield
    if "S3_BUCKET_NAME" in os.environ:
        del os.environ["S3_BUCKET_NAME"]
    if "S3_RETRY_LIMIT" in os.environ:
        del os.environ["S3_RETRY_LIMIT"]
    if "S3_RETRY_INTERVAL" in os.environ:
        del os.environ["S3_RETRY_INTERVAL"]


@pytest.fixture
def glue_job():
    """GlueJobGetFileのインスタンスを返すフィクスチャ"""
    return GlueJobGetFile(jobnet_id="test-jobnet")


@pytest.fixture(autouse=True)
def mock_env_vars(monkeypatch):
    """S3_BUCKET_NAMEの環境変数をモック"""
    monkeypatch.setenv("S3_BUCKET_NAME", "test-bucket")
    monkeypatch.setenv("S3_RETRY_LIMIT", "3")
    monkeypatch.setenv("S3_RETRY_INTERVAL", "1")


@pytest.fixture
def mock_secrets():
    """Secrets Manager"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際の接続情報を設定
        secret_data = {
            "protocol": "https",
            "site_url": "https://example.sharepoint.com/sites/TestSite",
            "username": "<EMAIL>",
            "password": "securepassword",
        }
        secrets.create_secret(Name="test-secret", SecretString=json.dumps(secret_data))
        yield secrets


def set_data(glue_job, backup_flag: int):
    """
    テスト用データ・パラメータを初期設定
    Args:
        glue_job: ジョブ
    """
    source_secret_name = "test-secret"
    source_remote_file_full_path = "horiguchi/remote/path/to/test.csv"
    s3_storage_file_full_path = "horiguchi/input-output/CMS_IN/test.csv"
    backup_file_dir = "horiguchi/back-up/CMS_IN/"
    multi_file_mode = 0
    if backup_flag == 1:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "source_secret_name",
            source_secret_name,
            "source_remote_file_full_path",
            source_remote_file_full_path,
            "s3_storage_file_full_path",
            s3_storage_file_full_path,
            "backup_flag",
            True,
            "backup_file_dir",
            backup_file_dir,
            "multi_file_mode",
            multi_file_mode,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    elif backup_flag == 0:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "source_secret_name",
            source_secret_name,
            "source_remote_file_full_path",
            source_remote_file_full_path,
            "s3_storage_file_full_path",
            s3_storage_file_full_path,
            "backup_flag",
            False,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    else:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "source_secret_name",
            source_secret_name,
            "source_remote_file_full_path",
            source_remote_file_full_path,
            "s3_storage_file_full_path",
            s3_storage_file_full_path,
            # backup_flg指定なし
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]


def assert_error(captured, method_name: str, file_name: str):
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        + method_name
        + ")"
        in captured.out
    )
    assert "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" in captured.out
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )


def test_1_get_connection_info(mock_secrets, glue_job):
    """
    正常系テスト
    外部システム接続情報取得確認
    """
    # シークレット情報を取得する
    connection_info = glue_job.get_connection_info("test-secret")
    # 期待される結果と比較
    expect_data = {
        "protocol": "https",
        "site_url": "https://example.sharepoint.com/sites/TestSite",
        "username": "<EMAIL>",
        "password": "securepassword",
    }
    assert connection_info == expect_data


def test_2_get_connection_info_failure(mock_secrets, glue_job, capsys):
    """
    異常系テスト
    get_connection_infoが失敗することを確認し、ログを確認
    """
    # モックの設定: get_connection_infoメソッドで例外を発生させる
    with patch.object(
        glue_job.secrets_client,
        "get_secret_value",
        side_effect=Exception("Test Exception"),
    ):
        with pytest.raises(Exception):  # 例外がスローされることを確認
            # テスト対象メソッドの実行
            glue_job.get_connection_info("test")

        # 標準出力のキャプチャ
        captured = capsys.readouterr()

        # ログの確認
        expected_log_message = "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名=接続情報取得)"
        assert expected_log_message in captured.out


"""
def test_3_get_file_ftp(glue_job):
    # 接続情報を準備
    connection_info = {
        "protocol": "ftp",
        "host": "ftp.example.com",
        "username": "ftp_user",
        "password": "ftp_password",
    }
    source_remote_file_full_path = "/remote/path/to/file.txt"

    # モック用のファイル内容
    expected_file_content = b"Hello, world!"

    # メモリ上のバッファに書き込むための変数
    actual_file_content = bytearray()

    # ftplib.FTPをモック化
    with patch("source.glue_job_get_file.FTP") as MockFTP:
        mock_ftp_instance = MockFTP.return_value

        # retrbinaryの動作をモック
        def retrbinary_side_effect(cmd, callback):
            if cmd == f"RETR {source_remote_file_full_path}":
                stream = io.BytesIO(expected_file_content)
                while True:
                    block = stream.read(1024)
                    if not block:
                        break
                    callback(block)

        mock_ftp_instance.retrbinary.side_effect = retrbinary_side_effect

        # get_fileメソッドの呼び出し
        actual_file_content = glue_job.get_file(
            connection_info, source_remote_file_full_path
        )

        # # FTPメソッドの呼び出しを確認
        # mock_ftp_instance.login.assert_called_once_with(
        #     user=connection_info["username"], passwd=connection_info["password"]
        # )
        # mock_ftp_instance.retrbinary.assert_called_once_with(
        #     f"RETR {source_remote_file_full_path}",
        #     mock_ftp_instance.retrbinary.side_effect,
        # )
        # mock_ftp_instance.quit.assert_called_once()

        # ファイル内容の確認
        assert actual_file_content == expected_file_content
"""


def test_3_get_file_ftp(glue_job):
    """
    正常系テスト
    get_fileメソッドのFTP接続パターンのテスト
    """
    mock_ftp = MagicMock(spec=FTP)
    mock_file_content = b"Mocked file content"

    # retrbinaryが呼ばれたときに実行される関数を定義
    def retrbinary_side_effect(cmd, callback):
        callback(mock_file_content)

    # retrbinaryメソッドのサイドエフェクトを設定
    mock_ftp.retrbinary.side_effect = retrbinary_side_effect

    with patch("source.glue_job_get_file.FTP", return_value=mock_ftp):
        connection_info = {
            "protocol": "ftp",
            "host": "fake_host",
            "username": "fake_user",
            "password": "fake_pass",
        }
        source_remote_file_full_path = "fake_path"

        # テスト対象関数の呼び出し
        # デバッグモードでとめて、host, username, passwordが上記のものであるか確認する
        glue_job.get_file(connection_info, source_remote_file_full_path)


def test_4_get_file_ftp_failure(glue_job, capsys):
    """get_fileメソッドの失敗パターンのテスト"""

    set_data(glue_job, 2)

    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        mock_connection_info = {
            "protocol": "ftp",
            "host": "fake_host",
            "username": "fake_user",
            "password": "fake_pass",
        }
        with patch.object(
            GlueJobGetFile, "get_connection_info", return_value=mock_connection_info
        ):
            with pytest.raises(SystemExit):
                main()

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "FTPファイル取得", "file.txt")


def test_5_6_get_file_sftp_failure(glue_job, capsys):
    """get_fileメソッドの失敗パターンのテスト"""

    # ケース5については、SFTP通信直前でデバッグモードでとめて、想定通りの値であるか確認する

    set_data(glue_job, 2)

    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        mock_connection_info = {
            "protocol": "sftp",
            "host": "sftp.example.com",
            "username": "sftp_user",
            "password": "sftp_password",
            "private_key": "xxx",
        }
        with patch.object(
            GlueJobGetFile, "get_connection_info", return_value=mock_connection_info
        ):
            with pytest.raises(SystemExit):
                main()

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "SFTPファイル取得", "file.txt")


def test_7_8_get_file_https_failure(glue_job, capsys):
    """get_fileメソッドの失敗パターンのテスト"""

    # ケース8については、HTTPS通信直前でデバッグモードでとめて、想定通りの値であるか確認する

    set_data(glue_job, 2)

    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        mock_connection_info = {
            "protocol": "https",
            "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
            "username": "https_user",
            "password": "https_password",
            "private_key": "xxxyyy",
        }
        with patch.object(
            GlueJobGetFile, "get_connection_info", return_value=mock_connection_info
        ):
            with pytest.raises(SystemExit):
                main()

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "HTTPSファイル取得", "file.txt")


def test_9_put_s3_file_success(s3_bucket: None, glue_job):
    """
    正常系テスト
    S3ファイル配置が正常に完了することを確認
    """
    # テストデータ準備
    output_file_dir = "saito/input-output/CMS_OUT/"
    file_name = "test.csv"
    input_file_data = b"test\ndata"

    try:
        # S3ファイル配置
        glue_job.put_s3_file(output_file_dir + file_name, input_file_data)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert response["Body"].read() == input_file_data

    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


def test_10_put_s3_file_retry_success(s3_bucket: None, glue_job, capsys):
    """
    準正常系テスト
    S3ファイル配置がリトライして成功することを確認
    """
    output_file_dir = "saito/input-output/CMS_OUT/"
    file_name = "test.csv"
    input_file_data = b"test\ndata"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "put_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.put_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=output_file_dir + file_name,
                    Body=input_file_data,
                ),  # 3回目で成功
            ],
        ):
            glue_job.put_s3_file(output_file_dir + file_name, input_file_data)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert response["Body"].read() == input_file_data
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][test-jobnet][I_job_get_file_003]処理をリトライしました。(処理名=S3ファイル配置)"
            in captured.out
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


def test_11_put_s3_file_error(s3_bucket: None, glue_job, capsys):
    """
    異常系テスト
    S3ファイル配置が失敗（リトライ超過）することを確認
    """
    set_data(glue_job, 2)

    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        mock_connection_info = {
            "protocol": "https",
            "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
            "username": "https_user",
            "password": "https_password",
            "private_key": "xxxyyy",
        }
        with patch.object(
            GlueJobGetFile, "get_connection_info", return_value=mock_connection_info
        ):
            with patch.object(GlueJobGetFile, "get_file", return_value=b"test\ndata"):
                with patch.object(
                    glue_job.s3_client,
                    "put_object",
                    side_effect=Exception("Test Exception"),
                ):
                    with pytest.raises(SystemExit):
                        params = get_params()
                        glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "S3ファイル配置", "file.txt")


def test_12_16_17_main(s3_bucket: None, glue_job, capsys):
    # バックアップフラグTrueのときバックアップされるか確認
    execute_main(glue_job, 1, "ftp", 1)

    # ケース16では、デバッグモードでFTP通信直前でとめて、想定通りの変数値であるか確認する
    # ケース17のログ確認
    captured = capsys.readouterr()
    assert_error(captured, "FTPファイル削除", "file.txt")


def test_13_18_19_main(s3_bucket: None, glue_job, capsys):
    # バックアップフラグFalseのときバックアップされるか確認
    execute_main(glue_job, 0, "sftp", 1)

    # ケース18では、デバッグモードでSFTP通信直前でとめて、想定通りの変数値であるか確認する
    # ケース19のログ確認
    captured = capsys.readouterr()
    assert_error(captured, "SFTPファイル削除", "file.txt")


def test_20_21_22_main(s3_bucket: None, glue_job, capsys):
    # バックアップフラグFalseのときバックアップされるか確認
    execute_main(glue_job, 2, "https", 1)

    # ケース20では、デバッグモードでHTTPS通信直前でとめて、想定通りの変数値であるか確認する
    # ケース21のログ確認
    captured = capsys.readouterr()
    assert_error(captured, "HTTPSファイル削除", "file.txt")


def test_14_backup_input_file_retry_failure(s3_bucket: None, glue_job, capsys):
    set_data(glue_job, 1)
    with patch.object(
        glue_job.s3_client, "copy_object", side_effect=Exception("Test Exception")
    ):
        mock_connection_info = {
            "protocol": "https",
            "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
            "username": "https_user",
            "password": "https_password",
            "private_key": "xxxyyy",
        }
        with patch.object(
            GlueJobGetFile, "get_connection_info", return_value=mock_connection_info
        ):
            with patch.object(GlueJobGetFile, "get_file", return_value=b"test\ndata"):
                with pytest.raises(SystemExit):
                    params = get_params()
                    glue_job.execute(params)

    # バックアップされていないことを確認
    with pytest.raises(Exception):
        glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=params["backup_file_dir"] + params["file_name"],
        )

    # S3ファイル配置したものを削除
    glue_job.s3_client.delete_object(
        Bucket=os.environ["S3_BUCKET_NAME"], Key=params["s3_storage_file_full_path"]
    )

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "インプットファイルバックアップ", "file.txt")


def test_15_backup_input_file_retry_success(s3_bucket: None, glue_job, capsys):
    """
    準正常系テスト
    インプットファイルバックアップがリトライして成功することを確認
    """
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    byte_data = b"a1,b1,c1\nd1,e1"
    set_data(glue_job, 1)

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "copy_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.copy_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + file_name,
                    CopySource={
                        "Bucket": os.environ["S3_BUCKET_NAME"],
                        "Key": input_file_dir + file_name,
                    },
                ),  # 3回目で成功
            ],
        ):
            mock_connection_info = {
                "protocol": "https",
                "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
                "username": "https_user",
                "password": "https_password",
                "private_key": "xxxyyy",
            }
            with patch.object(
                GlueJobGetFile, "get_connection_info", return_value=mock_connection_info
            ):
                with patch.object(GlueJobGetFile, "get_file", return_value=byte_data):
                    glue_job.backup_input_file(
                        input_file_dir + file_name, backup_file_dir, file_name
                    )

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )
        assert response["Body"].read() == byte_data
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][test-jobnet][I_job_get_file_003]処理をリトライしました。(処理名=インプットファイルバックアップ)"
            in captured.out
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )


def test_23_main(s3_bucket: None, glue_job, capsys):
    with patch.object(GlueJobGetFile, "delete_input_file", return_value=None):
        execute_main(glue_job, 1, "ftp", 0)

    # ログ確認
    captured = capsys.readouterr()
    file_name = "test.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )


def execute_main(
    glue_job: GlueJobGetFile, backup_flag_mode: int, protocol: str, exit_mode: int
):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data(glue_job, backup_flag_mode)
    params = get_params()
    s3_storage_file_full_path = params["s3_storage_file_full_path"]
    file_name = os.path.basename(params["source_remote_file_full_path"])
    backup_file_dir = params["backup_file_dir"]
    byte_data = b"test\ndata"
    put_done = False
    backup_done = False

    try:
        # メイン処理実行
        backup_flag = params["backup_flag"]
        if backup_flag_mode == 1:
            assert backup_flag
        else:
            assert not backup_flag

        with patch("boto3.client") as mock_boto_client:
            mock_ssm_client = MagicMock()
            mock_ssm_client.get_parameter.return_value = {
                "Parameter": {
                    "Value": json.dumps(
                        {
                            "process": {"TZ": "Asia/Tokyo"},
                            "aws": {
                                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                                "REGION_NAME": "ap-northeast-1",
                                "S3_RETRY_LIMIT": "3",
                                "S3_RETRY_INTERVAL": "1",
                                "S3_RETRY_MODE": "standard",
                            },
                        }
                    )
                }
            }
            mock_boto_client.return_value = mock_ssm_client

            if protocol == "ftp":
                mock_connection_info = {
                    "protocol": "ftp",
                    "host": "fake_host",
                    "username": "fake_user",
                    "password": "fake_pass",
                }
            elif protocol == "sftp":
                mock_connection_info = {
                    "protocol": "sftp",
                    "host": "sftp.example.com",
                    "username": "sftp_user",
                    "password": "sftp_password",
                    "private_key": "xxx",
                }
            elif protocol == "https":
                mock_connection_info = {
                    "protocol": "https",
                    "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
                    "username": "https_user",
                    "password": "https_password",
                    "private_key": "xxxyyy",
                }

            with patch.object(
                glue_job,
                "get_connection_info",
                return_value=mock_connection_info,
            ):
                with patch.object(glue_job, "get_file", return_value=byte_data):
                    if exit_mode == 1:
                        with pytest.raises(SystemExit):
                            glue_job.execute(params)
                    else:
                        glue_job.execute(params)

        # S3ファイル配置されたか確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=s3_storage_file_full_path
        )
        put_done = True
        assert response["Body"].read() == byte_data

        if backup_flag:
            # バックアップされたか確認
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
            backup_done = True
            assert response["Body"].read() == byte_data
        else:
            # バックアップされていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
                )
    finally:
        if put_done:
            # S3ファイル配置したものを削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=s3_storage_file_full_path
            )
        if backup_done:
            # バックアップしたファイルを削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )


def test_24_value_error_source_secret_name(s3_bucket: None, glue_job):
    target = "source_secret_name"
    # source_secret_name = "test"
    source_remote_file_full_path = "/test"
    s3_storage_file_full_path = "saito/input-output/WMS_OUT/test.txt"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        # "source_secret_name",
        # source_secret_name,
        "source_remote_file_full_path",
        source_remote_file_full_path,
        "s3_storage_file_full_path",
        s3_storage_file_full_path,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter '" + target + "' is missing"


def test_25_value_error_source_remote_file_full_path(s3_bucket: None, glue_job):
    target = "source_remote_file_full_path"
    source_secret_name = "test"
    # source_remote_file_full_path = "/test"
    s3_storage_file_full_path = "saito/input-output/WMS_OUT/test.txt"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",
        source_secret_name,
        # "source_remote_file_full_path",
        # source_remote_file_full_path,
        "s3_storage_file_full_path",
        s3_storage_file_full_path,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter '" + target + "' is missing"


def test_26_value_error_s3_storage_file_full_path(s3_bucket: None, glue_job):
    target = "s3_storage_file_full_path"
    source_secret_name = "test"
    source_remote_file_full_path = "/test"
    # s3_storage_file_full_path = "saito/input-output/WMS_OUT/test.txt"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",
        source_secret_name,
        "source_remote_file_full_path",
        source_remote_file_full_path,
        # "s3_storage_file_full_path",
        # s3_storage_file_full_path,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter '" + target + "' is missing"


def test_27_value_error_backup_file_dir(s3_bucket: None, glue_job):
    target = "backup_file_dir"
    source_secret_name = "test"
    source_remote_file_full_path = "/test"
    s3_storage_file_full_path = "saito/input-output/WMS_OUT/test.txt"
    # backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",
        source_secret_name,
        "source_remote_file_full_path",
        source_remote_file_full_path,
        "s3_storage_file_full_path",
        s3_storage_file_full_path,
        # "backup_file_dir",
        # backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter '" + target + "' is missing"


def test_28_value_error_jobnet_id(s3_bucket: None, glue_job):
    target = "jobnet_id"
    source_secret_name = "test"
    source_remote_file_full_path = "/test"
    s3_storage_file_full_path = "saito/input-output/WMS_OUT/test.txt"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",
        source_secret_name,
        "source_remote_file_full_path",
        source_remote_file_full_path,
        "s3_storage_file_full_path",
        s3_storage_file_full_path,
        "backup_file_dir",
        backup_file_dir,
        # "jobnet_id",
        # glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter '" + target + "' is missing"


def execute_multi_main(
    glue_job: GlueJobGetFile,
    backup_flag_mode: int,
    protocol: str,
    file_name: str,
    multi_mode: int,
    expect_data: str,
    expect_backup_file_name: str
):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data(glue_job, backup_flag_mode)
    params = get_params()
    directory = os.path.dirname(params["source_remote_file_full_path"])
    params["source_remote_file_full_path"] = directory + "/" + file_name
    params["multi_file_mode"] = multi_mode
    s3_storage_file_full_path = params["s3_storage_file_full_path"]
    file_name = os.path.basename(params["source_remote_file_full_path"])
    backup_file_dir = params["backup_file_dir"]
    byte_data = expect_data.encode("utf-8")

    try:
        # メイン処理実行
        backup_flag = params["backup_flag"]
        if backup_flag_mode == 1:
            assert backup_flag
        else:
            assert not backup_flag

        with patch("boto3.client") as mock_boto_client:
            mock_ssm_client = MagicMock()
            mock_ssm_client.get_parameter.return_value = {
                "Parameter": {
                    "Value": json.dumps(
                        {
                            "process": {"TZ": "Asia/Tokyo"},
                            "aws": {
                                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                                "REGION_NAME": "ap-northeast-1",
                                "S3_RETRY_LIMIT": "3",
                                "S3_RETRY_INTERVAL": "1",
                                "S3_RETRY_MODE": "standard",
                            },
                        }
                    )
                }
            }
            mock_boto_client.return_value = mock_ssm_client

            if protocol == "ftp":
                mock_connection_info = {
                    "protocol": "ftp",
                    "host": "localhost",
                    "username": "localadmin",
                    "password": "localnimd@02",
                }
            elif protocol == "sftp":
                mock_connection_info = {
                    "protocol": "sftp",
                    "host": "localhost",
                    "username": "sftpuser",
                    "password": "P@ssw0rd0124",
                    "private_key": "xxx",
                }
            elif protocol == "https":
                mock_connection_info = {
                    "protocol": "https",
                    "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
                    "username": "https_user",
                    "password": "https_password",
                    "private_key": "xxxyyy",
                }

            with patch.object(
                glue_job,
                "get_connection_info",
                return_value=mock_connection_info,
            ):
                glue_job.execute(params)

        # S3ファイル配置されたか確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=s3_storage_file_full_path
        )
        assert response["Body"].read() == byte_data

        if backup_flag:
            # バックアップされたか確認
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + expect_backup_file_name
            )
            assert response["Body"].read() == byte_data
        else:
            # バックアップされていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + expect_backup_file_name
                )
    finally:
        pass


def test_29_ftp_mono(s3_bucket: None, glue_job, capsys):
    execute_multi_main(
        glue_job,
        1,
        "ftp",
        "test.csv",
        0,
        "テスト1,test2\r\ntesr3,テスト4\r\n",
        "test.csv"
    )

    # ログ確認
    captured = capsys.readouterr()
    file_name = "test.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_30_ftp_date_mono(s3_bucket: None, glue_job, capsys):
    execute_multi_main(
        glue_job,
        1,
        "ftp",
        "header_test*.csv",
        0,
        "header1,header2,header3,header4\r\n値1-1,値1-2,値1-3,値1-4\r\n値2-1,値2-2,値2-3,値2-4\r\n",
        "header_test20250227.csv"
    )
    # ログ確認
    captured = capsys.readouterr()
    file_name = "header_test*.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_31_ftp_no_header(s3_bucket: None, glue_job, capsys):
    execute_multi_main(
        glue_job,
        1,
        "ftp",
        "test*.csv",
        1,
        "テスト1,test2\r\ntesr3,テスト4\r\nテスト5,test6\r\ntesr7,テスト8\r\nテスト9,test10\r\ntesr11,テスト12,test13\r\n",
        "test20241231120000.csv"
    )
    # ログ確認
    captured = capsys.readouterr()
    file_name = "test*.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_32_ftp_header(s3_bucket: None, glue_job, capsys):
    execute_multi_main(
        glue_job,
        1,
        "ftp",
        "header_test*.csv",
        2,
        "header1,header2,header3,header4\r\n値1-1,値1-2,値1-3,値1-4\r\n値2-1,値2-2,値2-3,値2-4\r\n値3-1,値3-2,値3-3,値3-4\r\n値4-1,値4-2,値4-3,値4-4\r\n値5-1,値5-2,値5-3,値5-4\r\n値6-1,値6-2,値6-3,値6-4\r\n",
        "header_test20250227.csv"
    )
    # ログ確認
    captured = capsys.readouterr()
    file_name = "header_test*.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_33_sftp_mono(s3_bucket: None, glue_job, capsys):
    execute_multi_main(
        glue_job,
        1,
        "sftp",
        "test.csv",
        0,
        "テスト1,test2\r\ntesr3,テスト4\r\n",
        "test.csv"
    )

    # ログ確認
    captured = capsys.readouterr()
    file_name = "test.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_34_sftp_date_mono(s3_bucket: None, glue_job, capsys):
    execute_multi_main(
        glue_job,
        1,
        "sftp",
        "test*.csv",
        0,
        "テスト1,test2\r\ntesr3,テスト4\r\n",
        "test20241231120000.csv"
    )

    # ログ確認
    captured = capsys.readouterr()
    file_name = "test*.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_35_sftp_no_header(s3_bucket: None, glue_job, capsys):
    execute_multi_main(
        glue_job,
        1,
        "sftp",
        "test*.csv",
        1,
        "テスト1,test2\r\ntesr3,テスト4\r\nテスト5,test6\r\ntesr7,テスト8\r\nテスト9,test10\r\ntesr11,テスト12,test13\r\n",
        "test20241231120000.csv"
    )
    # ログ確認
    captured = capsys.readouterr()
    file_name = "test*.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_36_sftp_header(s3_bucket: None, glue_job, capsys):
    execute_multi_main(
        glue_job,
        1,
        "sftp",
        "header_test*.csv",
        2,
        "header1,header2,header3,header4\r\n値1-1,値1-2,値1-3,値1-4\r\n値2-1,値2-2,値2-3,値2-4\r\n値3-1,値3-2,値3-3,値3-4\r\n値4-1,値4-2,値4-3,値4-4\r\n値5-1,値5-2,値5-3,値5-4\r\n値6-1,値6-2,値6-3,値6-4\r\n",
        "header_test20250227.csv"
    )
    # ログ確認
    captured = capsys.readouterr()
    file_name = "header_test*.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_37_no_file(s3_bucket: None, glue_job, capsys):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data(glue_job, 1)
    params = get_params()

    try:
        # メイン処理実行
        with patch("boto3.client") as mock_boto_client:
            mock_ssm_client = MagicMock()
            mock_ssm_client.get_parameter.return_value = {
                "Parameter": {
                    "Value": json.dumps(
                        {
                            "process": {"TZ": "Asia/Tokyo"},
                            "aws": {
                                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                                "REGION_NAME": "ap-northeast-1",
                                "S3_RETRY_LIMIT": "3",
                                "S3_RETRY_INTERVAL": "1",
                                "S3_RETRY_MODE": "standard",
                            },
                        }
                    )
                }
            }
            mock_boto_client.return_value = mock_ssm_client
            mock_connection_info = {
                "protocol": "ftp",
                "host": "localhost",
                "username": "localadmin",
                "password": "localnimd@02",
            }

            with patch.object(
                glue_job,
                "get_connection_info",
                return_value=mock_connection_info,
            ):
                glue_job.execute(params)

    finally:
        pass

    # ログ確認
    captured = capsys.readouterr()
    file_name = "test.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_004]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def execute_multi_main_sjis(
    glue_job: GlueJobGetFile,
    backup_flag_mode: int,
    protocol: str,
    file_name: str,
    multi_mode: int,
    expect_data: str,
    expect_backup_file_name: str
):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data(glue_job, backup_flag_mode)
    params = get_params()
    directory = os.path.dirname(params["source_remote_file_full_path"])
    params["source_remote_file_full_path"] = directory + "/" + file_name
    params["multi_file_mode"] = multi_mode
    s3_storage_file_full_path = params["s3_storage_file_full_path"]
    file_name = os.path.basename(params["source_remote_file_full_path"])
    backup_file_dir = params["backup_file_dir"]
    byte_data = expect_data.encode("cp932")

    try:
        # メイン処理実行
        backup_flag = params["backup_flag"]
        if backup_flag_mode == 1:
            assert backup_flag
        else:
            assert not backup_flag

        with patch("boto3.client") as mock_boto_client:
            mock_ssm_client = MagicMock()
            mock_ssm_client.get_parameter.return_value = {
                "Parameter": {
                    "Value": json.dumps(
                        {
                            "process": {"TZ": "Asia/Tokyo"},
                            "aws": {
                                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                                "REGION_NAME": "ap-northeast-1",
                                "S3_RETRY_LIMIT": "3",
                                "S3_RETRY_INTERVAL": "1",
                                "S3_RETRY_MODE": "standard",
                            },
                        }
                    )
                }
            }
            mock_boto_client.return_value = mock_ssm_client

            if protocol == "ftp":
                mock_connection_info = {
                    "protocol": "ftp",
                    "host": "localhost",
                    "username": "localadmin",
                    "password": "localnimd@02",
                }
            elif protocol == "sftp":
                mock_connection_info = {
                    "protocol": "sftp",
                    "host": "localhost",
                    "username": "sftpuser",
                    "password": "P@ssw0rd0124",
                    "private_key": "xxx",
                }
            elif protocol == "https":
                mock_connection_info = {
                    "protocol": "https",
                    "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
                    "username": "https_user",
                    "password": "https_password",
                    "private_key": "xxxyyy",
                }

            with patch.object(
                glue_job,
                "get_connection_info",
                return_value=mock_connection_info,
            ):
                glue_job.execute(params)

        # S3ファイル配置されたか確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=s3_storage_file_full_path
        )
        assert response["Body"].read() == byte_data

        if backup_flag:
            # バックアップされたか確認
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + expect_backup_file_name
            )
            assert response["Body"].read() == byte_data
        else:
            # バックアップされていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + expect_backup_file_name
                )
    finally:
        pass


def test_38_ftp_zerofile(s3_bucket: None, glue_job, capsys):
    execute_multi_main_sjis(
        glue_job,
        1,
        "ftp",
        "test*.csv",
        0,
        "",
        "test20241130120000.csv"
    )
    # ログ確認
    captured = capsys.readouterr()
    file_name = "test*.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)


def test_39_ftp_sjisfile(s3_bucket: None, glue_job, capsys):
    execute_multi_main_sjis(
        glue_job,
        1,
        "ftp",
        "sjisfile*.csv",
        1,
        "テスト1,test2\ntesr3,テスト4\nテスト9,test10\ntesr11,テスト12,テスト①\n",
        "sjisfile20241231090000.csv"
    )
    # ログ確認
    captured = capsys.readouterr()
    file_name = "sjisfile*.csv"
    assert (
        "[INFO][test-jobnet][I_job_get_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_003]例外発生しました。" not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_get_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_get_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    print(captured)

def test_check_param_backup_file_dir1(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」のときはTrueと扱うこと
    """
    sys.argv = [
        "glue_job_get_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "source_remote_file_full_path",  # 連携元のファイルパス
        "folder/subfolder/file*.txt",
        "s3_storage_file_full_path",  # S3ストレージファイルパス
        "folder/subfolder/file.txt",
        "backup_flag",  # バックアップフラグ
        "TRUE",
        "backup_file_dir",  # バックアップファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == True
        assert params["backup_file_dir"] == "takahashi/input-output/CRM_IN/"
    except ValueError:
        assert False

def test_check_param_backup_file_dir2(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「true」のときはTrueと扱うこと
    """
    sys.argv = [
        "glue_job_get_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "source_remote_file_full_path",  # 連携元のファイルパス
        "folder/subfolder/file*.txt",
        "s3_storage_file_full_path",  # S3ストレージファイルパス
        "folder/subfolder/file.txt",
        "backup_flag",  # バックアップフラグ
        "true",
        "backup_file_dir",  # バックアップファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == True
        assert params["backup_file_dir"] == "takahashi/input-output/CRM_IN/"
    except ValueError:
        assert False

def test_check_param_backup_file_dir3(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「FALSE」のときはFalseと扱うこと
    """
    sys.argv = [
        "glue_job_get_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "source_remote_file_full_path",  # 連携元のファイルパス
        "folder/subfolder/file*.txt",
        "s3_storage_file_full_path",  # S3ストレージファイルパス
        "folder/subfolder/file.txt",
        "backup_flag",  # バックアップフラグ
        "FALSE",
        "backup_file_dir",  # バックアップファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == False
        assert params["backup_file_dir"] == "takahashi/input-output/CRM_IN/"
    except ValueError:
        assert False

def test_check_param_backup_file_dir4(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグ、バックアップファイルディレクトリを指定しない場合、バックアップフラグはFalseと扱うこと
    """
    sys.argv = [
        "glue_job_get_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "source_remote_file_full_path",  # 連携元のファイルパス
        "folder/subfolder/file*.txt",
        "s3_storage_file_full_path",  # S3ストレージファイルパス
        "folder/subfolder/file.txt",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == False
        assert not params.get("backup_file_dir")
    except ValueError:
        assert False

def test_check_param_backup_file_dir5_1(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」、かつバックアップファイルディレクトリを指定しない(Noneと同じ扱い)場合はエラーになること
    """
    sys.argv = [
        "glue_job_get_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "source_remote_file_full_path",  # 連携元のファイルパス
        "folder/subfolder/file*.txt",
        "s3_storage_file_full_path",  # S3ストレージファイルパス
        "folder/subfolder/file.txt",
        "backup_flag",  # バックアップフラグ
        "TRUE",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert False
    except ValueError as e:
        assert True
        assert str(e) == "Required parameter 'backup_file_dir' is missing"

def test_check_param_backup_file_dir5_2(
    s3_bucket: None, glue_job, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」、かつバックアップファイルディレクトリが空文字の場合はエラーになること
    """
    sys.argv = [
        "glue_job_get_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "source_secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "source_remote_file_full_path",  # 連携元のファイルパス
        "folder/subfolder/file*.txt",
        "s3_storage_file_full_path",  # S3ストレージファイルパス
        "folder/subfolder/file.txt",
        "backup_flag",  # バックアップフラグ
        "TRUE",
        "backup_file_dir",  # バックアップファイルディレクトリ
        "",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
    ]

    try:
        params = get_params()
        assert False
    except ValueError as e:
        assert True
        assert str(e) == "Required parameter 'backup_file_dir' is missing"
