#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys

# from unittest import mock
import boto3
from typing import Any
import pytest
from unittest.mock import patch, MagicMock
from source.glue_job_internal_db_import import GlueJobInternalDbImport, get_params, main


from source.common_util import get_resource_path
import json
from moto import mock_secretsmanager, mock_ssm


@pytest.fixture
def aws_credentials():
    """Mocked AWS Credentials for moto"""
    # os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    # os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    # os.environ["AWS_SECURITY_TOKEN"] = "testing"
    # os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "ap-northeast-1"


@pytest.fixture
def mock_secrets():
    """Secrets Managerのmock - 実際のDB接続情報を使用"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        db_secret = {
            # "username": "dlpf_batch",
            "username": "dlpf_ope",
            "password": "password",
            "host": "************",
            "port": "5432",
            "dbname": "test_db",
        }
        secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))
        yield secrets


@pytest.fixture
def mock_parameter_store():
    """Parameter Storeのmock"""
    with mock_ssm():
        ssm = boto3.client("ssm")
        # 環境変数設定のモック - 図の通りの設定
        env_config = {
            "process": {"TZ": "Asia/Tokyo"},
            "aws": {
                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                "S3_RETRY_LIMIT": "20",
                "S3_RETRY_INTERVAL": "1.0",
            },
        }
        ssm.put_parameter(
            Name="/glue/job/environment-config",
            Value=json.dumps(env_config),
            Type="String",
        )
        yield ssm


@pytest.fixture
def db_connector(mock_secrets: Any, glue_job: GlueJobInternalDbImport):
    glue_job.connect_db("test-db-secret")


@pytest.fixture
def glue_job():
    """GlueJobConvertCharacterEncodingのインスタンス生成"""
    return GlueJobInternalDbImport("JN-XXXXX-XXXX")


@pytest.fixture
def s3_bucket():
    """テスト用バケット名を環境変数に設定"""
    os.environ["S3_BUCKET_NAME"] = "s3-dev-dlpf-if-886436956581"
    os.environ["S3_RETRY_LIMIT"] = "3"
    os.environ["S3_RETRY_INTERVAL"] = "1"
    yield
    if "S3_BUCKET_NAME" in os.environ:
        del os.environ["S3_BUCKET_NAME"]
    if "S3_RETRY_LIMIT" in os.environ:
        del os.environ["S3_RETRY_LIMIT"]
    if "S3_RETRY_INTERVAL" in os.environ:
        del os.environ["S3_RETRY_INTERVAL"]


# No.1
# DB接続失敗
def test_1_db_conncet_failure(db_connector, glue_job: GlueJobInternalDbImport):
    """Mockを利用しDB接続情報取得を失敗させる"""

    try:
        glue_job.connect_db("test-db-error")
    except Exception:
        assert True


# DB接続成功
def test_2_db_conncet_success(
    monkeypatch,
    s3_bucket,
    db_connector,
    capsys,
    glue_job: GlueJobInternalDbImport,
):
    """Mockを利用しDB接続情報取得を失敗させる"""
    sql_dir = "sql"
    os.makedirs(sql_dir, exist_ok=True)

    with open(get_resource_path(sql_dir, "internal_db_test_upsert.sql"), "w") as f:
        f.write(
            """
        SELECT *
        FROM db_import_test
        """
        )
    with open(get_resource_path(sql_dir, "internal_db_test_upsert.sql")) as f:

        res = glue_job.db_connector.exec(sql_template=f.read())
        assert res


def test_3_backup_to_s3_failed(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_test_tmp_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        True,
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    params = get_params()

    input_character_encoding = "utf-8"
    input_file = "takahashi/input-output/CRM_IN/test.csv"
    backup_file = "takahashi/back-up/CRM_IN/test.csv"
    input_data_encode_expected = "aaa,data1,111\r\nbbb,data2,222"

    # テスト実行

    response = glue_job.s3_client.copy_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=backup_file,
        CopySource={
            "Bucket": os.environ["S3_BUCKET_NAME"],
            "Key": input_file,
        },
    )

    with patch.object(
        glue_job.s3_client,
        "copy_object",
        side_effect=[
            Exception("Test Exception"),
            Exception("Test Exception"),
            Exception("Test Exception"),
            response,  # 3回以上失敗
        ],
    ):
        try:
            if params["backup_flag"]:
                glue_job.backup_to_s3(input_file, backup_file)
                assert False
        except Exception as e:
            assert (
                "[E_job_internal_db_import_002]処理で異常が発生しました。(処理名=S3ファイルバックアップ)"
                in capsys.readouterr().out
            )
        finally:
            # テストファイル削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file
            )


def test_4_backup_to_s3_retry_success(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_test_tmp_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        True,
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    params = get_params()

    input_character_encoding = "utf-8"
    input_file = "takahashi/input-output/CRM_IN/test.csv"
    backup_file = "takahashi/back-up/CRM_IN/test.csv"
    input_data_encode_expected = "aaa,data1,111\r\nbbb,data2,222"

    # テスト実行

    response = glue_job.s3_client.copy_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=backup_file,
        CopySource={
            "Bucket": os.environ["S3_BUCKET_NAME"],
            "Key": input_file,
        },
    )

    with patch.object(
        glue_job.s3_client,
        "copy_object",
        side_effect=[
            Exception("Test Exception"),
            response,  # 2回目で成功
        ],
    ):
        try:
            if params["backup_flag"]:
                glue_job.backup_to_s3(input_file, backup_file)

            # 確認
            file_data = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file
            )
            assert (
                file_data["Body"].read().decode(input_character_encoding)
                == input_data_encode_expected
            )

        except Exception as e:
            assert False

        finally:
            # テストファイル削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file
            )


def test_5_backup_to_s3_success(s3_bucket, glue_job: GlueJobInternalDbImport):
    """
    正常系テスト
    S3ファイル取得が正常に完了することを確認
    """

    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_test_tmp_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        True,
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    params = get_params()

    input_character_encoding = "utf-8"
    input_file = "takahashi/input-output/CRM_IN/test.csv"
    backup_file = "takahashi/back-up/CRM_IN/test.csv"
    input_data_encode_expected = "aaa,data1,111\r\nbbb,data2,222"

    if params["backup_flag"]:
        try:
            # テスト実行
            glue_job.backup_to_s3(input_file, backup_file)

            # 確認
            file_data = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file
            )
            assert (
                file_data["Body"].read().decode(input_character_encoding)
                == input_data_encode_expected
            )
        finally:
            # テストファイル削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file
            )


def test_6_backup_to_s3_flag_false(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_test_tmp_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        False,
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    params = get_params()

    input_character_encoding = "utf-8"
    input_file = "takahashi/input-output/CRM_IN/test.csv"
    backup_file = "takahashi/back-up/CRM_IN/test.csv"
    input_data_encode_expected = "aaa,data1,111\r\nbbb,data2,222"

    # テスト実行
    try:
        response = glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=backup_file,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": input_file,
            },
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file
        )

    with patch.object(
        glue_job.s3_client,
        "copy_object",
        side_effect=[
            Exception("Test Exception"),
            Exception("Test Exception"),
            response,  # 3回目で成功
        ],
    ):
        try:
            if params["backup_flag"]:
                glue_job.backup_to_s3(input_file, backup_file)

            # 確認
            file_data = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file
            )

        except Exception as e:
            assert True


# DB UPSERT成功
def test_8_9_db_upsert_insert_success(s3_bucket, db_connector, capsys, glue_job):
    """Mockを利用しDB接続情報取得を失敗させる"""
    sql_dir = "sql"
    os.makedirs(sql_dir, exist_ok=True)

    with open(get_resource_path(sql_dir, "internal_db_test_upsert.sql"), "w") as f:
        f.write(
            """
        INSERT INTO db_import_test(
        test_key,
        test_vc,
        test_num,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
        )
        VALUES(
        'key1',
        'vc1',
        11,
        :user,
        CURRENT_TIMESTAMP,
        :user,
        CURRENT_TIMESTAMP,
        1
        )

        ON CONFLICT (test_key)
        DO
        UPDATE SET
        test_vc = 'updated_vc',
        test_num = 999,
        d_updated_user = :user,
        d_updated_datetime= CURRENT_TIMESTAMP,
        d_version=db_import_test.d_version + 1
        ;


        """
        )
    # with open(get_resource_path(sql_dir, "internal_db_test_upsert.sql")) as f2:
    glue_job.db_connector.begin()
    glue_job.execute_upsert(query_id="internal_db_test_upsert")
    glue_job.db_connector.commit()


# DB UPSERT失敗
def test_10_11_db_upsert_insert_failured(
    monkeypatch, s3_bucket, db_connector, capsys, glue_job
):
    """Mockを利用しDB接続情報取得を失敗させる"""
    sql_dir = "sql"
    os.makedirs(sql_dir, exist_ok=True)

    with open(get_resource_path(sql_dir, "internal_db_test_upsert.sql"), "w") as f:
        f.write(
            """
        INSERT INTO db_import_test(
        test_key,
        test_vc,
        test_num,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
        )
        VALUES(
        NULL,
        'vc1',
        11,
        'inserted_user',
        CURRENT_TIMESTAMP,
        'inserted_user',
        CURRENT_TIMESTAMP,
        1
        )

        ON CONFLICT (test_key)
        DO
        UPDATE SET
        test_vc = 'updated_vc',
        test_num = '111',
        d_updated_user = 'updated_user',
        d_updated_datetime= CURRENT_TIMESTAMP,
        d_version=db_import_test.d_version / 0
        ;


        """
        )

    with open(get_resource_path(sql_dir, "internal_db_test_upsert.sql")) as f2:
        try:
            glue_job.db_connector.begin()
            res = glue_job.db_connector.exec(sql_template=f2.read())
            glue_job.db_connector.commit()
            assert False
        except Exception:
            glue_job.db_connector.rollback()
            assert True


def test_12_delete_from_s3_failed(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    input_character_encoding = "utf-8"
    input_file = "takahashi/input-output/CRM_IN/test.tsv"
    input_file_stock = "takahashi/input-output/CRM_IN/test2.tsv"
    input_data_encode_expected = "aaa,data1,111\r\nbbb,data2,222"

    # テスト実行
    # 削除レスポンス取得
    response = glue_job.s3_client.delete_object(
        Bucket=os.environ.get("S3_BUCKET_NAME"),  # Backet
        Key=input_file,  # Key
    )

    with patch.object(
        glue_job.s3_client,
        "delete_object",
        side_effect=[
            Exception("Test Exception"),
            Exception("Test Exception"),
            Exception("Test Exception"),
            response,  # 3回以上失敗
        ],
    ):
        try:
            glue_job.delete_file(delete_file=input_file)
            assert False
        except Exception as e:
            assert (
                "[E_job_internal_db_import_002]処理で異常が発生しました。(処理名=インポートファイル削除)"
                in capsys.readouterr().out
            )
        finally:
            # テスト用ファイルを削除対象ファイルとして再配置
            glue_job.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": input_file_stock,
                },
            )


def test_13_delete_from_s3_success(s3_bucket, glue_job: GlueJobInternalDbImport):
    """
    正常系テスト
    S3ファイル取得が正常に完了することを確認
    """

    input_character_encoding = "utf-8"
    input_file = "takahashi/input-output/CRM_IN/test.tsv"
    # backup_file = "takahashi/back-up/CRM_IN/test.csv"
    input_file_stock = "takahashi/input-output/CRM_IN/test2.tsv"

    input_data_encode_expected = "aaa,data1,111\r\nbbb,data2,222"

    # テスト実行
    glue_job.delete_file(delete_file=input_file)

    try:
        # 確認
        file_data = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file
        )

    except Exception as e:
        assert True

    finally:
        # テスト用ファイルを削除対象ファイルとして再配置
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": input_file_stock,
            },
        )


def test_14_delete_from_s3_retry_success(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    input_character_encoding = "utf-8"
    input_file = "takahashi/input-output/CRM_IN/test.tsv"
    input_file_stock = "takahashi/input-output/CRM_IN/test2.tsv"
    input_data_encode_expected = "aaa,data1,111\r\nbbb,data2,222"

    # テスト実行
    # 削除レスポンス取得
    response = glue_job.s3_client.delete_object(
        Bucket=os.environ.get("S3_BUCKET_NAME"),  # Backet
        Key=input_file,  # Key
    )

    with patch.object(
        glue_job.s3_client,
        "delete_object",
        side_effect=[
            Exception("Test Exception"),
            response,  # 2回目で成功
        ],
    ):
        try:
            # 確認
            glue_job.delete_file(delete_file=input_file)
            assert True
        except Exception as e:
            assert False
        finally:
            # テスト用ファイルを削除対象ファイルとして再配置
            glue_job.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": input_file_stock,
                },
            )


# No.2,
def test_for_coverage_main_backup_flag_true(
    aws_credentials, monkeypatch, s3_bucket, glue_job, capsys
):
    """Mockを利用しDB接続情報取得を失敗させる"""

    input_file = "takahashi/input-output/CRM_IN/test.csv"
    input_file_stock = "takahashi/input-output/CRM_IN/test2.csv"
    # テスト用ファイルを削除対象ファイルとして再配置
    glue_job.s3_client.copy_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=input_file,
        CopySource={"Bucket": os.environ["S3_BUCKET_NAME"], "Key": input_file_stock},
    )
    try:
        mock_result = [("mock_r1c1", "mock_r1c2")]
        with patch("source.glue_job_internal_db_import.DbConnector") as mock_connector:
            # rdsのモック
            mock_connector = MagicMock()
            mock_connector.exec.return_value = mock_result
            # mock_sql.return_value = mock_result

            # initialize_env()
            sys.argv = [
                "glue_job_internal_db_import",
                "TZ",
                "Asia/Tokyo",
                "enable-job-insights",
                "false",
                "enable-glue-datacatalog",
                "true",
                "library-set",
                "analytics",
                "python-version",
                "3.9",
                "job-language",
                "python",
                "input_character_encoding",
                "aaa,data1,111\r\nbbb,data2,222",
                "input_format_options",  # フォーマットオプション
                {},
                "secret_name",  # Secrets Managerシークレット名
                "test-db-secret",
                "input_file_dir",  # インプットファイルディレクトリ
                "takahashi/input-output/CRM_IN/",
                "input_file_name",  # インプットファイル名
                "test.csv",
                "import_table",  # インポートテーブル名
                "db_import_test_tmp_work",  # インポートテーブル名
                "backup_flag",  # バックアップフラグ
                True,
                "backup_file_dir",  # バックアップディレクトリ
                "takahashi/back-up/CRM_IN/",
                "jobnet_id",  # ジョブネットID
                "job_internal_db_import",
                "query_upsert",  # UPSERTクエリ
                # "SELECT * from db_import_test",  # UPSERTクエリ
                "test",  # UPSERTクエリ
            ]

            params = get_params()

            glue_job = GlueJobInternalDbImport(jobnet_id=params["jobnet_id"])
            glue_job.execute(params=params)
            assert Exception

    finally:
        # テスト用ファイルを再配置
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": input_file_stock,
            },
        )


# No.2,
def test_for_coverage_main_system_exit(
    aws_credentials, monkeypatch, s3_bucket, glue_job, capsys
):
    """Mockを利用しDB接続情報取得を失敗させる"""

    input_file = "takahashi/input-output/CRM_IN/test.csv"
    input_file_stock = "takahashi/input-output/CRM_IN/test2.csv"
    # テスト用ファイルを削除対象ファイルとして再配置
    glue_job.s3_client.copy_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=input_file,
        CopySource={"Bucket": os.environ["S3_BUCKET_NAME"], "Key": input_file_stock},
    )
    try:
        mock_result = [("mock_r1c1", "mock_r1c2")]
        with patch("source.glue_job_internal_db_import.DbConnector") as mock_connector:
            # rdsのモック
            # mock_connector = MagicMock()
            # mock_connector.exec.return_value = mock_result

            # initialize_env()
            sys.argv = [
                "glue_job_internal_db_import",
                "TZ",
                "Asia/Tokyo",
                "enable-job-insights",
                "false",
                "enable-glue-datacatalog",
                "true",
                "library-set",
                "analytics",
                "python-version",
                "3.9",
                "job-language",
                "python",
                "input_character_encoding",
                "aaa,data1,111\r\nbbb,data2,222",
                "input_format_options",  # フォーマットオプション
                {},
                "secret_name",  # Secrets Managerシークレット名
                "test-db-secret",
                "input_file_dir",  # インプットファイルディレクトリ
                "Nothing",
                "input_file_name",  # インプットファイル名
                "test.csv",
                "import_table",  # インポートテーブル名
                "db_import_test_tmp_work",  # インポートテーブル名
                "backup_flag",  # バックアップフラグ
                True,
                "backup_file_dir",  # バックアップディレクトリ
                "takahashi/back-up/CRM_IN/",
                "jobnet_id",  # ジョブネットID
                "job_internal_db_import",
                "query_upsert",  # UPSERTクエリ
                # "SELECT * from db_import_test",  # UPSERTクエリ
                "test_select",  # UPSERTクエリ
            ]

            params = get_params()

            glue_job = GlueJobInternalDbImport(jobnet_id=params["jobnet_id"])
            glue_job.execute(params=params)
            assert False
    except Exception:
        assert (
            "[E_job_internal_db_import_001]ジョブが異常終了しました。(ファイル名=test.csv)"
            in capsys.readouterr().out
        )

    finally:
        # テスト用ファイルを再配置
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": input_file_stock,
            },
        )


def test_for_coverage_main_backup_flag_false(
    aws_credentials, monkeypatch, s3_bucket, glue_job, capsys
):
    """Mockを利用しDB接続情報取得を失敗させる"""

    input_file = "takahashi/input-output/CRM_IN/test.csv"
    input_file_stock = "takahashi/input-output/CRM_IN/test2.csv"
    # テスト用ファイルを削除対象ファイルとして再配置
    glue_job.s3_client.copy_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=input_file,
        CopySource={"Bucket": os.environ["S3_BUCKET_NAME"], "Key": input_file_stock},
    )
    try:
        mock_result = [("mock_r1c1", "mock_r1c2")]
        with patch("source.glue_job_internal_db_import.DbConnector") as mock_connector:
            # rdsのモック
            mock_connector = MagicMock()
            mock_connector.exec.return_value = mock_result
            # mock_sql.return_value = mock_result

            # initialize_env()
            sys.argv = [
                "glue_job_internal_db_import",
                "TZ",
                "Asia/Tokyo",
                "enable-job-insights",
                "false",
                "enable-glue-datacatalog",
                "true",
                "library-set",
                "analytics",
                "python-version",
                "3.9",
                "job-language",
                "python",
                "input_character_encoding",
                "aaa,data1,111\r\nbbb,data2,222",
                "input_format_options",  # フォーマットオプション
                {},
                "secret_name",  # Secrets Managerシークレット名
                "test-db-secret",
                "input_file_dir",  # インプットファイルディレクトリ
                "takahashi/input-output/CRM_IN/",
                "input_file_name",  # インプットファイル名
                "test.csv",
                "import_table",  # インポートテーブル名
                "db_import_test_tmp_work",  # インポートテーブル名
                "backup_flag",  # バックアップフラグ
                False,
                "backup_file_dir",  # バックアップディレクトリ
                "takahashi/back-up/CRM_IN/",
                "jobnet_id",  # ジョブネットID
                "job_internal_db_import",
                "query_upsert",  # UPSERTクエリ
                # "SELECT * from db_import_test",  # UPSERTクエリ
                "test",  # UPSERTクエリ
            ]

            params = get_params()

            glue_job = GlueJobInternalDbImport(jobnet_id=params["jobnet_id"])
            glue_job.execute(params=params)
            assert Exception

    finally:
        # テスト用ファイルを再配置
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": input_file_stock,
            },
        )


# DB接続成功
def test_for_coverage_exception_execute_import(
    s3_bucket, glue_job: GlueJobInternalDbImport, capsys
):
    """
    正常系テスト
    S3ファイル取得が正常に完了することを確認
    """

    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_test_tmp_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        True,
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    params = get_params()

    input_character_encoding = "utf-8"
    input_file = "takahashi/input-output/CRM_IN/test.csv"
    backup_file = "takahashi/back-up/CRM_IN/test.csv"
    input_data_encode_expected = "aaa,data1,111\r\nbbb,data2,222"

    if params["backup_flag"]:
        try:
            glue_job.execute_import()
        except:
            assert (
                "[E_job_internal_db_import_002]処理で異常が発生しました。(処理名=インポート実行)"
                in capsys.readouterr().out
            )


# DB UPSERT成功
def test_for_coverage_db_upsert_insert_success(
    s3_bucket, db_connector, capsys, glue_job
):
    """Mockを利用しDB接続情報取得を失敗させる"""

    try:
        glue_job.execute_upsert(query_id="test")
    except:
        assert (
            "[E_job_internal_db_import_002]処理で異常が発生しました。(処理名=クエリ実行)"
            in capsys.readouterr().out
        )


def test_for_coverage_main(aws_credentials, monkeypatch, s3_bucket, glue_job, capsys):
    """Mockを利用しDB接続情報取得を失敗させる"""

    input_file = "takahashi/input-output/CRM_IN/test.csv"
    input_file_stock = "takahashi/input-output/CRM_IN/test2.csv"
    # テスト用ファイルを削除対象ファイルとして再配置
    glue_job.s3_client.copy_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=input_file,
        CopySource={"Bucket": os.environ["S3_BUCKET_NAME"], "Key": input_file_stock},
    )
    try:
        main()

    except SystemExit as e:
        assert e.code == 1


def test_for_coverage_paramless(
    aws_credentials, monkeypatch, s3_bucket, glue_job, capsys
):
    """Mockを利用しDB接続情報取得を失敗させる"""

    input_file = "takahashi/input-output/CRM_IN/test.csv"
    input_file_stock = "takahashi/input-output/CRM_IN/test2.csv"

    mock_result = [("mock_r1c1", "mock_r1c2")]
    with patch("source.glue_job_internal_db_import.DbConnector") as mock_connector:
        # rdsのモック
        mock_connector = MagicMock()
        mock_connector.exec.return_value = mock_result
        # mock_sql.return_value = mock_result

        # initialize_env()
        sys.argv = [
            "glue_job_internal_db_import",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_character_encoding",
            "aaa,data1,111\r\nbbb,data2,222",
            "input_format_options",  # フォーマットオプション
            {},
            "secret_name",  # Secrets Managerシークレット名
            "test-db-secret",
            "input_file_dir",  # インプットファイルディレクトリ
            "takahashi/input-output/CRM_IN/",
            "input_file_name",  # インプットファイル名
            "test.csv",
            "import_table",  # インポートテーブル名
            "db_import_test_tmp_work",  # インポートテーブル名
            "backup_flag",  # バックアップフラグ
            True,
            # "backup_file_dir",  # バックアップディレクトリ
            # "takahashi/back-up/CRM_IN/",
            "jobnet_id",  # ジョブネットID
            "job_internal_db_import",
            "query_upsert",  # UPSERTクエリ
            # "SELECT * from db_import_test",  # UPSERTクエリ
            "test_select",  # UPSERTクエリ
        ]

        try:
            params = get_params()
        except ValueError as e:
            assert True

def test_check_param_backup_file_dir1(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」のときはTrueと扱うこと
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        "TRUE",
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == True
        assert params["backup_file_dir"] == "takahashi/input-output/CRM_IN/"
    except ValueError:
        assert False

def test_check_param_backup_file_dir2(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    入力チェック確認
    バックアップフラグが「true」のときはTrueと扱うこと
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        "true",
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == True
        assert params["backup_file_dir"] == "takahashi/input-output/CRM_IN/"
    except ValueError:
        assert False

def test_check_param_backup_file_dir3(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    入力チェック確認
    バックアップフラグが「FALSE」のときはFalseと扱うこと
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        "FALSE",
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == False
        assert params["backup_file_dir"] == "takahashi/input-output/CRM_IN/"
    except ValueError:
        assert False

def test_check_param_backup_file_dir4(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    入力チェック確認
    バックアップフラグ、バックアップディレクトリを指定しない場合、バックアップフラグはFalseと扱うこと
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_work",  # インポートテーブル名
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    try:
        params = get_params()
        assert params["backup_flag"] == False
        assert not params.get("backup_file_dir")
    except ValueError:
        assert False

def test_check_param_backup_file_dir5_1(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」、かつバックアップファイルディレクトリを指定しない(Noneと同じ扱い)場合はエラーになること
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        "TRUE",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    try:
        params = get_params()
        assert False
    except ValueError as e:
        assert True
        assert str(e) == "Required parameter 'backup_file_dir' is missing"

def test_check_param_backup_file_dir5_2(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    入力チェック確認
    バックアップフラグが「TRUE」、かつバックアップファイルディレクトリが空文字の場合はエラーになること
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_work",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        "TRUE",
        "backup_file_dir",  # バックアップディレクトリ
        "",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    try:
        params = get_params()
        assert False
    except ValueError as e:
        assert True
        assert str(e) == "Required parameter 'backup_file_dir' is missing"

def test_check_param_not_work_table(
    s3_bucket: None, glue_job: GlueJobInternalDbImport, capsys
):
    """
    入力チェック確認
    """
    sys.argv = [
        "glue_job_internal_db_import",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        "aaa,data1,111\r\nbbb,data2,222",
        "input_format_options",  # フォーマットオプション
        {},
        "secret_name",  # Secrets Managerシークレット名
        "test-db-secret",
        "input_file_dir",  # インプットファイルディレクトリ
        "takahashi/input-output/CRM_IN/",
        "input_file_name",  # インプットファイル名
        "test.csv",
        "import_table",  # インポートテーブル名
        "db_import_test",  # インポートテーブル名
        "backup_flag",  # バックアップフラグ
        True,
        "backup_file_dir",  # バックアップディレクトリ
        "takahashi/input-output/CRM_IN/",
        "jobnet_id",  # ジョブネットID
        glue_job.jobnet_id,
        "query_upsert",  # UPSERTクエリ
        "SELECT * from db_import_test",  # UPSERTクエリ
    ]

    try:
        params = get_params()
        assert False
    except ValueError:
        assert True
